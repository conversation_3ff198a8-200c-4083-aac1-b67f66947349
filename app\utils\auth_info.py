import jwt
from fastapi import Request

from app.config import SECRET_KEY, ALGORITHM


def token_info(token: str):
    """
    查询token中包含的所有信息
    :param token: 鉴权token
    :return:    {
                    "user_info": {                              用户信息
                                    "email": "string",          用户邮箱
                                    "id": int,                  用户id
                                    "ip": "string",             用户ip
                                    "nickname": "string",       用户昵称
                                    "org_id": int,              用户组织id
                                    "root": False,              用户是否是系统管理员
                                    "tel": "string",            用户电话
                                    "username": "string"        用户登录名
                                    "is_admin": True,           用户是否是企业管理员
                    }
                }
    """
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    return payload.get("sub")


def token_info_user(token: str = None):
    """
    查询token中的用户信息
    :param token: 鉴权token
    :return:    {
                    "email": "string",          用户邮箱
                    "id": int,                  用户id
                    "ip": "string",             用户ip
                    "nickname": "string",       用户昵称
                    "org_id": int,              用户组织id
                    "root": False,              用户是否是系统管理员
                    "tel": "string",            用户电话
                    "username": "string"        用户登录名
                    "is_admin": True,           用户是否是企业管理员
                }
    """
    info = token_info(token)
    return info.get("user_info", None)





def get_user_info(request: Request) -> tuple[int, int, int, bool, bool]:
    '''获取用户身份信息的注入函数

    Args:
        request (Request): 中间件返回的request请求

    Returns:
        tuple[int, int, bool, bool]: 返回用户id,组织id,是否是系统管理员,是否是企业管理员
    '''
    user_info = user_info_by_request(request)
    if not user_info:
        return None, None, None, None, None
    return int(user_info['id']), int(user_info['org_id']), \
        int(user_info['group_id']),\
        bool(user_info['root']), bool(user_info['is_admin'])

