from typing import Any, Dict
from app.services.flow import logger, now_ts
from app.services.flow.component.base import ComponentBase
from app.services.flow.namespace import FlowNamespace
from app.services.mcp.mcp import McpService

# TODO: 重试三次 改成装饰器


class McpToolWrapper(ComponentBase):
    function_call = "MCP服务的包装器"
    component_name = "mcp_tool_wrapper"
    desc = "对Mcp服务进行封装, 进而调用mcp服务"

    def __init__(
        self,
        namespace: FlowNamespace,
        mcp_service: McpService,
        tool_info: Dict[str, Any],
        base_url: str = "",
        **kwargs,
    ):
        super().__init__(namespace, **kwargs)
        self.mcp_service = mcp_service
        self.tool_info = tool_info
        self.base_url = base_url

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """调用mcp工具（新增context参数）"""
        start_ts = now_ts()
        question = step["question"]
        context = self.get_last_context(step_result_list)
        context_display = context[:200] if context is not None else ""
        log_msg = f"问题: {question}\n 上下文: {context_display}..."
        logger.info(log_msg)  # 日志显示上下文摘要
        yield self.namespace.add_log(log_msg, step)
        # 简单模拟搜索引擎查询
        # 将上下文与问题拼接（示例格式：上游结果\n当前问题）
        while attempt_count > 0:
            result = ""
            try:
                response = await self.mcp_service.execute_tool_agent_async(self.tool_info, self.base_url)
                log_msg = f"Mcp 工具的调用结果: {response}"
                yield self.namespace.add_log(log_msg, step)
                if response.get("error") is not None or response.get("isError"):
                    logger.info(f"Error: {response}")
                    attempt_count -= 1
                else:
                    # TODO: 格式处理
                    logger.info(f"搜索引擎查询结果: {response}")
                    content = response["result"]["content"]
                    result = ""
                    for row in content:
                        result += row.get("text", "") + "\n"
                    break
            except Exception as e:
                attempt_count -= 1
                logger.error(f"普通对话处理失败: {e}")
                result = "搜索答案时异常，请稍后再试。"

        formatted_input = f"{question}\n 已知的问题的上下文是: {context[:512]}..." if context else f"{question}"
        yield self.save_input_and_output(formatted_input, result, start_ts, step)
        yield result


if __name__ == "__main__":
    import json
    from app.services.mcp.mcp import McpService

    namespace = FlowNamespace()

    url = "https://mcp.higress.ai/mcp-time/cmb5yrsw100d08r01xajzzom6/sse".replace("/sse", "/rpc")
    tool_info = {"parameters": {"timeZone": "Asia/Shanghai"}, "tool": {"name": "get-current-time"}}
    mcp_tool = McpToolWrapper(namespace, McpService(), tool_info, url)
    step = {
        "step": "1",
        "question": "优化后的问题：请提供过去30天内中国市场主流品牌（如华为、小米、OPPO、vivo、苹果）智能手机的线上销量数据，按品牌和价格区间分类统计。",
        "call_function": "搜索引擎查询",
        "component_name": McpToolWrapper.component_name,
        "component_id": f"{McpToolWrapper.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = mcp_tool.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
