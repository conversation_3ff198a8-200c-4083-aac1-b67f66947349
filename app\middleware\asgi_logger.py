from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import Message
import time
import http
import json

from app.log import logger
from app.curd.ai_api_log import AiApiLogCurd


class AccessLoggerMiddleware(BaseHTTPMiddleware):
    
    def __init__(self, app):
        super().__init__(app)
        self.json_body = ''
    
    async def set_receive_hook(self, request: Request):  
        old_receive = request._receive

        async def receive() -> Message:
            receive_ = await old_receive()
            try:
                if receive_['type'] == 'http.request' and receive_['body']:
                    try:
                        self.json_body = json.loads(receive_['body'])
                    except Exception:
                        self.json_body = receive_['body'].decode()
            except Exception as e:
                logger.error(f"json load request body error: {str(e)}", exc_info=False)
            return receive_
        
        request._receive = receive

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        await self.set_receive_hook(request)
        start_time = time.time()
        response: Response = await call_next(request)
        process_time = "%.6f" % (time.time() - start_time)
        response.headers['X-Process-Time'] = process_time
        request_path = '/' + '/'.join(str(request.url).split('/')[3:])
        log_str = f'{request.client.host}:{request.client.port} - "{request.method} {request_path}" ' \
                  f'{response.status_code} {http.HTTPStatus(response.status_code).phrase} - {process_time}s'
        api_params = {}

        if request.query_params:
            log_str += f"\n[URL  params] {json.dumps(dict(request.query_params), ensure_ascii=False)}"
            api_params = dict(request.query_params)
        if self.json_body:
            log_str += f"\n[BODY params] {json.dumps(self.json_body, ensure_ascii=False)}"
            api_params = self.json_body
            self.json_body = ''
        # 增加用户日志记录
        if request_path =="/v1/health":
            return response
        try:
            item = {
                "client_ip": request.client.host,
                "api_path": request_path,
                "api_params": api_params,
                "cookie": dict(request.cookies),
                "header": dict(request.headers),
                "method": request.method,
                "process_time": process_time,
            }
            AiApiLogCurd.add_log(item)
        except Exception as e:
            logger.error(f"用户行为记录出错了:{e}")
        logger.info(log_str)
        return response
