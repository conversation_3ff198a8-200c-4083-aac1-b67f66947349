from datetime import datetime
from zoneinfo import ZoneInfo
from app.const.time import TIMEFORMAT, TIMEZONE


def get_now_timestr(zone: str=TIMEZONE, format: str=TIMEFORMAT):
    return datetime.now(tz=ZoneInfo(zone)).strftime(format)

def get_now_date(tz: str=TIMEZONE):
    return datetime.now(tz=ZoneInfo(tz)) if tz else datetime.now()


def date_replace_timezone(date: datetime, tz: str=TIMEZONE):
    return date.replace(tzinfo=ZoneInfo(tz)) if tz else date.replace(tzinfo=None)

def date_fromstr(timestr: str, tz: str=TIMEZONE):
    return datetime.strptime(timestr, TIMEFORMAT)\
        .replace(tzinfo=ZoneInfo(tz))

def date_tostr(date: datetime, tz: str=TIMEZONE):
    tzinfo = ZoneInfo(tz) if tz else None
    return date.astimezone(tz=tzinfo).strftime(TIMEFORMAT)
