import json
import asyncio
from docker.models.containers import Container as Docker<PERSON>ontainer
from fastapi import APIRouter, Query
from fastapi.responses import StreamingResponse

from app.api.deps import DockerClientDep
from app.const.response import MessageCode
from app.config.config import settings
from app.api.v1.client.schemas.container import ContainerRestartInput
from app.api.response import APIResponse
from app.services.container_service import ContainerService
from app.log import logger


router = APIRouter()


class ContainerApi(object):
    NAME_MAPPING = {s["name"]: s for s in settings.container.services}  # type: ignore

    @staticmethod
    @router.get("/list")
    def get_container_list(docker_client: DockerClientDep):
        """
        获取容器列表。
        容器的状态(status):
            created: 容器已创建但尚未启动。
            running: 容器正在运行。
            paused:容器已暂停，进程暂停但未终止。
            restarting: 容器正在重启。
            exited: 容器已停止运行。
            dead: 容器因错误无法启动或运行。
        """
        result = []
        try:
            containers: list[DockerContainer] = docker_client.containers.list(all=True)
            for c in containers:
                if ContainerApi.service_is_ignore(c.name):
                    continue
                if c.status == "running" and c.health == "starting":
                    status = "restarting"
                else:
                    status = c.status
                result.append(
                    {
                        "id": c.id,
                        "name": ContainerApi.get_service_alias_name(c.name),
                        "desc": ContainerApi.get_formatted_service_desc(c.name),
                        "server_default": ContainerApi.is_default_server(c.name),
                        "status": status,
                    }
                )
            # 按照名称排序
            result = sorted(result, key=lambda x: x["name"])
            return APIResponse(result)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/restart")
    async def restart_container(
        docker_client: DockerClientDep, req_data: ContainerRestartInput
    ):
        """
        根据名称列表重启容器服务
        :return:
        """

        async def _async_restart_container(container: DockerContainer):
            # 在独立线程中重启容器
            try:
                # 将同步的 container.restart() 放到线程中执行
                await asyncio.to_thread(container.restart)
                logger.info(f"Container {container.name} restarted successfully.")
            except Exception as e:
                logger.warning(f"Failed to restart container {container.name}: {e}")
        # 获取所有容器时改成异步执行(同步执行速度慢)
        containers: list[DockerContainer] = await asyncio.to_thread(docker_client.containers.list, all=True)
        container_mapping = {c.id: c for c in containers}

        for id in req_data.ids:
            if id not in container_mapping:
                return APIResponse(f"Invalid id: {id}", MessageCode.SYSTEMERR)
        result = []
        for id in req_data.ids:
            container = container_mapping[id]
            if ContainerApi.service_is_ignore(container.name):
                continue
            try:
                alias_name = ContainerApi.get_service_alias_name(container.name)
                # 重启中的容器忽略重启
                if not (container.status == "running" or container.status == "exited"):
                    return APIResponse(
                        f"status cannot restart: {alias_name}: {container.status}",
                        MessageCode.SYSTEMERR,
                    )
                # 异步重启
                asyncio.create_task(_async_restart_container(container))
                result.append({"id": container.id, "name": alias_name, "logs": "-"})
            except Exception as e:
                return APIResponse(
                    f"restart failed: {container.name}: {e}", MessageCode.SYSTEMERR
                )
        return APIResponse(result)

    @staticmethod
    @router.post("/reset")
    async def recreate_and_run_container(docker_client: DockerClientDep):
        """
        重新创建容器并重启容器。
        :return:
        """

        async def _async_recreate_and_run_container(container_name: str):
            # 在独立线程中重启容器
            try:
                # 将同步的函数放到线程中执行
                await asyncio.to_thread(ContainerService.recreate_and_run, docker_client, container_name)
                logger.info(f"Container recreate successfully: {container_name}")
            except Exception as e:
                logger.info(f"Failed to recreate container: {container_name}: {e}")

        result = []
        for name, _ in ContainerApi.NAME_MAPPING.items():
            if ContainerApi.service_can_reset(name):
                try:
                    alias_name = ContainerApi.get_service_alias_name(name)
                    # 异步重启
                    asyncio.create_task(_async_recreate_and_run_container(name))
                    result.append({"name": alias_name})
                except Exception as e:
                    return APIResponse(f"restart failed: {name}: {e}", MessageCode.SYSTEMERR)
        return APIResponse(result)

    @staticmethod
    @router.get("/log")
    async def get_container_log(
        docker_client: DockerClientDep,
        ids: list[str] = Query(..., description="容器ID列表"),
    ):
        """
        根据容器ID列表, 实时传输多个容器的日志。
        **注意: 目前只支持传递一个id, 即数组中只传递一个元素。
        """
        containers: list[DockerContainer] = await asyncio.to_thread(docker_client.containers.list, all=True)
        container_mapping = {c.id: c for c in containers}

        valid_containers = []
        for id in ids:
            container = container_mapping.get(id)
            if container is None:
                continue
            if ContainerApi.service_is_ignore(container.name):
                continue
            valid_containers.append(container)

        async def stream_logs():
            container = valid_containers[0]  # TODO: 只处理了第一个
            log_stream = container.logs(tail=20, stream=True, follow=True)
            while True:
                chunk = await asyncio.to_thread(next, log_stream)
                yield json.dumps(
                    {
                        "id": container.id,
                        "name": ContainerApi.get_service_alias_name(container.name),
                        "log": chunk.decode("utf-8"),
                    }
                ) + "\n"

        return StreamingResponse(stream_logs(), media_type="application/json")

    @staticmethod
    def get_service_alias_name(name):
        service = ContainerApi.NAME_MAPPING.get(name)
        return service["alias_name"] if service else name

    @staticmethod
    def get_formatted_service_desc(name):
        service = ContainerApi.NAME_MAPPING.get(name)
        return service["desc"] if service else "-"

    @staticmethod
    def service_is_ignore(name):
        service = ContainerApi.NAME_MAPPING.get(name)
        return service["ignore"] if service else False

    @staticmethod
    def is_default_server(name):
        service = ContainerApi.NAME_MAPPING.get(name)
        return service["server_default"] if service else False

    @staticmethod
    def service_can_reset(name):
        service = ContainerApi.NAME_MAPPING.get(name)
        return service["can_reset"] if service else False


import asyncio

if __name__ == "__main__":
    from app.api.deps import get_docker_client

    t = ContainerApi()

    container_id = "ec386c4a3f2c2dd51cc179f17f6aa61c5537296e85a64b8ee63a5c11d57258bc"

    async def main():
        try:
            # 手动传入 docker_client
            response = await t.get_container_log(
                docker_client=next(get_docker_client()), ids=[container_id]
            )
            print(response)
            if response:
                async for log in response.body_iterator:
                    print(log)
            else:
                print("No logs retrieved.")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
