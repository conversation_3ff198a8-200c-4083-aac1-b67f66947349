from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session, scoped_session
from typing import Generator
from app.config import DB_PATH,DB_PATH_MCP
from app.log import logger


# 创建数据库会话
engine = create_engine(
    DB_PATH,
    echo=False,
    echo_pool=True,
    pool_pre_ping=True,
    pool_recycle=3600 * 4,
    pool_size=100,
    max_overflow=50,
    pool_use_lifo=True,
    connect_args={"connect_timeout": 5},  # ← 连接超时时间（秒）
    pool_timeout=10,
)


engine_mcp = create_engine(
    DB_PATH_MCP,
    echo=False,
    echo_pool=True,
    pool_pre_ping=True,
    pool_recycle=3600 * 4,
    pool_size=100,
    max_overflow=50,
    pool_use_lifo=True,
    connect_args={"connect_timeout": 5},  # ← 连接超时时间（秒）
    pool_timeout=10,
)

# 创建数据库会话工厂
SessionLocal = scoped_session(sessionmaker(bind=engine))
SessionLocalMcp = scoped_session(sessionmaker(bind=engine_mcp))


def get_session():
    session = SessionLocal()

    class SessionContextManager:
        def __enter__(self):
            return session

        def __exit__(self, exc_type, exc_val, exc_tb):
            session.close()  # 释放内存,内部会调用expunge_all方法释放
            SessionLocal.remove()

    return SessionContextManager()


def get_mcp_session():
    session = SessionLocalMcp()

    class SessionContextManager:
        def __enter__(self):
            return session

        def __exit__(self, exc_type, exc_val, exc_tb):
            session.close()  # 释放内存,内部会调用expunge_all方法释放
            SessionLocal.remove()

    return SessionContextManager()
def get_db() -> Generator[Session, None, None]:
    '''获取数据库session生成器

    方便使用FastAPI的Depends依赖项进行管理

    用法示例：
    def upload_xxx(file: UploadFile, db: Session=Depends(get_db)):
        data = file.file.read()
        crud.add_file(db, file.filename, data)
        db.commit()

    Yields:
        Generator[Session, None, None]: 返回Session对象
    '''
    with get_session() as session:
        yield session

def get_mcp_db() -> Generator[Session, None, None]:
    '''获取数据库session生成器

    方便使用FastAPI的Depends依赖项进行管理

    用法示例：
    def upload_xxx(file: UploadFile, db: Session=Depends(get_db)):
        data = file.file.read()
        crud.add_file(db, file.filename, data)
        db.commit()

    Yields:
        Generator[Session, None, None]: 返回Session对象
    '''
    with get_mcp_session() as session:
        yield session



@event.listens_for(engine, "before_execute")
def log_sql_execution(conn, clauseelement, multiparams, params):
    if isinstance(clauseelement, str):
        # 对于文本查询，直接记录
        logger.sql_info(clauseelement)
    else:
        # 对于 ORM 查询，编译并记录完整的 SQL 语句
        compiled_query = clauseelement.compile(dialect=conn.dialect, compile_kwargs={"literal_binds": True})
        logger.sql_info(str(compiled_query))


# 设置每次连接建立后的锁等待时间
@event.listens_for(engine, "connect")
def on_connect(dbapi_connection, connection_record):
    with dbapi_connection.cursor() as cur:
        cur.execute("SET innodb_lock_wait_timeout = 10;")  # ← 等待锁最多 10 秒
