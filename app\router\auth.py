from fastapi.security import APIKeyHeader

# swagger ui请求需要的authorization
Api_Auth = APIKeyHeader(name='Authorization')

# 配置不需要鉴权的路由
AUTH_SKIP = [
    "/v1/user/login",
    "/api/v1/user_client/login",
    "/v1/user_client/login",
    "/docs",
    "/openapi.json",
    "/admin"
]


NOT_LOGGED_PATH = [
    "/api/v1/user/login",
    "/v1/user/login",
    "/api/v1/user_client/login",
    "/v1/user_client/login",
    "/docs",
    "/openapi.json",
    "/admin",
    "/static/index.html",
    "/"
]


# 配置登录后不需要鉴权的路由
LOGIN_AUTH_SKIP = [
    "/api/v1/sensitive/export",
    "/api/v1/single/session/history",
    "/api/v1/single/talk",
    "/api/v1/single/aimodel/list",
    "/api/v1/single/session/list",
    "/api/v1/single/session/auth",
    "/api/v1/single/session/history",
    "/api/v1/single/session/delete",
    "/api/v1/single/session/rename"
]