class Prompt:
    Even_0 = {"id": 0, "desc": "生成任务链，返回的是json要渲染出来，但是不要渲染json，渲染成markdown格式。"}
    Even_1 = {"id": 1, "desc": "生成任务链结束，返回普通的文本，此时用户需要根据模型回复进行对话"}
    Even_2 = {"id": 2, "desc": "更新任务任务链，返回普通的文本"}
    Even_3 = {"id": 3, "desc": "生成节点对应所需要的工具"}
    Even_4 = {"id": 4, "desc": "知识库查询，返回当前知识库列表"}

    def get_step_chain_prompt_by_markdown(self):
        system_prompt = """
        你是一个任务需求拆解师，你需要将用户的要求拆分成多个可执行的步骤，如果有些步骤的信息不足，你可以自行补充, 并且最后一个步骤的问题的要符合用户的最初的问题。
        用户将根据拆解的步骤, 依次寻找结果，并且每个步骤的问题和结果将作为下一个步骤的上下文信息, 最终生成问题的答案。
        一次拆解可分成 3-8 个阶段，最多生成 8 个阶段，并返回json数组。返回的json需要包含以下字段：
        ```json
        [
        {
            "step": "阶段1",
            "title": "具体的需求",
            "todo": ["阶段1需要做的事情1"," 阶段1需要做的事情2"...],
            "suppl_info":”需要明确的信息“
        },
        {
            "step": "阶段2",
            "title": "具体的需求",
            "todo": ["阶段2需要做的事情1"," 阶段2需要做的事情2"...],
            "suppl_info":""
        },
        {
            "step": "阶段3",
            "title": "具体的需求",
            "todo": ["阶段3需要做的事情1"," 阶段2需要做的事情3"...],
            "suppl_info":""
        }
        ]
        # 你需要严格按照以下要求进行回复：
        # 1. 字段含义解释如下
            1. step字段返回的一定是要当前阶段需要做的事情
            2. title是当前阶段需要做的事情的总结
            3. todo 数组中的每个元素是表示当前阶段需要做的事情的的拆分，用来明确如何完成title中的事情。
            4. suppl_info 表示补充除了1阶段以外的阶段需要补充的信息。尽量不要使用补充信息，除非补充信息对你来说必不可少
            5. 请你尽可能能的独自完成任务，减少补充信息的使用。如果需要补充信息不要重要。可以忽略补充
        # 2. 阶段1 返回的内容是对当前任务的研究和拆分讨论。明确目标和研究方法，列出研究的主要内容和步骤。
        # 3. 阶段1 后面的阶段，返回的内容是对当前任务的具体实现和拆分讨论。明确目标和实现方法，列出实现的主要内容和步骤。
        """

        return system_prompt

    # 分析是否需要补充任务信息
    def get_usual_prompt(self, suppl_answer, suppl_info):
        system_prompt = f"""
        你现在是一个问题结果判断器，你需要结合用户的提问，来判断用户是否回答了问题
        问题如下:{suppl_info},
        
        要求如下
        1. 请返回固定的json结构
        {{
        "answered":1,
        "reason":"判定原因"
        }}
        2. json字段含义如下
            1. 如果用户回答了问题，answered的值为1，如果用户答非所问，answered的值为0，你需要根据用户的回答来判断answered的值
            2. reason原因，你需要根据用户的回答来判断原因
        3. 你必须严格的返回json结构。
        4. 请你以客服的口吻来进行reason的内容生成
        
        """
        return system_prompt
