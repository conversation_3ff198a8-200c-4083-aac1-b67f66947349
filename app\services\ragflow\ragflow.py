import base64
import uuid

import aiohttp
from datetime import datetime
from Cryptodome.PublicKey import RSA
from Cryptodome.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
from fastapi import Request
from starlette.datastructures import UploadFile

from app.log import logger
from app.config.config import settings
from app.const.ragflow import RagFlowUrl
from app.utils.http import AsyncHTTPRequest


class RagFlowService:
    def __init__(self):
        self.api_base = settings.ragflow.api_base  # type: ignore
        self.token = settings.ragflow.token  # type: ignore
        self.user_id = settings.ragflow.user_id  # type: ignore
        self.dialog_id = settings.ragflow.dialog_id  # type: ignore
        self.public_key_path = RagFlowUrl.PUBLIC_KEY_PATH

        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }

    async def dataset_list(self, page, page_size, name, dataset_id):
        """知识库列表"""
        try:
            url = f"{self.api_base}{RagFlowUrl.DATASETS_LIST_URL}"
            payload = {
                "page": page,
                "page_size": page_size,
            }
            if name:
                payload["name"] = name

            if dataset_id:
                payload["id"] = dataset_id
            request = AsyncHTTPRequest("GET", url, headers=self.headers, params=payload)
            async for data in request():
                if data["code"] != 0:
                    if data["code"] == 102:
                        return []
                    raise Exception(data["message"])

                items = [
                    {
                        "id": item["id"],
                        "name": item["name"],
                        "desc": item["description"],
                        "embedding_model": item["embedding_model"],
                        "file_count": item["document_count"],
                        "create_time": datetime.fromtimestamp(
                            item["create_time"] / 1000
                        ).strftime("%Y-%m-%d %H:%M:%S"),
                        "avatar": item["avatar"],
                        "chunk_count": item["chunk_count"],
                        "create_date": item["create_date"],
                        "created_by": item["created_by"],
                        "language": item["language"],
                        "chunk_method": item["chunk_method"],
                        "parser_config": item["parser_config"],
                        "permission": item["permission"],
                        "similarity_threshold": item["similarity_threshold"],
                        "status": item["status"],
                        "tenant_id": item["tenant_id"],
                        "token_num": item["token_num"],
                        "update_date": item["update_date"],
                        "update_time": datetime.fromtimestamp(
                            item["update_time"] / 1000
                        ).strftime("%Y-%m-%d %H:%M:%S"),
                        "vector_similarity_weight": item["vector_similarity_weight"],
                    }
                    for item in data["data"]["items"]
                ]
                result = {"total": data["data"]["total"], "items": items}
                return result
        except Exception as e:
            errmsg = f"获取知识库列表出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def agent_summary(self, page, page_size, name, agent_id):
        """agent列表"""
        try:
            url = f"{self.api_base}{RagFlowUrl.AGENTS_LIST_SUMMARY_URL}"
            payload = {"page": page, "page_size": page_size}
            if name:
                payload["title"] = name

            if agent_id:
                payload["id"] = agent_id
            request = AsyncHTTPRequest("GET", url, headers=self.headers, params=payload)
            async for data in request():
                if data["code"] != 0:
                    if data["code"] == 102:
                        return []
                    raise Exception(data["message"])
                # return self.__parse_agent_list(data["data"]["items"])
                return {
                    "total": data["data"]["total"],
                    "items": self.__parse_agent_list(data["data"]["items"]),
                }
        except Exception as e:
            errmsg = f"获取agent列表出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    def __parse_agent_list(self, agent_list):
        return [
            {
                "id": item["id"],
                "name": item["title"],
                "desc": item["description"],
                "avatar": item["avatar"],
                "canvas_type": item["canvas_type"],
                "create_date": item["create_date"],
                "create_time": datetime.fromtimestamp(
                    item["create_time"] / 1000
                ).strftime("%Y-%m-%d %H:%M:%S"),
                "dsl": item["dsl"],
                "update_date": item["update_date"],
                "update_time": datetime.fromtimestamp(
                    item["update_time"] / 1000
                ).strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": item["user_id"],
            }
            for item in agent_list
        ]

    async def create_dataset_assistant(self, data):
        """创建知识库对话助手"""
        try:
            url = f"{self.api_base}{RagFlowUrl.DATASET_CHAT_ASSISTANT_URL}"
            if data.llm.model_name == "":
                data = data.copy(exclude={"llm": {"model_name"}})
            request = AsyncHTTPRequest(
                "POST", url, headers=self.headers, params=data.dict()
            )
            async for data in request():
                if data["code"] != 0:
                    raise Exception(data["message"])
                return {
                    "chat_id": data["data"]["id"],
                    "dataset_ids": data["data"]["dataset_ids"],
                }
        except Exception as e:
            errmsg = f"创建知识库对话助手出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def create_assistant_session(self, data):
        """注册接口"""
        try:
            params = data.dict()
            chat_id = params.pop("chat_id")
            url = f"{self.api_base}{RagFlowUrl.DATASET_CHAT_SESSION_URL.format(chat_id=chat_id)}"
            request = AsyncHTTPRequest("POST", url, headers=self.headers, params=params)
            async for resp in request():
                if resp["code"] != 0:
                    raise Exception(resp["message"])
                return resp["data"]
        except Exception as e:
            errmsg = f"创建知识库对话助手session出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def create_agent_session(self, request: Request):
        """创建AGENT会话"""
        try:
            # "multipart/form-data" or "application/json"
            headers = {**self.headers}
            headers.pop("Content-Type", None)
            async with aiohttp.ClientSession() as session:
                form_data = await request.form()
                agent_id = form_data.get("agent_id")
                if agent_id is None:
                    raise Exception("param: agent_id is not found")
                url = f"{self.api_base}{RagFlowUrl.AGENT_CHAT_SESSION_URL.format(agent_id=agent_id)}"
                if self.user_id:
                    url += f"?user_id={self.user_id}"
                logger.info(f"Url: {url}")
                form = aiohttp.FormData()
                for key, value in form_data.multi_items():
                    if isinstance(value, UploadFile):
                        form.add_field(
                            key, await value.read(), filename=value.filename, content_type=value.content_type
                        )
                    else:
                        form.add_field(key, value)
                async with session.post(url, data=form, headers=headers) as resp:
                    resp_data = await resp.json()

                if resp_data["code"] != 0:
                    raise Exception(resp_data["message"])
                else:
                    # http://**************:81/user-setting/api#create-session-with-agent
                    return resp_data["data"]
        except Exception as e:
            errmsg = f"创建AGENT会话出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def dataset_file_upload_and_parse(self, request: Request):
        """"""
        form_data = await request.form()
        try:
            # "multipart/form-data"
            headers = {**self.headers}
            headers.pop("Content-Type", None)
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_base}{RagFlowUrl.DATASET_UPLOAD_AND_PARSE_URL}"
                logger.info(f"Url: {url}")
                form = aiohttp.FormData()
                for key, value in form_data.multi_items():
                    if isinstance(value, UploadFile):
                        form.add_field(
                            key, await value.read(), filename=value.filename, content_type=value.content_type
                        )
                    else:
                        form.add_field(key, value)
                async with session.post(url, data=form, headers=headers) as resp:
                    resp_data = await resp.json()
                if resp_data["code"] != 0:
                    raise Exception(resp_data["message"])
                else:
                    return resp_data["data"]
        except Exception as e:
            errmsg = f"上传文件时出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def register(self, data):
        """注册接口"""
        try:
            data.password = self.encrypt_password(data.password)

            url = f"{self.api_base}{RagFlowUrl.REGISTER_URL}"
            request = AsyncHTTPRequest(
                "POST", url, headers=self.headers, params=data.dict()
            )
            async for resp in request():
                if resp["code"] != 0:
                    raise Exception(resp["message"])
        except Exception as e:
            errmsg = f"注册接口出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    def encrypt_password(self, password):
        """加密密码"""
        try:
            with open(self.public_key_path, "rb") as f:
                key_data = f.read()

            rsa_key = RSA.importKey(key_data, "Welcome")
            cipher = Cipher_pkcs1_v1_5.new(rsa_key)
            password_base64 = base64.b64encode(password.encode("utf-8")).decode("utf-8")
            encrypted_password = cipher.encrypt(password_base64.encode())
            return base64.b64encode(encrypted_password).decode("utf-8")
        except Exception as e:
            print(f"密码加密失败: {e}")
            return None

    async def login(self, data):
        """登录接口"""
        try:
            data.password = self.encrypt_password(data.password)
            url = f"{self.api_base}{RagFlowUrl.LOGIN_URL}"
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, headers=self.headers, json=data.dict()
                ) as response:
                    resp = await response.json()
                    if resp["code"] != 0:
                        raise Exception(resp["message"])
                    return {"body": resp["data"], "header": dict(response.headers)}
        except Exception as e:
            errmsg = f"登录接口出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def update_password(self, data):
        """更新密码接口"""
        try:
            data.password = self.encrypt_password(data.password)
            data.new_password = self.encrypt_password(data.new_password)
            login_url = f"{self.api_base}{RagFlowUrl.LOGIN_URL}"
            update_url = f"{self.api_base}{RagFlowUrl.UPDATE_PASSWORD_URL}"

            authorization = ""
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    login_url, json=data.dict(), headers=self.headers
                ) as resp:
                    resp_data = await resp.json()
                    if resp_data["code"] != 0:
                        raise Exception(resp_data["message"])
                    authorization = resp.headers.get("Authorization")

            new_headers = dict(**self.headers)
            new_headers["Authorization"] = authorization
            request = AsyncHTTPRequest(
                "POST", update_url, headers=new_headers, params=data.dict()
            )
            async for resp in request():
                if resp["code"] != 0:
                    raise Exception(resp["message"])
                return resp["data"]
        except Exception as e:
            errmsg = f"更新接口出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)

    async def generate_questions(self, data):
        """生成提问的问题接口"""
        try:
            url = f"{self.api_base}{RagFlowUrl.GENERATE_QUESTIONS_URL.format(chat_id=self.dialog_id)}"
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data.dict(), headers=self.headers) as resp:
                    resp_data = await resp.json()
                    if resp_data["code"] != 0:
                        raise Exception(resp_data["message"])
                    else:
                        return resp_data["data"]
        except Exception as e:
            errmsg = f"更新接口出错：{str(e)}"
            logger.error(errmsg)
            raise Exception(errmsg)
