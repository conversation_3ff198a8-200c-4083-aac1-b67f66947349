from enum import Enum

class CCKnowledgeBaseApi(str, Enum):
    def __str__(self):
        return self.value

    # 获取知识库列表
    LIST_KNOWLEDGE_BASES_URL = '/knowledge_base/list_knowledge_bases'
    # 创建知识库
    CREATE_KNOWLEDGE_BASE_URL = '/knowledge_base/create_knowledge_base'
    # 删除知识库
    DELETE_KNOWLEDGE_BASE_URL = '/knowledge_base/delete_knowledge_base'
    # 获取知识库内的文件列表
    LIST_FILES_URL = '/knowledge_base/list_files'
    # 搜索知识库
    SEARCH_DOCS_URL = '/knowledge_base/search_docs'
    # 上传文件到知识库，并/或进行量化
    UPLOAD_DOCS_URL = '/knowledge_base/upload_docs'
    # 删除知识库内指定文件
    DELETE_DOCS_URL = '/knowledge_base/delete_docs'
    # 更新知识库介绍
    UPDATE_INFO_URL = '/knowledge_base/update_info'
    # 更新现有文件到知识库
    UPDATE_DOCS_URL = '/knowledge_base/update_docs'
    # 下载知识库中的文件
    DOWNLOAD_DOCS_URL = '/knowledge_base/download_doc'
    # 根据content中文本重新计算向量，流式输出处理进度
    RECREATE_VECTOR_STORE_URL = '/knowledge_base/recreate_vector_store'
    # 上传文件到临时目录
    UPLOAD_TEMP_DOCS_URL = '/knowledge_base/upload_temp_docs'
    # 搜索临时知识库
    SEARCH_TEMP_DOCS_URL = '/knowledge_base/search_temp_docs'
    # 将单个知识库文件转化为向量存储
    SUMMARY_FILE_TO_VECTOR_STORE_URL = '/knowledge_base/kb_summary_api/summary_file_to_vector_store'
    # 将多个doc ids转化为vector store
    SUMMARY_DOC_IDS_TO_VECTOR_STORE_URL = '/knowledge_base/kb_summary_api/summary_doc_ids_to_vector_store'
    # 重建单个知识库的向量存储
    RECREATE_SUMMARY_VECTOR_STORE_URL = '/knowledge_base/kb_summary_api/recreate_summary_vector_store'

