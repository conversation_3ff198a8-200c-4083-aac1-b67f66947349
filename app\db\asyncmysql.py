from sqlalchemy.ext.asyncio import (
    create_async_engine, async_scoped_session, 
    async_sessionmaker, AsyncSession
)
from asyncio import current_task
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from app.config import ASYNC_DB_PATH


# 创建数据库引擎
async_engine = create_async_engine(
    ASYNC_DB_PATH, echo=False, 
    echo_pool=False, pool_pre_ping=True, 
    pool_recycle=3600*4, 
    pool_size=100, max_overflow=50,
    pool_use_lifo=True
)

# 创建会话工厂
async_session_factory = async_sessionmaker(
    bind=async_engine, expire_on_commit=False
)
AsyncScopedSession = async_scoped_session(
    async_session_factory, scopefunc=current_task
)


@asynccontextmanager
async def get_async_session():
    session: AsyncSession = AsyncScopedSession()
    try:
        yield session
    finally:
        await AsyncScopedSession.remove()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            try:
                await session.close()
            except Exception as e:
                pass
