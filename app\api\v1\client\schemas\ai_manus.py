from pydantic import BaseModel, Field, field_validator


class CreateTask(BaseModel):
    question: str = Field(description="任务提问内容")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class CreateTaskV2(BaseModel):
    question: str = Field(description="任务提问内容")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")
    event: int | None = Field(default=None, description="事件ID(当前版本-选填)")
    event_question: str | None = Field(default=None, description="event补充的描述")
    step_chain: str | None = Field(default=None, description="任务链")
    suppl_answer: str | None = Field(
        default=None, description="event补充的描述,创建任务的时候event等于1的时候返回提交的问题的回复"
    )
    suppl_info: str | None = Field(default=None, description="event补充的描述,创建任务的时候event等于1的时候返回内容")


class SupplTask(BaseModel):
    user_id: int | None = Field(default=0, description="用户ID(当前版本-选填)")
    event: int | None = Field(default=None, description="事件ID(当前版本-选填)")
    suppl_answer: str | None = Field(
        default=None, description="event补充的描述,创建任务的时候event等于1的时候返回提交的问题的回复"
    )
    suppl_info: str | None = Field(default=None, description="event补充的描述,创建任务的时候event等于1的时候返回内容")
    task_list: str | None = Field(default=None, description="任务链")
    question: str | None = Field(description="任务提问内容")

class CreateTaskManual(BaseModel):
    question: str = Field(description="任务提问内容")
    step_chain: list[dict] = Field(description="步骤链")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class RunTask(BaseModel):
    id: int = Field(description="任务的id")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class ListTask(BaseModel):
    status: int | None = Field(
        default=None, description="任务状态, 默认返回所有任务, 0-未开始, 1-进行中, 2-已完成, 3-已失败, 4-已取消"
    )
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class DeleteTask(BaseModel):
    id: int = Field(description="任务ID")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class UpdateStepChain(BaseModel):
    id: int = Field(description="任务ID")
    step_chain: list[dict] = Field(description="步骤链")
    user_id: int = Field(default=0, description="用户ID(当前版本-选填)")


class TaskContext(BaseModel):
    task_context: str = Field(default="", description='任务上下文内容')
