import logging
import colorlog
import sys
from logging.handlers import RotatingFileHandler
from datetime import datetime
from pathlib import Path
from app.config import settings


# 屏蔽debug日志
for logger_name in ('websockets', 'multipart', 'asyncio', 'sse_starlette'):
    if logger := logging.getLogger(logger_name):
        logger.setLevel(logging.INFO)


# 存放日志的路径
log_path = Path(settings.log.path)
log_path.mkdir(exist_ok=True)

log_colors_config = {
    # 终端输出日志颜色配置
    'DEBUG': 'cyan',
    'INFO': 'green',
    'WARNING': 'yellow',
    'ERROR': 'red',
    'CRITICAL': 'bold_red',
}

default_formats = {
    # 终端输出格式
    'color_format': '%(log_color)s[%(asctime)s %(levelname)8s]%(reset)s %(message)s',
    # 日志输出格式
    'log_format': '%(asctime)s-%(levelname)s-"%(pathname)s:%(lineno)d"-[日志信息]: %(message)s'
}


class SQLFilter(logging.Filter):
    def filter(self, record):
        # 检查日志消息，排除 BEGIN 和 COMMIT
        if record.msg.strip().upper().startswith(('BEGIN', 'COMMIT', 'ROLLBACK')):
            return False
        return True


class HandleLog:
    """
    先创建日志记录器(logging.getLogger),然后再设置日志级别(logger.setLevel),
    接着再创建日志文件,也就是日志保存的地方(logging.FileHandler),然后再设置日志格式(logging.Formatter),
    最后再将日志处理程序记录到记录器(addHandler)
    """

    def __init__(self):
        self.__now_time = datetime.now().strftime('%Y-%m-%d')  # 当前日期格式化
        self.__all_log_path = log_path.joinpath(self.__now_time + "-all" + ".log")  # 收集所有日志信息文件
        self.__error_log_path = log_path.joinpath(self.__now_time + "-error" + ".log")  # 收集错误日志信息文件
        self.__sql_log_path = log_path.joinpath(self.__now_time + "-sql" + ".log")  # 收集错误日志信息文件
        self.__logger = logging.getLogger('ai-client_server')  # 创建日志记录器
        self.__logger_sql = logging.getLogger('sqlalchemy')
        self.__logger_sql.setLevel(logging.WARNING)
        self.__logger_sql.propagate = False
        self.all_logger_handler = None
        self.error_logger_handler = None
        self.console_handle = None

        if not self.__logger.handlers:
            self.__logger.setLevel(settings.log.level)  # 设置默认日志记录器记录级别
            # """构造日志收集器"""
            self.console_handle = self.__init_console_handle()
            self.__set_color_formatter(self.console_handle, log_colors_config)
            self.__set_color_handle(self.console_handle)
            
            if settings.log.write_file:
                # 写入到文件中
                all_logger_handler = self.__init_logger_handler(self.__all_log_path)  # 创建日志文件
                error_logger_handler = self.__init_logger_handler(self.__error_log_path)
                sql_logger_handler = self.__init_logger_handler(self.__sql_log_path)
                self.__set_log_formatter(all_logger_handler)  # 设置日志格式
                self.__set_log_formatter(error_logger_handler)
                self.__set_log_formatter(sql_logger_handler)
                self.__set_sql_handler(sql_logger_handler)
                sql_logger_handler.addFilter(SQLFilter())

                self.__set_log_handler(all_logger_handler)  # 设置handler级别并添加到logger收集器
                self.__set_log_handler(error_logger_handler, level=logging.ERROR)
                self.all_logger_handler = all_logger_handler
                self.error_logger_handler = all_logger_handler
    
    def __del__(self):
        if self.all_logger_handler:
            self.__logger.removeHandler(self.all_logger_handler)  # 避免日志输出重复问题
            self.__close_handler(self.all_logger_handler)  # 关闭handler
        if self.error_logger_handler:
            self.__logger.removeHandler(self.error_logger_handler)
            self.__close_handler(self.error_logger_handler)
        if self.console_handle:
            self.__logger.removeHandler(self.console_handle)

    @staticmethod
    def __init_logger_handler(log_path):
        """
        创建日志记录器handler,用于收集日志
        :param log_path: 日志文件路径
        :return: 日志记录器
        """
        # 写入文件,如果文件超过1M大小时,切割日志文件,仅保留3个文件
        logger_handler = RotatingFileHandler(
            filename=log_path, encoding='utf-8',
            maxBytes=settings.log.maxsize, 
            backupCount=settings.log.maxfile
        )
        return logger_handler

    @staticmethod
    def __init_console_handle():
        """创建终端日志记录器handler,用于输出到控制台"""
        console_handle = colorlog.StreamHandler()
        return console_handle

    def __set_sql_handler(self, logger_handler, level=logging.DEBUG):
        """
        记录sql语句到单独的日志文件
        """
        self.__logger_sql.addHandler(logger_handler)

    def __set_log_handler(self, logger_handler, level=logging.DEBUG):
        """
        设置handler级别并添加到logger收集器
        :param logger_handler: 日志记录器
        :param level: 日志记录器级别
        """
        logger_handler.setLevel(level=level)
        self.__logger.addHandler(logger_handler)

    def __set_color_handle(self, console_handle):
        """
        设置handler级别并添加到终端logger收集器
        :param console_handle: 终端日志记录器
        :param level: 日志记录器级别
        """
        console_handle.setLevel(logging.DEBUG)
        self.__logger.addHandler(console_handle)

    @staticmethod
    def __set_color_formatter(console_handle, color_config):
        """
        设置输出格式-控制台
        :param console_handle: 终端日志记录器
        :param color_config: 控制台打印颜色配置信息
        :return:
        """
        formatter = colorlog.ColoredFormatter(
            default_formats["color_format"], 
            log_colors=color_config, 
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handle.setFormatter(formatter)

    @staticmethod
    def __set_log_formatter(file_handler):
        """
        设置日志输出格式-日志文件
        :param file_handler: 日志记录器
        """
        formatter = logging.Formatter('%(asctime)s-%(levelname)s-"%(lineno)d"-[日志信息]: %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)

    @staticmethod
    def __close_handler(file_handler):
        """
        关闭handler
        :param file_handler: 日志记录器
        """
        file_handler.close()

    def debug(self, message):
        self.__logger.debug(message, stacklevel=2)

    def info(self, message):
        self.__logger.info(message, stacklevel=2)

    def sql_info(self, message):
        self.__logger_sql.warning(message, stacklevel=2)

    def warning(self, message):
        self.__logger.warning(message, stacklevel=2)

    def error(self, message, exc_info: bool=True):
        if exc_info and sys.exc_info() != (None, None, None):
            self.__logger.error(message, exc_info=exc_info, stacklevel=2)
        else:
            self.__logger.error(message, stacklevel=2)
    
    def exception(self, message):
        self.__logger.exception(message, stacklevel=2)
    
    def critical(self, message):
        if sys.exc_info() != (None, None, None):
            self.__logger.critical(message, exc_info=True, stacklevel=2)
        else:
            self.__logger.critical(message, stacklevel=2)


logger = HandleLog()


if __name__ == '__main__':
    logger.debug("这是debug信息")
    logger.info("这是日志信息")
    logger.warning("这是警告信息")
    try:
        logger.error(f"这是错误日志信息1")
    except Exception as e:
        logger.error(f"这是错误日志信息2: {str(e)}")
    logger.critical("这是严重级别信息")