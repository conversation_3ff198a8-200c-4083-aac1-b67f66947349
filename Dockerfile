FROM --platform=amd64 python:3.11 as python3.11-base

RUN echo 'deb https://mirrors.huaweicloud.com/debian/ bookworm main non-free non-free-firmware contrib \n\
deb-src https://mirrors.huaweicloud.com/debian/ bookworm main non-free non-free-firmware contrib \n\
deb https://mirrors.huaweicloud.com/debian-security/ bookworm-security main \n\
deb-src https://mirrors.huaweicloud.com/debian-security/ bookworm-security main \n\
deb https://mirrors.huaweicloud.com/debian/ bookworm-updates main non-free non-free-firmware contrib \n\
deb-src https://mirrors.huaweicloud.com/debian/ bookworm-updates main non-free non-free-firmware contrib \n\
deb https://mirrors.huaweicloud.com/debian/ bookworm-backports main non-free non-free-firmware contrib \n\
deb-src https://mirrors.huaweicloud.com/debian/ bookworm-backports main non-free non-free-firmware contrib' > /etc/apt/sources.list && \
    mv /etc/apt/sources.list.d/debian.sources /etc/apt/sources.list.d/debian.sources.bak && \
    apt update && apt upgrade --fix-missing -y && \
    apt install gcc vim net-tools procps python3-dev default-libmysqlclient-dev \
        build-essential pkg-config flex bison curl wget busybox \
        mariadb-server mariadb-client mariadb-backup --fix-missing -y && \
    apt autoremove -y && apt clean && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    mkdir -p /root/.pip && echo '[global] \n\
index-url = https://pypi.tuna.tsinghua.edu.cn/simple \n\
trusted-host = pypi.tuna.tsinghua.edu.cn \n\
timeout = 6000' > /root/.pip/pip.conf


FROM --platform=amd64 python3.11-base as python3.11-ai-server
WORKDIR /root
COPY . .
RUN mv startup.sh /usr/local/bin/ && chmod +x /usr/local/bin/startup.sh


ENTRYPOINT [ "/usr/local/bin/startup.sh" ]
