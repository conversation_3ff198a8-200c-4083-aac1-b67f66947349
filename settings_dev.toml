# 配置文件

[service]
# 绑定地址
bind = "0.0.0.0"
port = 8081

[chatapi]
api = "http://**************:8090"

[mysql]
# host = "**************"
# port = 3306
# user = "root"
# passwd = "YXZC!myZXCD123L"
# dbname = 'yuanfang'
host    = "***************"
port    = 3306
user    = "root"
passwd  = "infini_rag_flow"
dbname  = 'yuanfang'

[minio]
# host = "ragflow:9000"
host =  '***************:9002'
user = 'rag_flow'
password =  'infini_rag_flow'


[log]
level = "INFO"
write_file = true
path = "logs"

# 日志轮转,限制日志大小,默认5*20M
maxsize = 20971520 # 20M
maxfile = 5


[auth]
# to generate a secret key run:
# (bash)$ openssl rand -hex 32
seckey = "592ddeabe23e06b55ca72bbb4650abe7943d6c814db70d9ee70dee12516b669f"
algorithm = "HS256"

[path]
icons = "icons"
file_path = "source/file/"

[chatchat]
api = "http://***************:7861"

[prometheus]
api = "http://***************:9090/"

[portainer]
api_base = "http://***************:9000/"
token = "ptr_f01uyq25NLkHBOYih/2Ps3vOsuOdgNjqzKELzcON0c8="


# [ragflow]
# #id = "32fe0d76fb3111efaf280242ac120006"
# # api_base = "http://***************:80"             # ragflow的api
# api_base = "http://localhost:9222"               # ragflow的api
# dialog_id = "ab623ae0fb4411efbe440242ac120006" # 用户的助手id
# token = "ragflow-U5ZGRkZjRhZmE4NjExZWZhOGVlMDI0Mm" # ragflow的apikey
# # token = "ragflow-c4NzRlZDM4MGUxNTExZjBhNDk3MDI0Mm"
# user_id = "595dd63afa3d11efbcfd0242ac120006"

[ragflow]
id = "32fe0d76fb3111efaf280242ac120006"
api_base = "http://localhost:9222"# ragflow的api
dialog_id = "ab623ae0fb4411efbe440242ac120006" # 用户的助手id
token = "ragflow-U5ZGRkZjRhZmE4NjExZWZhOGVlMDI0Mm       " # ragflow的apikey
user_id="595dd63afa3d11efbcfd0242ac120006"


[crypt]
key = "Integritytech_20250317 20:28:67 "
nonce = "id801vh9"

[redis]
host = "***************"
passwd = "infini_rag_flow"
db = 15
port = 6379

[container]
socket_path = "/var/run/docker.sock"
es_data_mount_path = "/minio/data"
es_data_mount_path_backup = "/minio/backup_data"
minio_data_mount_path = "/elasticsearch/data"
minio_data_mount_path_backup = "/elasticsearch/backup_data"
services = [
    { name = "nginx", alias_name = "Web反向代理", desc = "用来反向代理web服务", ignore = false, server_default = true },
    { name = "redis", alias_name = "缓存服务", desc = "Redis用来缓存数据及作为消息队列", ignore = false, server_default = false },
]

[license]
enable = false

[llm_manager]
socket_path = "/var/run/docker.sock"
# /home/<USER>/data/docker-compose.yaml
docker_compose_file = "/Users/<USER>/codebase/ai_agent_tools_server/docker-compose.vllm.yml"
# /home/<USER>/data/ragflow.env
docker_compose_env_file = "/Users/<USER>/codebase/ai_agent_tools_server/ragflow.env"
llm_list = [
    { id = "deepseek_r1", name = "DeepSeek R1", command = "--served-model-name deepseek-r1-distill-qwen --model  /data/models/DeepSeek-R1-Distill-Qwen-32B-quantized8  --tensor-parallel-size 2  --max-model-len 8192 --gpu_memory_utilization 0.9 --chat-template /data/qwen_deepseek_r1.jinja" },
    { id = "deepseek_r1", name = "DeepSeek R1", command = "--served-model-name deepseek-r1-distill-qwen --model  /data/models/DeepSeek-R1-Distill-Qwen-32B-quantized9  --tensor-parallel-size 2  --max-model-len 8192 --gpu_memory_utilization 0.9 --chat-template /data/qwen_deepseek_r1.jinja" },
    { id = "qwq32b", name = "千问", command = "--served-model-name   deepseek-r1-distill-qwen --model  /data/models/qwq32b --tensor-parallel-size 2  --max-model-len 8192  --gpu_memory_utilization 0.9 --chat-template /data/qwen_deepseek_r1.jinja" },
]

