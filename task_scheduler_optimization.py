"""
智能任务调度器优化方案
解决现有系统的调度问题，提供更准确的任务编排
"""

import asyncio
import json
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    BLOCKED = "blocked"

class DependencyType(Enum):
    """依赖类型"""
    DATA = "data"          # 数据依赖
    RESOURCE = "resource"  # 资源依赖
    SEQUENCE = "sequence"  # 顺序依赖

@dataclass
class TaskDependency:
    """任务依赖关系"""
    task_id: str
    dependency_type: DependencyType
    required_output: Optional[str] = None
    condition: Optional[str] = None

@dataclass
class TaskNode:
    """任务节点"""
    id: str
    step: int
    question: str
    call_function: str
    arguments: Dict[str, Any] = field(default_factory=dict)
    
    # 调度相关
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[TaskDependency] = field(default_factory=list)
    estimated_duration: Optional[int] = None  # 预估执行时间(秒)
    max_retries: int = 3
    retry_count: int = 0
    
    # 执行相关
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)

class IntelligentTaskScheduler:
    """智能任务调度器"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, TaskNode] = {}
        self.running_tasks: Set[str] = set()
        self.completed_tasks: Set[str] = set()
        self.failed_tasks: Set[str] = set()
        self.task_graph: Dict[str, Set[str]] = {}  # 依赖图
        
    def add_task(self, task: TaskNode) -> None:
        """添加任务到调度器"""
        self.tasks[task.id] = task
        self.task_graph[task.id] = set()
        
        # 构建依赖图
        for dep in task.dependencies:
            if dep.task_id not in self.task_graph:
                self.task_graph[dep.task_id] = set()
            self.task_graph[dep.task_id].add(task.id)
    
    def build_execution_plan(self) -> List[List[str]]:
        """构建执行计划 - 拓扑排序 + 并发优化"""
        # 拓扑排序
        in_degree = {task_id: 0 for task_id in self.tasks}
        for task_id in self.tasks:
            for dep in self.tasks[task_id].dependencies:
                if dep.task_id in in_degree:
                    in_degree[task_id] += 1
        
        # 分层执行计划
        execution_layers = []
        remaining_tasks = set(self.tasks.keys())
        
        while remaining_tasks:
            # 找到当前可执行的任务（入度为0）
            ready_tasks = [
                task_id for task_id in remaining_tasks 
                if in_degree[task_id] == 0
            ]
            
            if not ready_tasks:
                # 检测循环依赖
                logger.error(f"检测到循环依赖: {remaining_tasks}")
                break
            
            # 按优先级排序
            ready_tasks.sort(
                key=lambda x: (
                    -self.tasks[x].priority.value,  # 优先级高的先执行
                    self.tasks[x].estimated_duration or 0  # 短任务优先
                )
            )
            
            execution_layers.append(ready_tasks)
            
            # 更新入度
            for task_id in ready_tasks:
                remaining_tasks.remove(task_id)
                for dependent_id in self.task_graph.get(task_id, []):
                    if dependent_id in in_degree:
                        in_degree[dependent_id] -= 1
        
        return execution_layers
    
    def can_execute_task(self, task_id: str) -> bool:
        """检查任务是否可以执行"""
        task = self.tasks[task_id]
        
        # 检查依赖是否满足
        for dep in task.dependencies:
            dep_task = self.tasks.get(dep.task_id)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
            
            # 检查特定条件
            if dep.condition and not self._evaluate_condition(dep.condition, dep_task):
                return False
        
        return True
    
    def _evaluate_condition(self, condition: str, dep_task: TaskNode) -> bool:
        """评估依赖条件"""
        try:
            # 简单的条件评估，可以扩展为更复杂的表达式解析
            context = {
                'result': dep_task.result,
                'status': dep_task.status.value,
                'error': dep_task.error
            }
            return eval(condition, {"__builtins__": {}}, context)
        except Exception as e:
            logger.warning(f"条件评估失败: {condition}, 错误: {e}")
            return False
    
    async def execute_plan(self, execution_plan: List[List[str]]) -> Dict[str, Any]:
        """执行任务计划"""
        results = {}
        
        for layer_idx, layer_tasks in enumerate(execution_plan):
            logger.info(f"执行第 {layer_idx + 1} 层任务: {layer_tasks}")
            
            # 并发执行当前层的任务
            layer_results = await self._execute_layer(layer_tasks)
            results.update(layer_results)
            
            # 检查是否有失败的关键任务
            failed_critical = [
                task_id for task_id in layer_tasks
                if (self.tasks[task_id].status == TaskStatus.FAILED and 
                    self.tasks[task_id].priority == TaskPriority.CRITICAL)
            ]
            
            if failed_critical:
                logger.error(f"关键任务失败，停止执行: {failed_critical}")
                break
        
        return results
    
    async def _execute_layer(self, task_ids: List[str]) -> Dict[str, Any]:
        """并发执行一层任务"""
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        tasks = []
        
        for task_id in task_ids:
            if self.can_execute_task(task_id):
                task = asyncio.create_task(
                    self._execute_single_task(task_id, semaphore)
                )
                tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        layer_results = {}
        for i, result in enumerate(results):
            task_id = task_ids[i] if i < len(task_ids) else None
            if task_id:
                if isinstance(result, Exception):
                    logger.error(f"任务 {task_id} 执行异常: {result}")
                    self.tasks[task_id].status = TaskStatus.FAILED
                    self.tasks[task_id].error = str(result)
                else:
                    layer_results[task_id] = result
        
        return layer_results
    
    async def _execute_single_task(self, task_id: str, semaphore: asyncio.Semaphore) -> Any:
        """执行单个任务"""
        async with semaphore:
            task = self.tasks[task_id]
            task.status = TaskStatus.RUNNING
            task.start_time = datetime.now()
            self.running_tasks.add(task_id)
            
            try:
                # 构建上下文
                context = self._build_task_context(task_id)
                task.context = context
                
                # 执行任务（这里需要集成实际的工具执行逻辑）
                result = await self._call_tool(task, context)
                
                task.result = result
                task.status = TaskStatus.COMPLETED
                task.end_time = datetime.now()
                self.completed_tasks.add(task_id)
                
                logger.info(f"任务 {task_id} 执行成功")
                return result
                
            except Exception as e:
                task.error = str(e)
                task.retry_count += 1
                
                if task.retry_count < task.max_retries:
                    logger.warning(f"任务 {task_id} 执行失败，准备重试 ({task.retry_count}/{task.max_retries})")
                    task.status = TaskStatus.PENDING
                    # 可以添加延迟重试逻辑
                    await asyncio.sleep(2 ** task.retry_count)  # 指数退避
                    return await self._execute_single_task(task_id, semaphore)
                else:
                    logger.error(f"任务 {task_id} 达到最大重试次数，标记为失败")
                    task.status = TaskStatus.FAILED
                    self.failed_tasks.add(task_id)
                    raise e
            finally:
                self.running_tasks.discard(task_id)
    
    def _build_task_context(self, task_id: str) -> Dict[str, Any]:
        """构建任务执行上下文"""
        task = self.tasks[task_id]
        context = {
            'task_id': task_id,
            'question': task.question,
            'step': task.step,
            'dependencies': {}
        }
        
        # 收集依赖任务的结果
        for dep in task.dependencies:
            dep_task = self.tasks.get(dep.task_id)
            if dep_task and dep_task.status == TaskStatus.COMPLETED:
                context['dependencies'][dep.task_id] = {
                    'result': dep_task.result,
                    'question': dep_task.question,
                    'call_function': dep_task.call_function
                }
        
        return context
    
    async def _call_tool(self, task: TaskNode, context: Dict[str, Any]) -> Any:
        """调用工具执行任务（需要集成实际的工具调用逻辑）"""
        # 这里是占位符，需要集成实际的工具调用逻辑
        logger.info(f"执行工具: {task.call_function}, 参数: {task.arguments}")
        
        # 模拟执行时间
        if task.estimated_duration:
            await asyncio.sleep(min(task.estimated_duration, 10))  # 最多等待10秒
        else:
            await asyncio.sleep(1)
        
        return f"Task {task.id} completed with function {task.call_function}"

# 使用示例
def create_optimized_scheduler_example():
    """创建优化调度器的示例"""
    scheduler = IntelligentTaskScheduler(max_concurrent_tasks=3)
    
    # 创建任务节点
    task1 = TaskNode(
        id="task_1",
        step=1,
        question="搜索相关信息",
        call_function="web_search",
        priority=TaskPriority.HIGH,
        estimated_duration=5
    )
    
    task2 = TaskNode(
        id="task_2", 
        step=2,
        question="分析搜索结果",
        call_function="analyze_data",
        dependencies=[TaskDependency("task_1", DependencyType.DATA)],
        estimated_duration=3
    )
    
    task3 = TaskNode(
        id="task_3",
        step=3, 
        question="生成报告",
        call_function="generate_report",
        dependencies=[TaskDependency("task_2", DependencyType.DATA)],
        priority=TaskPriority.CRITICAL,
        estimated_duration=8
    )
    
    # 添加任务
    for task in [task1, task2, task3]:
        scheduler.add_task(task)
    
    return scheduler
