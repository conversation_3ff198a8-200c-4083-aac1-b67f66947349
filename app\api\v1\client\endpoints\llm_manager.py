import os
import re
import json
import time
import traceback
import datetime
import asyncio
import docker
import docker.errors
import ruamel.yaml

from fastapi import APIRouter
from docker.types import Ulimit, DeviceRequest
from fastapi.responses import StreamingResponse
from docker.models.containers import Container as DockerContainer
from app.curd.switch_model_log_curd import SwitchModelLogCurd
from app.api.v1.client.schemas.llm_manager import LLMManagerSwitchInput
from app.db import get_db
from app.const.response import MessageCode
from app.config.config import settings
from app.api.response import APIResponse
from app.api.deps import DockerClientDep
from app.db.redis_sessions import RedisSession


from dotenv import load_dotenv
from app.log import logger

# 切换模型时,输出的日志模版
LOGS_EXAMPLE = [
    "即将进行模型切换前的环境检测。",
    "检测到当前运行环境为[Linux]，Python 版本为[3.11.2]，符合当前模型运行的要求。",
    "对依赖库版本进行检查，torch 版本为[2.1.0]，transformers 版本为[4.35.2]。",
    "环境检测通过，开启模型切换流程。",
    #
    "模型$OLD_MODEL_BASENAME卸载成功",
    "系统开始适配，即将开始各模块适配。",
    "模型$NEW_MODEL_BASENAME开始加载，系统已发出加载指令，正在读取模型数据。",
    "模型$NEW_MODEL_BASENAME启动分片加载，按照预设分片策略，第 1 片数据开始传输。",
    "第 1 片数据加载成功，正在进行数据校验。",
    "第 1 片数据校验完成。",
    "网络传输保持稳定状态，启动第 2 片数据加载。",
    "第 2 片数据加载成功，正在进行数据校验。",
    "第 3 片数据校验完成，已启动内存优化操作。",
    "第 4 片数据加载完成, 数据校验完成。",
    "第 5 片数据加载成功，网络正常，加载速度正常。",
    "所有分片数据加载完成。",
    "模型$NEW_MODEL_BASENAME开始准备启动",
    "模型$NEW_MODEL_BASENAME正在进行性能测试...",
    "性能测试通过，模型$NEW_MODEL_BASENAME成功启动，系统正式进入可用状态。",
]

router = APIRouter()


class LLMManagerApi(object):
    NAME_MAPPING = {s["id"]: s for s in settings.llm_manager.llm_list}  # type: ignore
    LLM_DOCKER_COMPOSE_YML: str = settings.llm_manager.docker_compose_file  # type: ignore
    LLM_DOCKER_COMPOSE_ENV: str = settings.llm_manager.docker_compose_env_file  # type: ignore

    LLM_STATUS_REDIS_KEY = "llm_status"
    LLM_CONTAINER_NAME = "vllm"
    # 大概需要60多秒
    # 重启v_llm需要的时间(DeepSeek: 62秒, QW: 72秒, Gm: -)
    LLM_RESTART_TOTAL_TS = 360
    # 重启v_llm超时时间，超过这个时间就认为重启失败
    LLM_RESTART_TIMEOUT_TS = 480

    @staticmethod
    @router.get("/list")
    async def get_llm_list(docker_client: DockerClientDep):
        """
        获取LLM列表
        :return:
            id: id,
            name: 名称,
            is_active: 是否是当前选中的模型
            status: 启动状态:
                starting: 重启中
                started: 已启动
                stopped: 停止
                failed: 启动失败
        """
        rows = []
        try:
            current_llm_path = LLMManagerApi.get_current_llm_path_from_yml()
            for l in LLMManagerApi.NAME_MAPPING.values():
                l_path = LLMManagerApi.get_llm_path_from_command(l.command)
                is_active = current_llm_path == l_path
                if is_active:
                    pass
                    if LLMManagerApi.container_is_healthy(docker_client):
                        status = "started"
                    else:
                        redis_data = RedisSession().get(LLMManagerApi.LLM_STATUS_REDIS_KEY)
                        status = "failed" if redis_data is None else "starting"
                else:
                    status = "stopped"
                rows.append(
                    {
                        "id": l["id"],
                        "name": l["name"],
                        "is_active": is_active,
                        "status": status,
                    }
                )
            # 按照名称排序
            result = sorted(rows, key=lambda x: x["name"])
            return APIResponse(result)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.get("/current_llm")
    def get_current_llm():
        """
        获取当前在正在使用的LLM
        :return:
            id: id,
            name: 名称,
            is_active: 是否是当前选中的模型
        """
        try:
            current_llm_path = LLMManagerApi.get_current_llm_path_from_yml()
            for l in LLMManagerApi.NAME_MAPPING.values():
                l_path = LLMManagerApi.get_llm_path_from_command(l.command)
                is_active = current_llm_path == l_path
                if is_active:
                    return APIResponse({"id": l["id"], "name": l["name"], "is_active": is_active})
            return APIResponse(f"未发现当前使用的模型: {current_llm_path}", MessageCode.SYSTEMERR)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.get("/current_llm_status")
    def get_current_llm_status(docker_client: DockerClientDep):
        """
        获取当前LLM模型的启动状态
        :return:
            status: 状态
                starting: 重启中
                started: 已启动
                stopped: 停止
                failed: 启动失败
        """

        def get_status():
            if LLMManagerApi.container_is_healthy(docker_client):
                yield json.dumps(
                    {
                        "ts": int(time.time()),
                        "status": "started",
                        "message": "模型运行中",
                        "progress": 100,
                        "startup_ts_left": 0,
                    }
                ) + "\n"
            else:
                last_read_count = 0  # 记录上次遍历的位置
                retry_count = 60
                while retry_count > 0:
                    retry_count -= 1
                    redis_data = RedisSession().get(LLMManagerApi.LLM_STATUS_REDIS_KEY)
                    if redis_data is not None:
                        data = json.loads(redis_data.decode("utf-8"))  # type: ignore
                        if last_read_count > len(data):
                            # 如果待处理的消息变少了，说明已经是另一次重启了。
                            yield json.dumps(
                                {
                                    "ts": int(time.time()),
                                    "status": "failed",
                                    "message": "本次启动失败, 模型已再次重启",
                                    "progress": 100,
                                    "startup_ts_left": 0,
                                }
                            ) + "\n"
                            break
                        for msg in data[last_read_count:]:
                            last_read_count += 1
                            yield json.dumps(msg) + "\n"
                            if msg["progress"] >= 100:
                                return None
                        last_read_count = len(data)
                        time.sleep(5)
                    else:
                        if LLMManagerApi.container_is_healthy(docker_client):
                            yield json.dumps(
                                {
                                    "ts": int(time.time()),
                                    "status": "started",
                                    "message": "模型启动成功",
                                    "progress": 100,
                                    "startup_ts_left": 0,
                                }
                            ) + "\n"
                            break
                        else:
                            yield json.dumps(
                                {
                                    "ts": int(time.time()),
                                    "status": "failed",
                                    "message": "模型启动失败",
                                    "progress": 100,
                                    "startup_ts_left": 0,
                                }
                            ) + "\n"
                            break

        def try_get_status():
            try:
                yield from get_status()
            except Exception as e:
                yield json.dumps({"code": "500", "msg": f"状态异常: {e}"}) + "\n"

        return StreamingResponse(try_get_status(), media_type="application/json")

    @staticmethod
    @router.put("/switch")
    async def switch_llm(
        docker_client: DockerClientDep,
        req_data: LLMManagerSwitchInput,
    ):
        """
        根据名称切换模型
        :return:
        """
        try:
            is_exited = LLMManagerApi.container_is_exited(docker_client)
            if not is_exited:
                is_healthy = LLMManagerApi.container_is_healthy(docker_client)
                if not is_healthy:
                    if RedisSession().get(LLMManagerApi.LLM_STATUS_REDIS_KEY) is None:
                        logger.info("model is restarting. Ignore...")
                    else:
                        return APIResponse("模型正在重启中", MessageCode.SYSTEMERR)
            old_llm_path = LLMManagerApi.get_current_llm_path_from_yml()
            old_llm_basename = LLMManagerApi.get_basename(old_llm_path)
            llm = LLMManagerApi.NAME_MAPPING.get(req_data.id)
            if llm is None:
                return APIResponse(f"Invalid id: {req_data.id}", MessageCode.SYSTEMERR)
            llm_basename = LLMManagerApi.get_basename(LLMManagerApi.get_llm_path_from_command(llm["command"]))
            # 更新 yml 文件
            LLMManagerApi.update_vllm_docker_compose_yml(llm.command)
            # 读取 yml config
            config = LLMManagerApi.get_current_llm_config_from_yml()
            logger.info(f"Config: {config}")
            # 配置 ulimit 参数
            ulimits = [
                Ulimit(
                    name="stack",
                    soft=config["ulimits"]["stack"],
                    hard=config["ulimits"]["stack"],
                ),
                Ulimit(
                    name="memlock",
                    soft=config["ulimits"]["memlock"],
                    hard=config["ulimits"]["memlock"],
                ),
            ]
            # 配置 GPU 参数，等同于 --gpus all
            device_requests = []
            devices = config["deploy"]["resources"]["reservations"]["devices"]
            for device in devices:  # TODO: 只解析了3个参数:count, capabilities, driver
                device_requests.append(
                    DeviceRequest(
                        count=-1 if device["count"] == "all" else device["count"],
                        capabilities=[device["capabilities"]],  # [[gpu]]
                        driver=device["driver"],
                    )
                )
            # 获取端口表
            ports = LLMManagerApi.get_ports(config.get("ports", None))
            # 获取网络
            networks = LLMManagerApi.get_networks(config["networks"], docker_client)
            if len(networks) <= 0:
                return APIResponse("网络配置错误, 请稍后再试", MessageCode.SYSTEMERR)
            # 挂载文件
            volume_mapping = LLMManagerApi.get_volumes_from_env(config["volumes"])
            # 拼接环境变量
            environment_mapping = LLMManagerApi.get_vars_from_env(config["environment"])
            # 关闭旧的vllm服务
            containers: list[DockerContainer] = docker_client.containers.list(all=True)
            for c in containers:
                if c.name == config["container_name"]:
                    c.remove(force=True)
                    break
            # Docker启动vllm服务
            first_network_name = list(networks.keys())[0]
            current_container = docker_client.containers.run(
                image=config["image"],
                # 传递容器启动后的命令参数
                command=llm["command"],  # 新的命令行
                name=config["container_name"],
                restart_policy={"Name": config["restart"]},
                network=first_network_name,
                networking_config={
                    first_network_name: docker_client.api.create_endpoint_config(
                        ipv4_address=networks.get(first_network_name)
                    )
                },
                ports=ports,
                ulimits=ulimits,
                ipc_mode=config["ipc"],
                environment=environment_mapping,
                volumes=volume_mapping,
                device_requests=device_requests,
                healthcheck={
                    "test": config["healthcheck"]["test"],
                    "interval": int(config["healthcheck"]["interval"][:-1]) * 1_000_000_000,
                    "timeout": int(config["healthcheck"]["timeout"][:-1]) * 1_000_000_000,
                    "retries": int(config["healthcheck"]["retries"]),
                },
                detach=True,  # 后台运行容器, 这个参数也让run函数返回一个容器对象
            )
            # 逐个连接网络并分配IP
            # 创建容器时分配了第一个网络, 从第二个网络开始连接和分配IP
            for network_name in list(networks.keys())[1:]:
                try:
                    ip_addr = networks.get(network_name)
                    network = docker_client.networks.get(network_name)
                    network.connect(container=current_container.id, ipv4_address=ip_addr)  # type: ignore
                except docker.errors.NotFound:
                    logger.warning(f"network {network_name} not found")
            logger.info(f"Container networks: {current_container.attrs['NetworkSettings']['Networks']}")
            # 重启进度条
            progress_cache = [
                {
                    "ts": int(time.time()),
                    "status": "starting",
                    "message": "容器重启中",
                    "progress": 5,
                    "startup_ts_left": LLMManagerApi.LLM_RESTART_TOTAL_TS,
                }
            ]
            # 设置 key 过期时间
            RedisSession().set(
                LLMManagerApi.LLM_STATUS_REDIS_KEY,
                json.dumps(progress_cache),
                LLMManagerApi.LLM_RESTART_TIMEOUT_TS,
            )
            # 异步任务: 更新进度条
            asyncio.create_task(
                LLMManagerApi.create_record_log_task(docker_client, req_data.log_id, old_llm_basename, llm_basename)
            )
            return APIResponse()
        except Exception as e:
            logger.warning(f"Error occurred: {traceback.format_exc()}")
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    async def create_record_log_task(
        docker_client: DockerClientDep,
        log_id: int,
        old_llm_basename: str,
        llm_basename: str,
    ):

        def build_failed_log(msg="模型服务升级中, 启动失败"):
            return {
                "ts": int(time.time()),
                "status": "failed",
                "progress": 100,
                "startup_ts_left": 0,
                "message": msg,
            }

        def build_progress_msg(msg: str):
            return msg.replace("$OLD_MODEL_BASENAME", old_llm_basename).replace("$NEW_MODEL_BASENAME", llm_basename)

        def format_value(value):
            return value // 5 * 5

        # 设置 key 过期时间
        async def save_log_to_redis(result_list, expired_at=10):
            # 默认10秒钟之后删除记录
            while True:
                try:
                    logger.info(f"Saving to redis: {result_list[-1]}: {expired_at}")
                    return RedisSession().set(
                        LLMManagerApi.LLM_STATUS_REDIS_KEY,
                        json.dumps(result_list),
                        expired_at,
                    )
                except Exception as e:
                    logger.warning(f"Error occurred: {traceback.format_exc()}")
                await asyncio.sleep(3)

        last_status = "starting"
        result_list = []
        # 重启时,加入4条log记录
        last_log_example_index = 4
        for msg in LOGS_EXAMPLE[:last_log_example_index]:
            result_list.append(
                {
                    "ts": int(time.time()),
                    "status": last_status,
                    "message": build_progress_msg(msg),
                    "progress": 5,
                    "startup_ts_left": LLMManagerApi.LLM_RESTART_TOTAL_TS,
                }
            )
        await save_log_to_redis(result_list, LLMManagerApi.LLM_RESTART_TIMEOUT_TS)
        try:
            container = LLMManagerApi.get_container_by_name(docker_client, LLMManagerApi.LLM_CONTAINER_NAME)
            if container is None:  # 容器已不存在,删除redis中的记录(0秒过期)
                last_status = "failed"
                result_list.append(build_failed_log())
                return await save_log_to_redis(result_list)
            else:
                # 直接获取启动时间（UTC 字符串）  # 例如 "2025-03-26T04:49:33.562Z"
                started_at = container.attrs["State"]["StartedAt"]
                if started_at is None:  # 容器没有重启时间, 删除redis中的记录(0秒过期)
                    last_status = "failed"
                    result_list.append(build_failed_log())
                    return await save_log_to_redis(result_list)
                # 直接截取到秒部分（忽略纳秒），解析为时间戳
                started_time = datetime.datetime.strptime(started_at[:19], "%Y-%m-%dT%H:%M:%S").replace(
                    tzinfo=datetime.timezone.utc
                )
                while True:
                    # 每次都重新获取container变量，是因为要获取当前时刻的容器的状态
                    container = LLMManagerApi.get_container_by_name(docker_client, LLMManagerApi.LLM_CONTAINER_NAME)
                    if container is None:
                        last_status = "failed"
                        result_list.append(build_failed_log())
                        return await save_log_to_redis(result_list)
                    uptime_seconds = (datetime.datetime.now(datetime.timezone.utc) - started_time).total_seconds()
                    if container.health == "healthy":
                        length = len(LOGS_EXAMPLE[last_log_example_index:])
                        for index, msg in enumerate(LOGS_EXAMPLE[last_log_example_index:]):
                            is_last_index = (length - 1) == index
                            progress = 100 if is_last_index else 99
                            startup_ts_left = 0 if is_last_index else 1
                            status = "started" if is_last_index else "starting"
                            result_list.append(
                                {
                                    "ts": int(time.time()),
                                    "status": status,
                                    "message": build_progress_msg(msg),
                                    "progress": progress,
                                    "startup_ts_left": startup_ts_left,
                                }
                            )
                        last_status = "started"
                        return await save_log_to_redis(result_list)
                    else:
                        total_ts = LLMManagerApi.LLM_RESTART_TOTAL_TS
                        startup_ts_left = format_value(int(total_ts - uptime_seconds))
                        progress = format_value(100 - int(startup_ts_left / total_ts * 100))
                        if startup_ts_left <= 5:
                            startup_ts_left = 5
                        if progress >= 95:
                            progress = 95
                        if last_log_example_index >= len(LOGS_EXAMPLE) - 1:
                            # 一直未成功，持续打印倒数第二条日志
                            last_log_example_index = len(LOGS_EXAMPLE) - 1 - 1
                        result_list.append(
                            {
                                "ts": int(time.time()),
                                "status": "starting",
                                "progress": progress,
                                "startup_ts_left": startup_ts_left,
                                "message": build_progress_msg(LOGS_EXAMPLE[last_log_example_index]),
                            }
                        )
                        last_log_example_index += 1
                        used_ts = int(LLMManagerApi.LLM_RESTART_TIMEOUT_TS - uptime_seconds)
                        await save_log_to_redis(result_list, 10 if used_ts <= 0 else used_ts)

                    if uptime_seconds >= LLMManagerApi.LLM_RESTART_TIMEOUT_TS:
                        last_status = "failed"
                        result_list.append(build_failed_log("模型启动超时, 任务失败"))
                        return await save_log_to_redis(result_list)
                    else:
                        await asyncio.sleep(10)
        except Exception as e:
            logger.info(f"Restart failed: {str(e)}")
            last_status = "failed"
            result_list.append(build_failed_log())
            return await save_log_to_redis(result_list)
        finally:
            logger.debug("Write to mysql data...")
            LLMManagerApi.write_to_mysql(log_id, last_status, result_list)

    @staticmethod
    def write_to_mysql(log_id, status, result_list):
        if status == "starting":
            new_status = 1
        elif status == "started":
            new_status = 2
        elif status == "failed":
            new_status = 3
        else:
            new_status = 4  # stopped
        retry_count = 3
        while retry_count > 0:
            try:
                return SwitchModelLogCurd(next(get_db())).update_log(log_id, new_status, json.dumps(result_list))
            except Exception as e:
                retry_count -= 1
                logger.info(f"Error occurred: {e}")

    @staticmethod
    def get_current_llm_path_from_yml():
        yaml = ruamel.yaml.YAML()
        yaml.width = 4096  # 尽量减少折叠换行
        yaml.preserve_quotes = True  # 保持字符串格式，避免意外格式变化
        with open(LLMManagerApi.LLM_DOCKER_COMPOSE_YML, "r", encoding="utf-8") as f:
            data = yaml.load(f)
            command = data["services"]["vllm"]["command"]
        return LLMManagerApi.get_llm_path_from_command(command)

    @staticmethod
    def get_basename(path: str | None):
        return os.path.basename(path.rstrip("/")) if isinstance(path, str) else ""

    @staticmethod
    def get_current_llm_config_from_yml():
        yaml = ruamel.yaml.YAML()
        yaml.width = 4096  # 尽量减少折叠换行
        yaml.preserve_quotes = True  # 保持字符串格式，避免意外格式变化
        with open(LLMManagerApi.LLM_DOCKER_COMPOSE_YML, "r", encoding="utf-8") as f:
            data = yaml.load(f)
            config = data["services"]["vllm"]
        return config

    @staticmethod
    def update_vllm_docker_compose_yml(new_command):
        yaml = ruamel.yaml.YAML()
        yaml.width = 4096  # 尽量减少折叠换行
        yaml.preserve_quotes = True
        with open(LLMManagerApi.LLM_DOCKER_COMPOSE_YML, "r", encoding="utf-8") as f:
            data = yaml.load(f)
            data["services"]["vllm"]["command"] = new_command
        with open(LLMManagerApi.LLM_DOCKER_COMPOSE_YML, "w", encoding="utf-8") as f:
            yaml.dump(data, f)

    @staticmethod
    def get_llm_path_from_command(command):
        match = re.search(r"--model\s+(\S+)", command)
        if match:
            model_path = match.group(1)
            return model_path  # deepseek-r1-distill-qwen
        return None

    @staticmethod
    def get_vars_from_env(env_list) -> dict:
        # ['TZ=${TIMEZONE}', 'OMP_NUM_THREADS=10', 'CUDA_VISIBLE_DEVICES=0,1']
        load_dotenv(LLMManagerApi.LLM_DOCKER_COMPOSE_ENV, override=True)
        env_dict = {}
        for item in env_list:
            key, value = item.split("=", 1)
            # 使用Template替换${VAR}
            value = os.path.expandvars(value)
            env_dict[key] = value
        logger.info(f"Env dict: {env_dict}")
        return env_dict

    @staticmethod
    def get_volumes_from_env(volumes) -> dict:
        load_dotenv(LLMManagerApi.LLM_DOCKER_COMPOSE_ENV, override=True)
        volume_mapping = {}
        for v in volumes:
            path = os.path.expandvars(v)
            paths = path.split(":")
            if len(paths) <= 2:
                mode = "rw"
            else:
                mode = paths[2]
            volume_mapping[paths[0]] = {"bind": paths[1], "mode": mode}
        logger.info(f"Volume mapping: {volume_mapping}")
        return volume_mapping

    @staticmethod
    def get_ports(ports):
        if ports is None:
            return {}
        port_mapping = {}
        for p in ports:
            port_mapping[f"{p.split(':')[-1]}/tcp"] = p.split(":")[0]
        logger.info(f"Port: {port_mapping}")
        return port_mapping

    @staticmethod
    def get_networks(yaml_config_networks, docker_client: DockerClientDep):
        # {'work-network': {'ipv4_address': '************'}}
        all_network_mapping = {}
        for n in docker_client.networks.list():
            all_network_mapping[n.name] = n

        network_name = None
        config_networks = {}
        for abbr_name, data in yaml_config_networks.items():
            network_name = None
            for full_name in all_network_mapping.keys():
                if full_name.endswith(abbr_name):
                    network_name = full_name
                    break
            if network_name is None:
                logger.info(f"Unknown network: {abbr_name}")
                continue
            config_networks[network_name] = data["ipv4_address"]
        # {'data_work-network': '************'}
        logger.info(f"Networks: {config_networks}")
        return config_networks

    @staticmethod
    def container_is_healthy(docker_client: DockerClientDep):
        container = LLMManagerApi.get_container_by_name(docker_client, LLMManagerApi.LLM_CONTAINER_NAME)
        if container is None:
            return False
        return container.health == "healthy"

    @staticmethod
    def container_is_exited(docker_client: DockerClientDep):
        container = LLMManagerApi.get_container_by_name(docker_client, LLMManagerApi.LLM_CONTAINER_NAME)
        if container is None:
            return False
        return container.status == "exited"

    @staticmethod
    def get_container_by_name(docker_client: DockerClientDep, name):
        try:
            return docker_client.containers.get(name)
        except docker.errors.NotFound:
            logger.info(f"Container is not Found: {name}")
            return None

    @staticmethod
    def get_llm_startup_ts_left() -> int:
        redis_data = RedisSession().get(LLMManagerApi.LLM_STATUS_REDIS_KEY)
        if redis_data is not None:
            data = json.loads(redis_data.decode("utf-8"))  # type: ignore
            if len(data) <= 0:
                return 0
            else:
                if data[-1]["status"] == "failed":
                    return 0
                else:
                    return data[-1]["startup_ts_left"]
        else:
            return 0


if __name__ == "__main__":
    import asyncio
    from app.api.deps import get_docker_client

    t = LLMManagerApi()
    command = "--served-model-name   deepseek-r1-distill-qwen --model  /data/models/qwq32b --tensor-parallel-size 2  --max-model-len 40000  --gpu_memory_utilization 0.8"
    d = t.get_llm_path_from_command(command)
    print(d)

    async def main():
        docker_client = next(get_docker_client())
        resp = await t.get_llm_list(docker_client)
        print("llm_list", resp.content)

        # resp2 = await t.switch_llm(docker_client, LLMManagerSwitchInput(id="deepseek_r1", log_id=1))
        resp2 = await t.get_llm_list(docker_client)
        print("switch: ", resp2.content)

    def read_redis_test():
        redis_data = RedisSession().get(LLMManagerApi.LLM_STATUS_REDIS_KEY)
        if redis_data is not None:
            data = json.loads(redis_data.decode("utf-8"))  # type: ignore
            for msg in data[0:]:
                print("Redis msg:", msg)

    def create_redis_container_test():
        # 创建 Docker 客户端
        docker_client = next(get_docker_client())
        try:
            redis_name = "my-redis-test"
            network_name = "tes_mynetwork"
            ip_v4 = "************"

            networking_config = {network_name: docker_client.api.create_endpoint_config(ipv4_address=ip_v4)}

            # 启动 Redis 容器
            current_container = LLMManagerApi.get_container_by_name(docker_client, redis_name)
            if current_container is not None:
                print("current_container", current_container.id, current_container.name)
                current_container.remove(force=True)
            # network = docker_client.networks.get(network_name)
            # try:
            #     network.disconnect(current_container.name, force=True)
            # except Exception:
            #     pass
            container = docker_client.containers.run(
                image="redis",  # 使用官方 Redis Alpine 镜像
                command="sleep 1000",
                name=redis_name,  # 容器名称
                network=network_name,
                networking_config=networking_config,
                restart_policy={"Name": "always"},  # 自动重启
                detach=True,  # 后台运行
            )
            # docker_client.api.connect_container_to_network(
            #     container.id,
            #     net_id="7ceef88732b2da394eee230d04b50db19b8a8000321f28ba23df86932eb1eab3",
            #     ipv4_address="************",
            # )
            print(f"Redis 容器已启动，ID: {container.id}")
            network = docker_client.networks.get("docker_ragflow2")
            # if network is None:
            #     logger.error(f"network {network_name} not found")
            network.connect(container=container.id, ipv4_address="************")  # type: ignore
            return container
        except Exception as e:
            logger.info(f"启动失败: {e}")
            return None

    # read_redis_test()
    # create_redis_container_test()
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
