import os
import json
import random
import shutil
import traceback
import subprocess
from enum import Enum
from datetime import datetime
from pathlib import Path
from typing import Any

from sqlalchemy import update
from app.models.task import Task
from app.services.flow import logger, TaskStatus, now_date_str


# TODO： 取消运行中的任务，才可以重新运行
# TODO:  current_workspace_abs_path 的设置不合理，没和用户关联
class TaskType(Enum):
    AUTO = 1
    MANUAL = 2


import json
import datetime
import sqlalchemy.exc
from sqlalchemy.orm import Session

# from app.models.ai_mcp import AiMcp
from app.models.task import Task
from app.log import logger


class TaskCurd:
    def __init__(self, session: Session):
        self.session = session

    def add_task(self, task: Task):
        try:
            self.session.add(task)
            self.session.commit()
            self.session.refresh(task)
            logger.info(f"Got task id: {task.id}")
            return task.to_dict()
        except Exception as e:
            self.session.rollback()
            logger.error(f"Add task failed: {e}")
            raise e

    def get_step_chain(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        return json.loads(task.step_chain) if task else None

    def update_step_chain(self, id, step_chain):
        try:
            self.session.execute(update(Task).where(Task.id == id).values(step_chain=step_chain))
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            logger.error(f"Update step chain failed: {e}")
            raise e

    # @staticmethod
    # def get_instance_from_id(namespace_id):
    #     # TODO: 先判断是否存在命名空间
    #     return FlowNamespace(namespace_id=namespace_id)

    def reset(self, id):
        # 重置数据. 清理数据
        try:
            chat_history = self.get_chat_histories(id)
            if chat_history is not None:
                chat_history = json.dumps(chat_history[:1])
            self.session.execute(
                update(Task)
                .where(Task.id == id)
                .values(log=None, input_and_output=None, stat=None, message=chat_history)
            )
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            logger.error(f"Update step chain failed: {e}")
            raise e

    def add_chat_history(self, id, chat_msg: dict):
        # 记录聊天信息
        task = self.session.query(Task).filter_by(id=id).first()
        if task is not None:
            if task.message:
                message = json.loads(task.message)
                message.append(chat_msg)
            else:
                message = [chat_msg]
            try:
                task.message = json.dumps(message)
                self.session.commit()
            except Exception as e:
                self.session.rollback()
                logger.error(f"Add chat history failed: {e}")
                raise e

    def get_chat_histories(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        return json.loads(task.message) if task and task.message else None

    def add_log(self, id, msg: str, step: dict, level: str = "info"):
        # 记录日志
        log_msg = {
            "msg": msg,
            "level": level,
            "created_at": now_date_str(),
            "call_function": step.get("call_function", None),
            "component_name": step.get("component_name", None),
            "component_id": step.get("component_id", None),
            "step": step.get("step", None),
        }
        task = self.session.query(Task).filter_by(id=id).first()
        if task is not None:
            if task.log:
                log_data = json.loads(task.log)
                log_data.append(log_msg)
            else:
                log_data = [log_msg]
            try:
                task.log = json.dumps(log_data)
                self.session.commit()
                return log_msg
            except Exception as e:
                self.session.rollback()
                logger.error(f"Add log failed: {e}")
                raise e

    def get_logs(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        return json.loads(task.log) if task and task.log else None

    def add_input_and_output(self, id: int, msg: dict):
        # 记录输出和输出
        task = self.session.query(Task).filter_by(id=id).first()
        if task is not None:
            if task.input_and_output:
                input_and_output = json.loads(task.input_and_output)
                input_and_output.append(msg)
            else:
                input_and_output = [msg]
            try:
                task.input_and_output = json.dumps(input_and_output)
                self.session.commit()
            except Exception as e:
                self.session.rollback()
                logger.error(f"Add input and output failed: {e}")
                raise e

    def add_node_stat_msg(self, id: int, msg: dict):
        # 记录统计信息
        task = self.session.query(Task).filter_by(id=id).first()
        if task is not None:
            if task.stat:
                stat = json.loads(task.stat)
                stat.append(msg)
            else:
                stat = [msg]
            try:
                task.stat = json.dumps(stat)
                self.session.commit()
            except Exception as e:
                self.session.rollback()
                logger.error(f"Add node stat failed: {e}")
                raise e

    def get_outputs(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        return json.loads(task.input_and_output) if task and task.input_and_output else None

    def get_stat(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        return json.loads(task.stat) if task and task.stat else None

    def update_meta(self, id: int, field: str, value: Any):
        task = self.session.query(Task).filter_by(id=id).first()
        if task:
            try:
                self.session.query(Task).filter_by(id=id).update({field: value})
                self.session.commit()
            except Exception as e:
                self.session.rollback()
                logger.error(f"Update meta failed: {e}")
                raise e
        else:
            logger.info(f"Unknown task: id: {id}")

    def get_meta(self, id: int):
        task = self.session.query(Task).filter_by(id=id).first()
        if task is None:
            logger.info(f"Unknown task: id: {id}")
            return None

        result = task.to_dict()
        # 兼容一下: question # TODO: 哪写地方用到了, 是否需要单独一个函数处理。
        result["question"] = json.loads(task.message)[0]["content"]
        return result

    def delete_by_id(self, id: int):
        task = self.session.query(Task).filter_by(id=id).delete()
        return task

    def get_all_ns(self, user_id=None, page: int = 1, page_size=100):
        """
        使用 pathlib 遍历目录树，查找第四层文件夹中的 meta.json
        """
        # TODO: 根据分页获取数据列表
        tasks = self.session.query(Task).order_by(Task.created_at.desc()).all()
        return [task.to_dict() for task in tasks]


# 测试
if __name__ == "__main__":
    import json

    task_list = {
        "task_list": [
            {"step": 1, "question": "北京和山东省的2022年GDP总量及增长率分别是多少？", "call_function": "数据库查询"},
            {
                "step": 2,
                "question": "北京和山东省的主要产业结构是什么？各产业占GDP比重如何？",
                "call_function": "知识库查询",
            },
            {
                "step": 3,
                "question": "北京和山东省的财政收入和固定资产投资额分别是多少？",
                "call_function": "数据库查询",
            },
            {"step": 4, "question": "北京和山东省的人均GDP水平及排名如何？", "call_function": "数据分析"},
            {
                "step": 5,
                "question": "北京和山东省的第三产业占比差异及其对经济的影响是什么？",
                "call_function": "数据分析",
            },
        ]
    }
    # _ns = FlowNamespace()
    # _script = """import os;print('Current working directory: ' + os.getcwd())"""
    # _script_name = "script.py"
    # _requires = "pip install aiohttp"
    # _res = _ns.exe_python(_script, _script_name, _requires)
    # print(json.dumps(_res, ensure_ascii=True, indent=2))
    # print(f"namespace id: {_ns.id}")


# 数据元信息: 问题名称，时间。
# 对话: 聊天记录。
# 流程画布: 输入和输出。（整体套局部）
# 执行日志: 执行日志。全部[整体]。
# 结果展示: 最后一条工作流的结果。
#
