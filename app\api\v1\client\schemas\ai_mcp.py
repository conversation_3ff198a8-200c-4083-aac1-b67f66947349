from pydantic import BaseModel, Field, field_validator


class AddMcp(BaseModel):
    config: str = Field(description="mcp配置")
    name: str = Field(default="", description="mcp名称")
    desc: str = Field(default="", description="mcp描述")


class DeleteMcp(BaseModel):
    id: int = Field(description="mcp server id")


class SetMcpEnabled(BaseModel):
    id: int = Field(description="mcp id")
    enabled: bool = Field(default=True, description="是否启用")


class GetMcpList(BaseModel):
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=100, description="每页数量")


class GetMcpListByConfig(BaseModel):
    config: str = Field(description="mcp配置")


class GetMcpTools(BaseModel):
    mcp_id: int = Field(description="mcp id")


class TestMcpTool(BaseModel):
    id: int = Field(description="mcp tool id")
    params: dict = Field(description="请求的参数, eg: {timezone': 'Asia/Shanghai'}")


class TestMcpToolByConfig(BaseModel):
    config: str = Field(description="mcp配置")
    tool_name: str = Field(description="工具名称")
    params: dict = Field(description="请求的参数, eg: {timezone': 'Asia/Shanghai'}")
