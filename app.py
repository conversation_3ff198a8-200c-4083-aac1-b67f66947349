import logging
import os
import uvicorn
from optparse import OptionPars<PERSON>


def _get_opt():
    parser = OptionParser("%prog [options]", version="%prog v0.1")
    # parser = argparse.ArgumentParser(description="FastAPI Application")
    parser.add_option("--config", dest="config", default="settings.toml", help="config")
    parser.add_option("--workers", dest="workers", type="int", default=1, help="Number of worker processes")
    return parser.parse_args()


opts, args = _get_opt()

if opts.config == "dev":
    settings_file = "settings_dev.toml"
else:
    settings_file = opts.config

os.environ['settings_file'] = settings_file


# 初始化数据库,必须放在routers前方
from app.config import settings
# 在启动处导出app应用
from app.router import app

from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi import  Request
from app.log import logger


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error_message = str(exc)
    error_response = {
        "msg": error_message,
        "code": 101,
        "content": ""
    }
    logger.error(f"请求地址:{request.url},请求参数异常:{error_message}")
    return JSONResponse(content=error_response, status_code=422)



if __name__ == "__main__":
    # 启动服务
    try:
        import resource
        soft, hard = resource.getrlimit(resource.RLIMIT_NOFILE)
        soft = 65536 if hard > 65536 else hard
        resource.setrlimit(resource.RLIMIT_NOFILE, (soft, hard))
    except Exception as e:
        print("[-] set NOFILE limit error:", str(e))
    uvicorn.run("app.router:app", host=settings.service.bind, port=settings.service.port, access_log=False, workers=opts.workers)









