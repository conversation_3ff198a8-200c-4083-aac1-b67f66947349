1. Docker镜像制作(构建amd64架构):
    docker build --platform linux/amd64 -t sandbox:0.0.1 .
    # 如果需要打tag: docker tag sandbox:0.0.1 sandbox:0.0.2

2. 在容器内部测试镜像
    docker run --rm -it -v "$PWD:/app" -w /app -p 11235:11235 ai_crawl bash
3. 保存镜像到本地
   docker save -o sandbox.tar sandbox:0.0.1

4. 镜像迁移
    1. 镜像压缩: gzip -9 sandbox.tar
    2. 镜像解压缩: gzip -d sandbox.tar.gz
    3. 镜像加载: docker load -i sandbox.tar

5. 镜像的启动脚本: ./server.py
6. 服务地址demo: http://***************:11235/docs
