import json
import uuid
from sqlalchemy.orm import Session
from app.log import logger
from app.curd.ai_seesion_list_curd import AiSessionListCurd
from app.curd.ai_chat_list_curd import AiChatListCurd
from app.services.ai_tools.agent_factory import AgentFactory
from app.api.v1.client.endpoints.llm_manager import LLMManagerApi
from app.api.deps import get_docker_client
from app.utils import SensitiveWords


class AiChatService:
    def __init__(self):
        pass

    # 创建新的会话
    def add_session_info(self, params, db: Session):
        try:
            ai_session_list_curd = AiSessionListCurd(db)
            if params.session_id is None or params.session_id == "":
                # 使用uuid生成session_id
                params.session_id = str(uuid.uuid4())
            ai_session_list_curd.add_session_info(params)
            return params.session_id
        except Exception as e:
            raise Exception(f"创建会话信息出错: {str(e)}")

    # 添加会话信息
    def add_chat_info(self, params, db: Session):
        try:
            ai_chat_list_curd = AiChatListCurd(db)
            ai_chat_list_curd.add_chat_info(params)
        except Exception as e:
            raise Exception(f"创建会话信息出错: {str(e)}")

    # 获取对话信息。
    async def chat_complete(self, db, param):
        # 获取对应的chat信息
        try:
            agent_factory = AgentFactory()
            func = agent_factory.get_function(param)
            if func:
                # 初始化敏感词库
                sw = SensitiveWords(db)
                sw.init()
                
                chat_result = func(db=db, param=param)
                async for chat_info in chat_result:
                    resp_content = chat_info["choices"][0]["delta"]["content"]  # type: ignore
                    error_flag = "\n**ERROR**:"
                    system_flag = "\n【**系统提示**】"
                    content_is_str = isinstance(resp_content, str)
                    if content_is_str and resp_content.startswith(
                        f"{error_flag} Connection error."
                    ):
                        docker_client = next(get_docker_client())
                        is_healthy = LLMManagerApi.container_is_healthy(docker_client)
                        if not is_healthy:
                            #  判断一下vllm容器的健康状态，如果不健康就返回一个错误
                            ts: int = LLMManagerApi.get_llm_startup_ts_left()
                            logger.info(f"倒计时: {ts}")
                            if ts <= 0:
                                chat_info["choices"][0]["delta"]["content"] = f"{system_flag} 模型正在重启中."  # type: ignore
                            else:
                                date = self.__format_countdown(ts)
                                chat_info["choices"][0]["delta"]["content"] = f"{system_flag} 模型正在切换中: 预计剩余时间: {date}"  # type: ignore
                        else:
                            pass
                    elif (
                        content_is_str
                        and "This model's maximum context length is" in resp_content
                        and "However, you requested" in resp_content
                    ):
                        chat_info["choices"][0]["delta"]["content"] = f"{system_flag} 上下文过长，请重新开启对话。"  # type: ignore
                    else:
                        pass
                    # 替换其他错误提示
                    new_resp_content = chat_info["choices"][0]["delta"]["content"]  # type: ignore
                    if content_is_str and new_resp_content.startswith(error_flag):
                        chat_info["choices"][0]["delta"]["content"] = (  # type: ignore
                            new_resp_content.replace(error_flag, system_flag)
                        )
                    error_flag2 = "**ERROR**:"
                    system_flag2 = "【**系统提示**】"
                    if content_is_str and new_resp_content.startswith(error_flag2):
                        chat_info["choices"][0]["delta"]["content"] = (  # type: ignore
                            new_resp_content.replace(error_flag2, system_flag2)
                        )

                    yield json.dumps(chat_info) + "\n"
        except Exception as e:
            logger.error(f"对话异常: {e}")
            # 你的redis 有值 我们就认为切换模型

            yield json.dumps({"code": "500", "msg": f"对话异常{e}"}) + "\n"

    def __format_countdown(self, seconds):
        if seconds > 60:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes}分钟{secs:02d}秒"
        else:
            return f"{seconds}秒"


from app.db import get_session


async def a():
    a = AiChatService()
    t = {
        "query": "最新北京有哪些大事件",
        "prompt": "",#如果用户让你进行数据分析，你需要使用html和echarts生成用户所需要的图表。。
        "kb_name": "",
        "knowledge_id": "d9412c9a3cf34da7b1a595fdf3a1b145",
        "top_k": 3,
        "score_threshold": 2,
        "stream": True,
        "model": "deepseek-r1-distill-qwen",
        "temperature": 0.7,
        "max_tokens": 4096,
        "type": "agent_chat",
        "type": "knowledge_base_search",
        "type": "file_chat",
        "type": "ai_search",
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "n": 1,
        "top_p": 1,
        "chat_id": "aa5be70afb5211ef9dce0242ac120006",
        "session_id": "7db033bf-59f3-4f76-b8cb-0bce6e9e609c",
        "user_id": 9,
        # "agent_id": "4a489f7e007b11f08e630242ac120006",
    }

    code = ""
    tst2 = ""
    strr = ""
    has_code = False
    has_think = False
    with get_session() as db:
        # 创建数据库会话
        async for chunk in a.chat_complete(db, t):
            print(chunk)

            chunk = json.loads(chunk)
            # 如果是流式对话，需要把代码块提取出来
            content = str(chunk["choices"][0]["delta"]["content"])
            res_con = str(chunk["choices"][0]["delta"]["reason_content"])
            if content.startswith("```"):
                if not has_code:
                    has_code = True
                else:
                    has_code = False
                    code += content
            if has_code:
                code += content

            # 如果有think字段，说明需要继续思考

            tst2 += res_con

            strr += content

    print(f"源数据:{strr}")
    print(f"代码数据:{code}")
    print(f"思考过程:{tst2}")


import asyncio

if __name__ == "__main__":
    asyncio.run(a())
