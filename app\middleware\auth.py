from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from app.log import logger
from app.const.response import MessageCode
from app.utils.license import YuanFangLicense


class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            success, msg = YuanFangLicense.check_license()
            if not success:
                error_response = {
                    "msg": msg,
                    "code": MessageCode.LICENSEDENIED.status_code,
                    "content": "",
                }
                logger.error(f"请求地址: {request.url},错误信息: {msg}")
                return JSONResponse(content=error_response, status_code=401)
            response = await call_next(request)
            return response
        except Exception as e:
            error_message = str(e)
            error_response = {"msg": error_message, "code": 101, "content": ""}
            logger.error(f"请求地址:{request.url},错误信息:{error_message}")
            return JSONResponse(content=error_response, status_code=500)
