import json
import logging
import requests
from typing import List, Dict, Any, Optional, Callable
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage


class MCPServiceManager:
    def __init__(self, config_path: str, llm_model: str = "gpt-3.5-turbo"):
        """
        初始化MCP服务管理器

        Args:
            config_path: 服务配置文件路径
            llm_model: 用于参数生成的LLM模型
        """
        self.config = self._load_config(config_path)
        self.llm = ChatOpenAI(model_name=llm_model)
        self.service_registry = {}
        self._register_services()
        self.logger = logging.getLogger(__name__)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载服务配置"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            raise ValueError(f"配置文件加载失败: {e}")

    def _register_services(self) -> None:
        """注册所有配置的MCP服务"""
        for service_name, service_config in self.config.get("services", {}).items():
            self.service_registry[service_name] = service_config
            self.logger.info(f"已注册服务: {service_name}")

    def generate_parameters(self, user_intent: str, service_name: str) -> Dict[str, Any]:
        """
        根据用户意图和服务信息生成调用参数

        Args:
            user_intent: 用户语义描述
            service_name: 目标服务名称

        Returns:
            生成的调用参数
        """
        service_config = self.service_registry.get(service_name)
        if not service_config:
            raise ValueError(f"未找到服务配置: {service_name}")

        # 构建提示词，指导LLM生成参数
        prompt = [
            SystemMessage(content=f"""
                你是一个专业的API调用参数生成助手。
                请根据用户意图和服务描述生成合适的API调用参数。
                服务名称: {service_name}
                服务描述: {service_config.get('description', '无描述')}
                参数规范: {json.dumps(service_config.get('parameter_spec', {}), ensure_ascii=False)}
                请仅返回参数的JSON对象，不要包含其他解释。
            """),
            HumanMessage(content=user_intent)
        ]

        # 调用LLM生成参数
        response = self.llm(prompt)
        try:
            # 尝试解析LLM返回的JSON
            parameters = json.loads(response.content)
            return parameters
        except json.JSONDecodeError:
            self.logger.error(f"参数解析失败: {response.content}")
            return {}

    def invoke_service(self, service_name: str, parameters: Dict[str, Any]) -> Any:
        """
        调用指定的MCP服务

        Args:
            service_name: 服务名称
            parameters: 调用参数

        Returns:
            服务响应结果
        """
        service_config = self.service_registry.get(service_name)
        if not service_config:
            raise ValueError(f"未找到服务配置: {service_name}")

        # 获取服务调用函数
        service_type = service_config.get("type", "http")
        if service_type == "http":
            return self._invoke_http_service(service_config, parameters)
        elif service_type == "function":
            # 可以扩展支持本地函数调用
            function_name = service_config.get("function_name")
            if function_name and function_name in globals():
                return globals()[function_name](**parameters)
            else:
                raise ValueError(f"未找到函数: {function_name}")
        else:
            raise ValueError(f"不支持的服务类型: {service_type}")

    def _invoke_http_service(self, service_config: Dict[str, Any], parameters: Dict[str, Any]) -> Any:
        """调用HTTP类型的MCP服务"""
        method = service_config.get("method", "POST").upper()
        url = service_config.get("url")
        headers = service_config.get("headers", {})
        timeout = service_config.get("timeout", 30)

        if not url:
            raise ValueError("服务配置缺少URL")

        try:
            if method == "GET":
                response = requests.get(url, params=parameters, headers=headers, timeout=timeout)
            elif method == "POST":
                response = requests.post(url, json=parameters, headers=headers, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"服务调用失败: {e}")
            raise

    def process_user_request(self, user_intent: str, service_name: Optional[str] = None) -> Any:
        """
        处理用户请求，自动选择服务并生成参数调用

        Args:
            user_intent: 用户语义描述
            service_name: 可选的目标服务名称，不指定时将自动识别

        Returns:
            服务响应结果
        """
        # 如果未指定服务，使用LLM识别合适的服务
        if not service_name:
            service_name = self._identify_service(user_intent)

        # 生成参数
        parameters = self.generate_parameters(user_intent, service_name)

        # 调用服务
        return self.invoke_service(service_name, parameters)

    def _identify_service(self, user_intent: str) -> str:
        """使用LLM识别最适合处理用户意图的服务"""
        # 构建服务列表提示
        service_list = "\n".join([
            f"{idx + 1}. {name}: {config.get('description', '无描述')}"
            for idx, (name, config) in enumerate(self.service_registry.items())
        ])

        prompt = [
            SystemMessage(content=f"""
                你是一个智能服务选择器。
                请根据用户意图，从以下可用服务中选择最合适的一个服务名称。
                可用服务:
                {service_list}

                请仅返回服务名称，不要包含其他解释。
            """),
            HumanMessage(content=user_intent)
        ]

        response = self.llm(prompt)
        return response.content.strip()


# 服务配置示例
SERVICE_CONFIG = {
    "services": {
        "deepseek": {
            "type": "http",
            "method": "POST",
            "url": "https://api.deepseek.com/v1/chat/completions",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer YOUR_API_KEY"
            },
            "description": "调用DeepSeek大语言模型生成文本",
            "parameter_spec": {
                "model": "模型名称，如YuanFang-Agent-Model",
                "messages": "对话历史，格式为[{\"role\": \"user\", \"content\": \"你好\"}]"
            }
        },
        "image_generation": {
            "type": "http",
            "method": "POST",
            "url": "https://api.image-generator.com/v1/images/generations",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer YOUR_API_KEY"
            },
            "description": "根据文本描述生成图像",
            "parameter_spec": {
                "prompt": "图像生成提示词",
                "n": "生成图像数量",
                "size": "图像尺寸，如1024x1024"
            }
        }
    }
}

# 使用示例
if __name__ == "__main__":
    # 保存配置到文件
    # with open("mcp_services.json", "w") as f:
    #     json.dump(SERVICE_CONFIG, f, indent=2)

    # 初始化服务管理器
    manager = MCPServiceManager("mcp_services.json")

    # 示例1: 显式指定服务
    user_intent = "请使用DeepSeek模型总结这篇文章：人工智能正在改变世界..."
    result = manager.process_user_request(user_intent, "deepseek")
    print(f"服务返回: {result}")

    # 示例2: 自动选择服务
    user_intent = "生成一张美丽的山水风景画"
    result = manager.process_user_request(user_intent)
    print(f"服务返回: {result}")