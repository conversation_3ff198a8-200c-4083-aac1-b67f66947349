from sqlalchemy.orm import Session
from sqlalchemy import select, delete, func, update
import sqlalchemy.exc
import copy

from app.db import get_session
from app.models.ai_chat_list import AiChatList
from app.log import logger
from app.const.isdelete import IS_DELETE, IS_NOT_DELETE


class AiChatListCurd(object):

    def __init__(self, db: Session):
        self.db = db

    # def __exit__(self, exc_type, exc_val, exc_tb):
    #     self.db.close()
    def add_chat_info(self, chat_info: dict):
        log_obj = AiChatList(**chat_info)
        self.db.add(log_obj)
        self.db.commit()
        return
