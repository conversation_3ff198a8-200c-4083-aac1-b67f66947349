"""
系统架构优化建议
提供完整的架构改进方案
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Protocol
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging

logger = logging.getLogger(__name__)

# 1. 统一的任务接口定义
class TaskExecutor(Protocol):
    """任务执行器协议"""
    
    async def execute(self, task_definition: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """执行任务"""
        ...
    
    async def validate_result(self, result: Any, task_definition: Dict[str, Any]) -> bool:
        """验证结果"""
        ...
    
    def get_capabilities(self) -> List[str]:
        """获取能力列表"""
        ...

class ToolInterface(Protocol):
    """工具接口协议"""
    
    async def call(self, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> Any:
        """调用工具"""
        ...
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式"""
        ...
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取工具元数据"""
        ...

# 2. 分层架构设计
class ArchitectureLayer(Enum):
    """架构层级"""
    PRESENTATION = "presentation"      # 表示层
    APPLICATION = "application"        # 应用层
    DOMAIN = "domain"                 # 领域层
    INFRASTRUCTURE = "infrastructure"  # 基础设施层

@dataclass
class LayerComponent:
    """层级组件"""
    name: str
    layer: ArchitectureLayer
    dependencies: List[str]
    interfaces: List[str]

class LayeredArchitecture:
    """分层架构管理器"""
    
    def __init__(self):
        self.components: Dict[str, LayerComponent] = {}
        self.layer_order = [
            ArchitectureLayer.INFRASTRUCTURE,
            ArchitectureLayer.DOMAIN,
            ArchitectureLayer.APPLICATION,
            ArchitectureLayer.PRESENTATION
        ]
    
    def register_component(self, component: LayerComponent):
        """注册组件"""
        self.components[component.name] = component
    
    def validate_dependencies(self) -> List[str]:
        """验证依赖关系"""
        violations = []
        
        for name, component in self.components.items():
            component_layer_index = self.layer_order.index(component.layer)
            
            for dep_name in component.dependencies:
                if dep_name in self.components:
                    dep_component = self.components[dep_name]
                    dep_layer_index = self.layer_order.index(dep_component.layer)
                    
                    # 检查是否违反分层原则（上层依赖下层）
                    if dep_layer_index > component_layer_index:
                        violations.append(
                            f"{name} ({component.layer.value}) 不应依赖 "
                            f"{dep_name} ({dep_component.layer.value})"
                        )
        
        return violations

# 3. 事件驱动架构
class Event:
    """事件基类"""
    
    def __init__(self, event_type: str, data: Dict[str, Any], source: str = None):
        self.event_type = event_type
        self.data = data
        self.source = source
        self.timestamp = asyncio.get_event_loop().time()

class EventHandler(ABC):
    """事件处理器基类"""
    
    @abstractmethod
    async def handle(self, event: Event) -> None:
        """处理事件"""
        pass
    
    @abstractmethod
    def can_handle(self, event_type: str) -> bool:
        """是否可以处理该类型事件"""
        pass

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.handlers: List[EventHandler] = []
        self.event_history: List[Event] = []
        self.max_history = 1000
    
    def register_handler(self, handler: EventHandler):
        """注册事件处理器"""
        self.handlers.append(handler)
    
    async def publish(self, event: Event):
        """发布事件"""
        self.event_history.append(event)
        
        # 保持历史记录大小
        if len(self.event_history) > self.max_history:
            self.event_history = self.event_history[-self.max_history:]
        
        # 异步处理事件
        tasks = []
        for handler in self.handlers:
            if handler.can_handle(event.event_type):
                task = asyncio.create_task(handler.handle(event))
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

# 4. 任务编排引擎重构
class TaskOrchestrationEngine:
    """任务编排引擎"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.task_registry: Dict[str, TaskExecutor] = {}
        self.execution_strategies: Dict[str, 'ExecutionStrategy'] = {}
    
    def register_task_executor(self, task_type: str, executor: TaskExecutor):
        """注册任务执行器"""
        self.task_registry[task_type] = executor
    
    def register_execution_strategy(self, strategy_name: str, strategy: 'ExecutionStrategy'):
        """注册执行策略"""
        self.execution_strategies[strategy_name] = strategy
    
    async def orchestrate(self, workflow_definition: Dict[str, Any]) -> Dict[str, Any]:
        """编排执行工作流"""
        workflow_id = workflow_definition.get('id', 'unknown')
        
        # 发布工作流开始事件
        await self.event_bus.publish(Event(
            'workflow_started',
            {'workflow_id': workflow_id, 'definition': workflow_definition}
        ))
        
        try:
            # 选择执行策略
            strategy_name = workflow_definition.get('execution_strategy', 'sequential')
            strategy = self.execution_strategies.get(strategy_name)
            
            if not strategy:
                raise ValueError(f"未找到执行策略: {strategy_name}")
            
            # 执行工作流
            result = await strategy.execute(workflow_definition, self.task_registry)
            
            # 发布工作流完成事件
            await self.event_bus.publish(Event(
                'workflow_completed',
                {'workflow_id': workflow_id, 'result': result}
            ))
            
            return result
            
        except Exception as e:
            # 发布工作流失败事件
            await self.event_bus.publish(Event(
                'workflow_failed',
                {'workflow_id': workflow_id, 'error': str(e)}
            ))
            raise

class ExecutionStrategy(ABC):
    """执行策略基类"""
    
    @abstractmethod
    async def execute(self, workflow_definition: Dict[str, Any], 
                     task_registry: Dict[str, TaskExecutor]) -> Dict[str, Any]:
        """执行工作流"""
        pass

class SequentialExecutionStrategy(ExecutionStrategy):
    """顺序执行策略"""
    
    async def execute(self, workflow_definition: Dict[str, Any], 
                     task_registry: Dict[str, TaskExecutor]) -> Dict[str, Any]:
        """顺序执行任务"""
        tasks = workflow_definition.get('tasks', [])
        results = {}
        context = workflow_definition.get('context', {})
        
        for task_def in tasks:
            task_type = task_def.get('type')
            task_id = task_def.get('id', f"task_{len(results)}")
            
            executor = task_registry.get(task_type)
            if not executor:
                raise ValueError(f"未找到任务执行器: {task_type}")
            
            # 更新上下文
            task_context = {**context, 'previous_results': results}
            
            # 执行任务
            result = await executor.execute(task_def, task_context)
            
            # 验证结果
            is_valid = await executor.validate_result(result, task_def)
            if not is_valid:
                raise ValueError(f"任务 {task_id} 结果验证失败")
            
            results[task_id] = result
        
        return results

class ParallelExecutionStrategy(ExecutionStrategy):
    """并行执行策略"""
    
    async def execute(self, workflow_definition: Dict[str, Any], 
                     task_registry: Dict[str, TaskExecutor]) -> Dict[str, Any]:
        """并行执行任务"""
        tasks = workflow_definition.get('tasks', [])
        context = workflow_definition.get('context', {})
        
        # 创建并行任务
        async_tasks = []
        task_ids = []
        
        for task_def in tasks:
            task_type = task_def.get('type')
            task_id = task_def.get('id', f"task_{len(async_tasks)}")
            
            executor = task_registry.get(task_type)
            if not executor:
                raise ValueError(f"未找到任务执行器: {task_type}")
            
            task_context = {**context}
            async_task = executor.execute(task_def, task_context)
            async_tasks.append(async_task)
            task_ids.append(task_id)
        
        # 等待所有任务完成
        results_list = await asyncio.gather(*async_tasks, return_exceptions=True)
        
        # 处理结果
        results = {}
        for i, result in enumerate(results_list):
            task_id = task_ids[i]
            if isinstance(result, Exception):
                raise ValueError(f"任务 {task_id} 执行失败: {result}")
            results[task_id] = result
        
        return results

# 5. 监控和可观测性
class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.counters: Dict[str, int] = {}
    
    def record_metric(self, name: str, value: float):
        """记录指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)
        
        # 保持最近1000个值
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]
    
    def increment_counter(self, name: str, value: int = 1):
        """增加计数器"""
        self.counters[name] = self.counters.get(name, 0) + value
    
    def get_statistics(self, name: str) -> Dict[str, float]:
        """获取统计信息"""
        if name not in self.metrics:
            return {}
        
        values = self.metrics[name]
        if not values:
            return {}
        
        return {
            'count': len(values),
            'sum': sum(values),
            'avg': sum(values) / len(values),
            'min': min(values),
            'max': max(values)
        }

class PerformanceMonitor(EventHandler):
    """性能监控器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
    
    def can_handle(self, event_type: str) -> bool:
        """可以处理的事件类型"""
        return event_type in [
            'task_started', 'task_completed', 'task_failed',
            'workflow_started', 'workflow_completed', 'workflow_failed'
        ]
    
    async def handle(self, event: Event):
        """处理性能事件"""
        if event.event_type == 'task_completed':
            # 记录任务执行时间
            duration = event.data.get('duration', 0)
            self.metrics_collector.record_metric('task_duration', duration)
            self.metrics_collector.increment_counter('tasks_completed')
        
        elif event.event_type == 'task_failed':
            self.metrics_collector.increment_counter('tasks_failed')
        
        elif event.event_type == 'workflow_completed':
            duration = event.data.get('duration', 0)
            self.metrics_collector.record_metric('workflow_duration', duration)
            self.metrics_collector.increment_counter('workflows_completed')
        
        elif event.event_type == 'workflow_failed':
            self.metrics_collector.increment_counter('workflows_failed')

# 6. 配置管理
class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self):
        self.config: Dict[str, Any] = {}
        self.watchers: List[callable] = []
    
    def load_config(self, config_source: str):
        """加载配置"""
        # 这里可以从文件、数据库或环境变量加载配置
        pass
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        # 通知观察者
        for watcher in self.watchers:
            try:
                watcher(key, value)
            except Exception as e:
                logger.error(f"配置变更通知失败: {e}")
    
    def watch(self, callback: callable):
        """监听配置变更"""
        self.watchers.append(callback)

# 7. 集成示例
def create_optimized_system():
    """创建优化后的系统"""
    # 创建核心组件
    event_bus = EventBus()
    metrics_collector = MetricsCollector()
    config_manager = ConfigurationManager()
    
    # 注册监控器
    performance_monitor = PerformanceMonitor(metrics_collector)
    event_bus.register_handler(performance_monitor)
    
    # 创建编排引擎
    orchestration_engine = TaskOrchestrationEngine(event_bus)
    
    # 注册执行策略
    orchestration_engine.register_execution_strategy(
        'sequential', SequentialExecutionStrategy()
    )
    orchestration_engine.register_execution_strategy(
        'parallel', ParallelExecutionStrategy()
    )
    
    return {
        'event_bus': event_bus,
        'metrics_collector': metrics_collector,
        'config_manager': config_manager,
        'orchestration_engine': orchestration_engine
    }

if __name__ == "__main__":
    system = create_optimized_system()
    print("优化后的系统架构已创建")
