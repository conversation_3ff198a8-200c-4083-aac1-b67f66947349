import re
import uuid
import logging
import datetime
import aiohttp
import requests
from enum import Enum
from typing import Dict, List


# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

_DP_API_URL = "http://192.168.196.151:20000/v1/chat/completions"
_DP_MODEL = "YuanFang-Agent-Model"
_DP_API_KEY = "sk"


# _DP_API_URL = "http://192.168.22.191:8000/v1/chat/completions"
# _DP_MODEL = "/home/<USER>/.cache/modelscope/hub/models/google/medgemma-27b-text-it/"


def now_ts():
    # 毫秒级时间戳
    return int(datetime.datetime.now().timestamp() * 1000)


def now_date_str():
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


async def call_chat_mdl(
    messages: List[Dict[str, str]], deepseek_api_url, model_name: str = "YuanFang-Agent-Model"
) -> str:
    """调用DeepSeek模型"""
    attempt = 3
    while attempt > 0:
        try:
            payload = {"model": model_name, "messages": messages}
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    deepseek_api_url,
                    headers={"Content-Type": "application/json"},
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60 * 2),
                ) as response:
                    response.raise_for_status()
                    data = await response.json()
                    return data["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"调用DeepSeek模型失败: {e}")
            attempt -= 1
    raise ValueError(f"调用DeepSeek模型失败")


def filter_think_tags(response: str) -> str:
    """
    过滤DeepSeek返回结果中的<think>标签内容
    """
    # 使用非贪婪匹配模式，确保正确处理多个标签
    clean_response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL)
    return clean_response.strip()


# 定义默认的 OpenAI 响应字典结构
def build_openai_response(task_id: int, content="", node_event=None, step_chain=None):
    return {
        "id": uuid.uuid4().hex[:24],
        "choices": [
            {
                "delta": {
                    "content": content,
                    "role": "assistant",
                    "function_call": None,
                    "tool_calls": None,
                },
                "finish_reason": None,
                "index": 0,
                "logprobs": None,
            }
        ],
        "created": now_date_str(),
        "model": "default_model",
        "node_event": node_event,
        "step_chain": step_chain,
        "task_id": task_id,
        "object": "chat.completion.chunk",
        "system_fingerprint": "",
        "usage": None,
    }


def merge_messages(messages):
    merged = []
    current_question = []
    current_answers = []

    for msg in messages:
        if msg["role"] == "user":
            # 如果有当前回答，添加到结果
            if current_answers:
                entry = {"question": "\n".join(current_question), "answer": "\n".join(current_answers)}
                merged.append(entry)
                current_question = []
                current_answers = []
            # 收集用户消息作为问题
            current_question.append(msg["content"])
        else:
            # 如果有当前问题，收集系统回复作为回答
            if current_question:
                current_answers.append(msg["content"])
            else:
                # 处理孤立的系统消息（通常不应该出现）
                pass

    # 处理最后一组对话
    if current_question:
        entry = {"question": "\n".join(current_question)}
        if current_answers:
            entry["answer"] = "\n".join(current_answers)
        merged.append(entry)

    return merged


class TaskStatus(Enum):
    UNSTARTED = 0
    RUNNING = 1
    FINISHED = 2
    FAILED = 3
    CANCELLED = 4

if __name__ == "__main__":
    # 测试数据
    messages = [
        {"role": "user", "content": "nihao"},
        {"role": "system", "content": "nihao2"},
        {"role": "system", "content": "nihao3"},
        # {"role": "user", "content": "nihao4"},
        # {"role": "user", "content": "nihao5"},
        # {"role": "system", "content": "nihao6"},
    ]

    # 执行合并
    merged_messages = merge_messages(messages)
    print(merged_messages)
