from pydantic import BaseModel, Field

class CCKnowledgeBaseCreateInput(BaseModel):
    knowledge_base_name: str = Field(default='samples', description='知识库名称')
    vector_store_type: str = Field(default='faiss', description='向量库类型')
    kb_info: str = Field(default='', description='知识库简介')
    embed_model: str = Field(default='bge-large-zh-v1.5', description='Embeddings模型')

class CCKnowledgeBaseSearchDocsInput(BaseModel):
    query: str = Field(default='')
    knowledge_base_name: str = Field(default='')
    top_k: int = Field(default=3)
    score_threshold: float = Field(default=2)
    file_name: str = Field(default='')
    metadata: dict = Field(default={})


class CCKnowledgeBaseDeleteDocsInput(BaseModel):
    knowledge_base_name: str = Field(title="知识库名称")
    file_names: list[str] = Field(default=[], title="文件名列表")
    delete_content: bool = Field(default=True, title="是否删除文件内容")
    not_refresh_vs_cache: bool = Field(default=True, title="暂不保存向量库(用于FAISS)")

class CCKnowledgeBaseUpdateInfoInput(BaseModel):
    knowledge_base_name: str = Field(title="知识库名称")
    kb_info: str = Field(default="",title="知识库简介")

class CCKnowledgeBaseDeleteInput(BaseModel):
    knowledge_base_name: str = Field(title="知识库名称")

class CCKnowledgeBaseUpdateDocsInput(BaseModel):
    knowledge_base_name: str = Field(..., title="知识库名称")
    file_names: list[str] = Field([], title="文件名列表")
    chunk_size: int = Field(default=750, title="知识库单段文本最大长度")
    chunk_overlap: int = Field(default=150, title="知识库单段文本重合长度")
    zh_title_enhance: bool = Field(default=False, title="是否开启中文标题增强")
    override_custom_docs: bool = Field(default=False, title="覆盖已有文件")
    docs: str = Field(default='', title="自定义的docs，需要为json字符串")
    not_refresh_vs_cache: bool = Field(default=False, title="暂不保存向量库(用于FAISS)")

class CCKnowledgeBaseRecreateVectorStoreInput(BaseModel):
    """根据content中文档重建向量库"""
    knowledge_base_name: str = Field(default='samples', title="知识库名称")
    allow_empty_kb: bool = Field(default=True, title="是否允许空知识库")
    vs_type: str = Field(default='faiss', title="向量库类型")
    embed_model: str = Field(default='bge-large-zh-v1.5', title="Embeddings模型")
    chunk_size: int = Field(default=750, title="单段文本最大长度")
    chunk_overlap: int = Field(default=150, title="文本段重合长度")
    zh_title_enhance: bool = Field(default=False, title="中文标题增强")
    not_refresh_vs_cache: bool = Field(default=False, title="暂不保存向量库(用于FAISS)")

class CCKnowledgeBaseQueryInput(BaseModel):
    knowledge_id: str
    query: str 
    top_k: int
    score_threshold: float

class CCKnowledgeBaseSummaryFileToVectorStoreInput(BaseModel):
    """单个知识库根据文件名称摘要"""
    knowledge_base_name: str = Field(default='', title="知识库名称")
    file_name: str = Field(default='', title="文件名")
    allow_empty_kb: bool = Field(default=True, title="是否允许空知识库")
    vs_type: str = Field(default='faiss', title="向量库类型")
    embed_model: str = Field(default='bge-large-zh-v1.5', title="Embeddings模型")
    file_description: str = Field(default='', title="文件描述")
    model_name: str = Field(default='', title="模型名称")
    temperature: float = Field(default=0.01, title="温度参数")
    max_tokens: int = Field(default=0, title="最大token数")

class CCKnowledgeBaseSummaryDocsIdsToVectorStoreInput(BaseModel):
    """单个知识库根据doc_ids摘要"""
    knowledge_base_name: str = Field(default='', title="知识库名称")
    doc_ids: list[str] = Field(default=[], title="文档ID列表")
    vs_type: str = Field(default='faiss', title="向量库类型")
    embed_model: str = Field(default='bge-large-zh-v1.5', title="Embeddings模型")
    file_description: str = Field(default='', title="文件描述")
    model_name: str = Field(default='', title="模型名称")
    temperature: float = Field(default=0.01, title="温度参数")
    max_tokens: int = Field(default=0, title="最大token数")

class CCKnowledgeBaseRecreateSummaryVectorStoreInput(BaseModel):
    """重建单个知识库文件摘要"""
    knowledge_base_name: str = Field(default='', title="知识库名称")
    allow_empty_kb: bool = Field(default=True, title="是否允许空知识库")
    vs_type: str = Field(default='faiss', title="向量库类型")
    embed_model: str = Field(default='bge-large-zh-v1.5', title="Embeddings模型")
    file_description: str = Field(default='', title="文件描述")
    model_name: str = Field(default='', title="模型名称")
    temperature: float = Field(default=0.01, title="温度参数")
    max_tokens: int = Field(default=0, title="最大token数")


class CCKnowledgeBaseSearchTempDocsInput(BaseModel):
    """检索临时知识库"""
    query: str = Field(default='')
    knowledge_id: str = Field(default='')
    top_k: int = Field(default=5)
    score_threshold: float = Field(default=0.8)