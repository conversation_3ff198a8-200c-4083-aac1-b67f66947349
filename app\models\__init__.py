from sqlalchemy.orm import DeclarativeBase
import datetime


class Base(DeclarativeBase):
    __abstract__ = True  # 声明为抽象基类，不会创建对应的数据库表

    def to_dict(self):
        data = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime.datetime):
                # 将 datetime 类型转换为字符串
                value = value.strftime('%Y-%m-%d %H:%M:%S')
            data[column.name] = value
        return data
