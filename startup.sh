#!/bin/bash

# 函数：输出日志信息
log_message() {
    echo "[Deploy Script] $1"
}

# 切换到 ai_client_server 环境
log_message "Activating conda environment: ai_client_server"
source activate ai_client_server

# 进入目录 /ai_client_server-bak/ai_client_server
log_message "Changing directory to /ai_client_server"
cd /ai_client_server


# 重置到上一个提交
log_message "Resetting Git repository to HEAD~"
git reset --hard HEAD~


# 如果有传入分支参数，则切换到该分支
if [ -n "$1" ]; then
    log_message "Switching to Git branch: $1"
    git checkout "$1"
fi



# 拉取最新代码
log_message "Pulling latest changes from Git"
git pull

# 修改 settings.toml 文件
log_message "Updating settings.toml file"
sed -i 's/port = 80/port = 8080/g' settings.toml
sed -i 's|http://*************:80|http://**************:8080|g' settings.toml
sed -i 's|ai_detection_platform_8080|ai_detection_platform_dev|g' settings.toml

# 检查 8080 端口是否被占用，如果被占用则 kill 掉相关进程
log_message "Checking if port 8080 is occupied and killing related processes if necessary"
lsof -ti :8080 | xargs kill -9 2>/dev/null

# 检查是否有 app.py 进程在运行，如果有则 kill 掉
#log_message "Checking for running app.py processes and killing if necessary"
ps -ef | grep 'python app.py' | grep -v grep | awk '{print $2}' | xargs kill -9

# 启动应用
log_message "Starting application (nohup python app.py &)"
nohup python app.py > nohup.out 2>&1 &
