"""
上下文感知的任务拆解优化方案
提供更智能的任务分解和上下文管理
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class TaskComplexity(Enum):
    """任务复杂度"""
    SIMPLE = 1      # 简单任务，1-2步
    MODERATE = 2    # 中等任务，3-5步  
    COMPLEX = 3     # 复杂任务，6-8步
    VERY_COMPLEX = 4 # 极复杂任务，需要分阶段

class TaskDomain(Enum):
    """任务领域"""
    RESEARCH = "research"           # 研究类
    ANALYSIS = "analysis"          # 分析类
    CREATION = "creation"          # 创作类
    COMPUTATION = "computation"    # 计算类
    COMMUNICATION = "communication" # 沟通类

@dataclass
class ContextualStep:
    """上下文感知的步骤"""
    step: int
    question: str
    call_function: str
    arguments: Dict[str, Any] = field(default_factory=dict)
    
    # 上下文相关
    required_context: List[str] = field(default_factory=list)  # 需要的上下文键
    context_weight: Dict[str, float] = field(default_factory=dict)  # 上下文权重
    output_keys: List[str] = field(default_factory=list)  # 输出的上下文键
    
    # 质量控制
    success_criteria: List[str] = field(default_factory=list)  # 成功标准
    validation_rules: List[str] = field(default_factory=list)  # 验证规则
    fallback_strategy: Optional[str] = None  # 失败后的备选策略

class ContextAwareDecomposer:
    """上下文感知的任务分解器"""
    
    def __init__(self):
        self.domain_patterns = self._init_domain_patterns()
        self.complexity_indicators = self._init_complexity_indicators()
        self.tool_capabilities = self._init_tool_capabilities()
    
    def _init_domain_patterns(self) -> Dict[TaskDomain, List[str]]:
        """初始化领域识别模式"""
        return {
            TaskDomain.RESEARCH: [
                r"搜索|查找|调研|研究|了解|收集.*信息",
                r"分析.*市场|竞品分析|行业分析",
                r"文献.*综述|论文.*查找"
            ],
            TaskDomain.ANALYSIS: [
                r"分析|解析|评估|比较|对比",
                r"数据.*分析|统计.*分析",
                r"性能.*分析|效果.*评估"
            ],
            TaskDomain.CREATION: [
                r"生成|创建|制作|编写|设计",
                r"报告|文档|方案|计划",
                r"代码|程序|脚本"
            ],
            TaskDomain.COMPUTATION: [
                r"计算|运算|求解|处理",
                r"数学|公式|算法",
                r"模拟|仿真"
            ],
            TaskDomain.COMMUNICATION: [
                r"发送|通知|邮件|消息",
                r"会议|讨论|沟通",
                r"展示|演示|汇报"
            ]
        }
    
    def _init_complexity_indicators(self) -> Dict[str, int]:
        """初始化复杂度指标"""
        return {
            # 关键词权重
            "多个": 2, "复杂": 3, "详细": 2, "全面": 3,
            "深入": 2, "系统": 3, "综合": 3, "完整": 2,
            
            # 动作词权重
            "分析": 2, "设计": 3, "开发": 4, "实现": 3,
            "优化": 3, "集成": 4, "部署": 3, "测试": 2,
            
            # 领域权重
            "算法": 3, "架构": 4, "系统": 3, "平台": 4,
            "框架": 3, "模型": 3, "数据库": 2, "网络": 3
        }
    
    def _init_tool_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """初始化工具能力映射"""
        return {
            "web_search": {
                "domains": [TaskDomain.RESEARCH],
                "output_types": ["text", "links", "summaries"],
                "context_keys": ["search_results", "web_content"],
                "avg_duration": 5
            },
            "knowledge_base_search": {
                "domains": [TaskDomain.RESEARCH, TaskDomain.ANALYSIS],
                "output_types": ["structured_data", "documents"],
                "context_keys": ["kb_results", "documents"],
                "avg_duration": 3
            },
            "code_execution": {
                "domains": [TaskDomain.COMPUTATION, TaskDomain.CREATION],
                "output_types": ["results", "files", "data"],
                "context_keys": ["execution_results", "generated_files"],
                "avg_duration": 10
            },
            "data_analysis": {
                "domains": [TaskDomain.ANALYSIS, TaskDomain.COMPUTATION],
                "output_types": ["insights", "charts", "statistics"],
                "context_keys": ["analysis_results", "visualizations"],
                "avg_duration": 8
            }
        }
    
    def analyze_task_complexity(self, question: str) -> TaskComplexity:
        """分析任务复杂度"""
        complexity_score = 0
        
        # 基于关键词计算复杂度
        for keyword, weight in self.complexity_indicators.items():
            if keyword in question:
                complexity_score += weight
        
        # 基于句子长度和结构
        sentences = question.split('。')
        complexity_score += len(sentences) * 0.5
        
        # 基于特殊标识符
        if '步骤' in question or '阶段' in question:
            complexity_score += 2
        if '并且' in question or '同时' in question:
            complexity_score += 1.5
        
        # 映射到复杂度等级
        if complexity_score <= 3:
            return TaskComplexity.SIMPLE
        elif complexity_score <= 6:
            return TaskComplexity.MODERATE
        elif complexity_score <= 10:
            return TaskComplexity.COMPLEX
        else:
            return TaskComplexity.VERY_COMPLEX
    
    def identify_task_domain(self, question: str) -> TaskDomain:
        """识别任务领域"""
        domain_scores = {}
        
        for domain, patterns in self.domain_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, question, re.IGNORECASE))
                score += matches
            domain_scores[domain] = score
        
        # 返回得分最高的领域
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        return TaskDomain.RESEARCH  # 默认领域
    
    def decompose_with_context(self, question: str) -> List[ContextualStep]:
        """基于上下文的任务分解"""
        complexity = self.analyze_task_complexity(question)
        domain = self.identify_task_domain(question)
        
        logger.info(f"任务复杂度: {complexity}, 领域: {domain}")
        
        # 根据复杂度确定步骤数量
        step_count = self._get_step_count(complexity)
        
        # 生成基础步骤
        base_steps = self._generate_base_steps(question, domain, step_count)
        
        # 增强上下文感知
        contextual_steps = self._enhance_with_context(base_steps, domain)
        
        # 添加质量控制
        final_steps = self._add_quality_control(contextual_steps)
        
        return final_steps
    
    def _get_step_count(self, complexity: TaskComplexity) -> int:
        """根据复杂度获取步骤数量"""
        step_ranges = {
            TaskComplexity.SIMPLE: (2, 3),
            TaskComplexity.MODERATE: (3, 5),
            TaskComplexity.COMPLEX: (5, 7),
            TaskComplexity.VERY_COMPLEX: (7, 10)
        }
        min_steps, max_steps = step_ranges[complexity]
        return min_steps + (max_steps - min_steps) // 2
    
    def _generate_base_steps(self, question: str, domain: TaskDomain, step_count: int) -> List[ContextualStep]:
        """生成基础步骤"""
        steps = []
        
        # 领域特定的步骤模板
        if domain == TaskDomain.RESEARCH:
            steps = self._generate_research_steps(question, step_count)
        elif domain == TaskDomain.ANALYSIS:
            steps = self._generate_analysis_steps(question, step_count)
        elif domain == TaskDomain.CREATION:
            steps = self._generate_creation_steps(question, step_count)
        elif domain == TaskDomain.COMPUTATION:
            steps = self._generate_computation_steps(question, step_count)
        else:
            steps = self._generate_generic_steps(question, step_count)
        
        return steps
    
    def _generate_research_steps(self, question: str, step_count: int) -> List[ContextualStep]:
        """生成研究类任务步骤"""
        steps = []
        
        # 第一步：信息收集
        steps.append(ContextualStep(
            step=1,
            question=f"收集关于'{question}'的基础信息",
            call_function="web_search",
            output_keys=["search_results", "basic_info"],
            success_criteria=["找到相关信息", "信息来源可靠"]
        ))
        
        # 第二步：深入研究
        if step_count > 2:
            steps.append(ContextualStep(
                step=2,
                question="深入分析收集到的信息",
                call_function="knowledge_base_search",
                required_context=["search_results"],
                context_weight={"search_results": 0.8},
                output_keys=["detailed_analysis", "key_insights"],
                success_criteria=["分析深入", "发现关键洞察"]
            ))
        
        # 最后一步：总结
        steps.append(ContextualStep(
            step=len(steps) + 1,
            question=f"基于研究结果回答：{question}",
            call_function="generate_summary",
            required_context=["search_results", "detailed_analysis"],
            context_weight={"search_results": 0.4, "detailed_analysis": 0.6},
            output_keys=["final_answer"],
            success_criteria=["回答完整", "逻辑清晰"]
        ))
        
        return steps
    
    def _generate_analysis_steps(self, question: str, step_count: int) -> List[ContextualStep]:
        """生成分析类任务步骤"""
        steps = []
        
        steps.append(ContextualStep(
            step=1,
            question="收集需要分析的数据",
            call_function="data_collection",
            output_keys=["raw_data"],
            success_criteria=["数据完整", "格式正确"]
        ))
        
        steps.append(ContextualStep(
            step=2,
            question="对数据进行预处理和清洗",
            call_function="data_preprocessing",
            required_context=["raw_data"],
            output_keys=["clean_data"],
            success_criteria=["数据质量良好", "无异常值"]
        ))
        
        steps.append(ContextualStep(
            step=3,
            question=f"执行分析：{question}",
            call_function="data_analysis",
            required_context=["clean_data"],
            output_keys=["analysis_results"],
            success_criteria=["分析结果可信", "支持结论"]
        ))
        
        return steps
    
    def _generate_creation_steps(self, question: str, step_count: int) -> List[ContextualStep]:
        """生成创作类任务步骤"""
        # 实现创作类步骤生成逻辑
        return []
    
    def _generate_computation_steps(self, question: str, step_count: int) -> List[ContextualStep]:
        """生成计算类任务步骤"""
        # 实现计算类步骤生成逻辑
        return []
    
    def _generate_generic_steps(self, question: str, step_count: int) -> List[ContextualStep]:
        """生成通用步骤"""
        # 实现通用步骤生成逻辑
        return []
    
    def _enhance_with_context(self, steps: List[ContextualStep], domain: TaskDomain) -> List[ContextualStep]:
        """增强上下文感知"""
        for i, step in enumerate(steps):
            if i > 0:
                # 添加对前序步骤的依赖
                prev_step = steps[i-1]
                step.required_context.extend(prev_step.output_keys)
                
                # 设置上下文权重
                for key in prev_step.output_keys:
                    step.context_weight[key] = 0.7  # 默认权重
        
        return steps
    
    def _add_quality_control(self, steps: List[ContextualStep]) -> List[ContextualStep]:
        """添加质量控制"""
        for step in steps:
            # 添加通用验证规则
            step.validation_rules.extend([
                "结果不为空",
                "格式正确",
                "内容相关"
            ])
            
            # 添加备选策略
            if not step.fallback_strategy:
                step.fallback_strategy = "使用简化版本重试"
        
        return steps

# 使用示例
def test_context_aware_decomposition():
    """测试上下文感知分解"""
    decomposer = ContextAwareDecomposer()
    
    question = "分析当前AI市场的发展趋势，并生成一份详细的市场分析报告"
    steps = decomposer.decompose_with_context(question)
    
    for step in steps:
        print(f"步骤 {step.step}: {step.question}")
        print(f"  工具: {step.call_function}")
        print(f"  需要上下文: {step.required_context}")
        print(f"  输出键: {step.output_keys}")
        print(f"  成功标准: {step.success_criteria}")
        print()

if __name__ == "__main__":
    test_context_aware_decomposition()
