import logging
import ahocorasick
from sqlalchemy import select, delete, func, update
from app.models.sensitive_words import SensitiveWords as SensitiveWordsModel
import os
from app.const.isdelete import STATUS_YES
from app.log import logger


class SensitiveWords():
    def __init__(self, db):
        self.db = db
        self.init_txt = False
        self.automaton = None
        self.sensitive_words = []

        # 初始化敏感词库
        # words_list = self.db.query(SensitiveWordsModel).filter(SensitiveWordsModel.deleted_at.is_(None)).filter(SensitiveWordsModel.status == STATUS_YES).all()
        # t = [row.to_dict() for row in words_list]
        # words = []
        # # 将SensitiveWords对象转换为字典列表
        # for item in words_list:
        #     words.append(item.name)
        # # 检查敏感词文件是否存在
        # if words is not None:
        #     # self.sensitive_words = self.load_sensitive_words('sensitive_words.txt')
        #     self.automaton = self.build_automaton(words)
        #     self.init_txt = True
        #     logging.info("初始化敏感词库成功")
        # else:
        #     logging.error("敏感词文件 sensitive_words.txt 不存在，请检查文件路径。")
        #     self.sensitive_words = []
        #     self.automaton = None

    def init(self):
        # 手动初始化敏感词，只调用一次
        words_list = self.db.query(SensitiveWordsModel)\
            .filter(SensitiveWordsModel.deleted_at.is_(None))\
            .filter(SensitiveWordsModel.status == STATUS_YES)\
            .all()
        words = [item.name for item in words_list]

        if words is not None:
            self.automaton = self.build_automaton(words)
            self.init_txt = True
            logger.info(f"敏感词初始化成功，共 {len(words)} 条")
        else:
            logger.error("敏感词文件 sensitive_words.txt 不存在，请检查文件路径。")
            self.sensitive_words = []
            self.automaton = None

    def build_automaton(self, sensitive_words):
        A = ahocorasick.Automaton()
        for idx, key in enumerate(sensitive_words):
            A.add_word(key, (idx, key))
        A.make_automaton()
        return A

    def load_sensitive_words(self, file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines()]

    def detect_sensitive_words(self, text):
        if self.init_txt:
            for end_index, (insert_order, original_value) in self.automaton.iter(text):
                return True,original_value
        return False, None


from app.db.mysql import get_session, get_db, Session

if __name__ == "__main__":
    with get_session() as db:
        sw = SensitiveWords(db)
        print(sw.detect_sensitive_words("你好，这是一段测试文本"))
        print(sw.detect_sensitive_words("孙文科"))
