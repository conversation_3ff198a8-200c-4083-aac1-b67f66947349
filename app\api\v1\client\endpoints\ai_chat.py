import json

from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from app.services.ai_tools.agent_factory import AgentFactory
from app.services.ai_tools.agent_model import AgentModel
from app.api.v1.client.schemas.ai_chat import ChatParams, CreateHtml
from sqlalchemy.orm import Session
from app.db import get_db
from app.api.response import APIResponse
from app.const.response import MessageCode
from app.const.agent_tools_const import AgentToolsConstants
from app.services.ai_chat_service import AiChatService
import os
import re

router = APIRouter()


class AiChatApi(object):

    @staticmethod
    @router.post("/completions")
    async def chat_completions(param: ChatParams, db: Session = Depends(get_db)) -> StreamingResponse:
        """

        param

            - query: str = Field(description="对话内容")
            - user_id: str = Field(description="用户id")
            - session_id: str = Field(description="会话id")
            - prompt: Optional[str] = Field(default="", description="系统提示词")
            - kb_name: Optional[str] = Field(default=None, description="知识库名称,如果type= knowledge_base_search必传")
            - knowledge_id: Optional[str] = Field(default=None, description="知识库名称,如果type= file_chat必传")
            - type: str = Field(default="completions", description="knowledge_base_search 知识库 ； completions 普通对话；file_chat 文件对话;agent_chat 工具对话")
            - agent_id: Optional[int] = Field(default=None,description="工具id，后续会动态维护，现在暂时固定 数据分析固定1")


        :return:
        """


        ai_chat_service = AiChatService()
        data = ai_chat_service.chat_complete(db=db, param=param.dict())
        # 将异步生成器包装在 StreamingResponse 中返回
        return StreamingResponse(data, media_type="application/json")

    @staticmethod
    @router.post("/create_code")
    def create_code(code: CreateHtml):

        """

        :param code:
        :return:
        """
        # file_path = '/html/echar.html'
        # # 创建目录
        # directory = os.path.dirname(file_path)

        def stip_html(s):
            # 去掉字符串前后的空格和换行符
            s = s.strip()
            # 检查字符串开头是否是 "html"
            if s.startswith("html"):
                # 如果是，则删除开头的 "html"
                s = s[4:]
            return s
        # 要替换成的新地址
        new_src = "./js/echarts.min.js"

        # 定义正则表达式，用于匹配包含任意版本号的 ECharts 引入 script 标签
        pattern = r'<script src="https://cdn\.jsdelivr\.net/npm/echarts@(?:latest|[\d.]+)/dist/echarts\.min\.js"></script>'

        # 执行替换操作
        replaced_html = re.sub(pattern, f'<script src="{new_src}"></script>', stip_html(code.code))
        return APIResponse({"code": replaced_html}, MessageCode.SUCCESS)

        # print(f"Directory to create: {directory}")  # 打印目录信息
        # if not os.path.exists(directory):
        #     print(f"Directory {directory} does not exist. Creating...")
        #     os.makedirs(directory)
        # try:
        #     # 打开文件以写入模式
        #     with open(file_path, 'w', encoding='utf-8') as file:
        #         # 写入 HTML 内容
        #         file.write(replaced_html)
        #     print(f"HTML 内容已成功写入到 {file_path}")
        #     return APIResponse({"path": file_path}, MessageCode.SUCCESS)
        #
        # except Exception as e:
        #     print(f"写入文件时发生错误: {e}")
        #     return APIResponse({"error": str(e)}, MessageCode.SYSTEMERR)
        #
        # except Exception as e:
        #     print(f"写入文件时发生错误: {e}")
        #     return APIResponse({"error": str(e)}, MessageCode.SYSTEMERR)




if __name__ == '__main__':
    html = """


<!DOCTYPE html>
<html>
<head>
    <title>产品销量统计</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <style>
        #chart-container {
            width: auto;
            height: auto;
            margin: auto;
        }
    </style>
</head>
<body>

<div id="chart-container" style="width:600px;height:400px;"></div>

<script type="text/javascript">
    // 基于准备好的dom,初始化echarts实例
    var myChart = echarts.init(document.getElementById('chart-container'));

    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '各产品类别销量'
        },
        tooltip: {},
        legend: {
            data:['销量']
        },
        xAxis: {
            data: ["手机","平板电脑","笔记本","智能手表"]
        },
        yAxis: {
            type:'value',
            name:'数量 (件)',
            minInterval:1,
            axisLabel:{
                formatter:function(value){
                    return value;
                }
            }
        },
        
       series:[{
           name:'销量',
           type:'bar',
           data:[120,85,150,90],
           itemStyle:{
               normal:{
                   color:'#B3CDE3'
               }
           }
       }]
       
       ,
       grid:{
           show:true,
           lineStyle:{
               color:'#ddd'
           }
       },
       toolbox:{
           show:true,
           feature:{
               magicType:{show:false},
               restore:{show:false},
               saveAsImage:{show:true}
           }
       },
       responsive:true
   };

   // 使用刚指定的配置项和数据显示图表。
   myChart.setOption(option);
   
   window.addEventListener('resize', function () {
      myChart.resize();
   });
</script>

</body>
</html>
    """
    create_cod1e = CreateHtml(code=html)
    t = AiChatApi.create_code(create_cod1e)
    print(t)