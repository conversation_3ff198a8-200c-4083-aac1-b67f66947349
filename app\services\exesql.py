import re
from copy import deepcopy
from openai import OpenAI
import pandas as pd
import pymysql
import psycopg2
import pyodbc
import asyncio

from app.log import logger as logging
from app.services.flow.manus_llm import ManusLLM


class LLMService:
    def __init__(self, model="YuanFang-Agent-Model", api_key="sk", base_url="http://***************:20000/v1"):
        self.model = model
        self.api_key = api_key
        self.base_url = base_url

    async def chat(self, prompt: str, question: str):
        messages = [{"role": "system", "content": prompt}, {"role": "user", "content": question}]
        return await ManusLLM(self.base_url, self.model).call_chat_mdl(messages)
        # client = OpenAI(api_key=self.api_key, base_url=self.base_url, timeout=30)
        # response = await client.chat.completions.create(model=self.model, messages=messages)  # type: ignore
        # return response.choices[0].message.content


class ExeSQLParam:
    def __init__(
        self,
        db_type="mysql",
        database="db",
        username="root",
        host="127.0.0.1",
        port=3306,
        password="",
        loop=3,
        top_n=30,
    ):
        self.db_type = db_type
        self.database = database
        self.username = username
        self.host = host
        self.port = port
        self.password = password
        self.loop = loop
        self.top_n = top_n


class ExeSQL:
    def __init__(self, param: ExeSQLParam, llm: LLMService):
        self._param = param
        self._llm = llm

    async def run(self, question, **kwargs):
        if self._param.db_type in ["mysql", "mariadb"]:
            db = pymysql.connect(
                db=self._param.database,
                user=self._param.username,
                host=self._param.host,
                port=self._param.port,
                password=self._param.password,
            )
        elif self._param.db_type == "postgresql":
            db = psycopg2.connect(
                dbname=self._param.database,
                user=self._param.username,
                host=self._param.host,
                port=self._param.port,
                password=self._param.password,
            )
        elif self._param.db_type == "mssql":
            conn_str = (
                r"DRIVER={ODBC Driver 17 for SQL Server};"
                r"SERVER=" + self._param.host + "," + str(self._param.port) + ";"
                r"DATABASE=" + self._param.database + ";"
                r"UID=" + self._param.username + ";"
                r"PWD=" + self._param.password
            )
            db = pyodbc.connect(conn_str)
        try:
            cursor = db.cursor()
        except Exception as e:
            raise Exception("Database Connection Failed! \n" + str(e))

        # 重新检查SQL语句是否正确
        last_req = question
        checked_ans = await self._recheck_sql(last_req, cursor, **kwargs)
        logging.debug(f"SQL statement: 更新后的答案: {checked_ans}")
        ans = self._refactor(checked_ans)
        logging.info(f"SQL statement: 更新后的sql: {ans}")

        input_list = re.split(r";", ans.replace(r"\n", " "))
        sql_res = []
        _retry = 0
        for i in range(len(input_list)):
            single_sql = input_list[i]
            while _retry <= self._param.loop:
                _retry += 1
                if not single_sql:
                    break
                try:
                    cursor.execute(single_sql)
                    if cursor.rowcount == 0:
                        sql_res.append({"content": "No record in the database!"})
                        break
                    if self._param.db_type == "mssql":
                        single_res = pd.DataFrame.from_records(
                            cursor.fetchmany(self._param.top_n), columns=[desc[0] for desc in cursor.description]  # type: ignore
                        )
                    else:
                        single_res = pd.DataFrame([i for i in cursor.fetchmany(self._param.top_n)])
                        single_res.columns = [i[0] for i in cursor.description]  # type: ignore
                    # sql_res.append({"content": single_res.to_markdown(index=False, floatfmt=".6f")})
                    # formatted_res = single_res.style.format(precision=4)  # 只对浮点数保留四位小数，整数格式不变
                    formatted_res = single_res.map(self._format_value)
                    # sql_res.append({"content": formatted_res.to_markdown(index=False)})
                    sql_res.append({"content": formatted_res.to_dict(orient='records')})

                    # sql_res.append({"content": formatted_res.to_markdown(index=False)})
                    break
                except Exception as e:
                    logging.error(f"Failed to execute SQL: {e}")
                    single_sql = await self._regenerate_sql(single_sql, str(e), **kwargs)
                    single_sql = self._refactor(single_sql)
                    if _retry > self._param.loop:
                        sql_res.append({"content": "Can't query the correct data via SQL statement."})
        db.close()
        # if not sql_res:
        #     return ExeSQL.be_output("")
        # result = "\n".join([i["content"] for i in sql_res])
        return sql_res

    async def _recheck_sql(self, last_req, cursor, **kwargs):
        table_summary, table_detail_info = self._fetch_mysql_table_data(cursor)
        prompt = f"""
        ## 你是SQL语句修复助手，请检查大模型的答案中的 SQL 语句是否正确。
        ## {"用户的原始问题是: "+last_req if last_req else ""}。
        ## 需要注意: 1.数据库的类型为:{self._param.db_type}; 2.当前连接的数据库的名称为: {self._param.database};
        ## 如果 SQL 语句正确则不修改, 如果大模型的答案中没有SQL语句,则根据答案中的信息重新生成一个SQL语句答案。
        ## 请你仅回复SQL语句答案。请不要给出任何解释，直接回复SQL语句代码即可。
        ## 已有的表名信息: {table_summary}。
        ## {table_detail_info}
        """
        prompt = prompt[:4000]
        kwargs_ = deepcopy(kwargs)
        kwargs_["stream"] = False
        try:
            response = await self._llm.chat(prompt, last_req)
            return response
        except Exception as e:
            logging.error(f"Failed to regenerate SQL: {e}")
            return None

    async def _regenerate_sql(self, failed_sql, error_message, **kwargs):
        prompt = f"""
        ## You are the Repair SQL Statement Helper, please modify the original SQL statement based on the SQL query error report.
        ## The original SQL statement is as follows:{failed_sql}.
        ## The contents of the SQL query error report is as follows:{error_message}.
        ## Answer only the modified SQL statement. Please do not give any explanation, just answer the code.
"""
        kwargs_ = deepcopy(kwargs)
        kwargs_["stream"] = False
        try:
            last_req = "根据提示词的描述, 回答问题"
            regenerated_sql = await self._llm.chat(prompt, last_req)
            return regenerated_sql
        except Exception as e:
            logging.error(f"Failed to regenerate SQL: {e}")
            return None

    def _format_value(self, x):
        if isinstance(x, float):
            return f"{x:.4f}"
        elif isinstance(x, int):
            return f"{x}"
        else:
            return str(x).replace("\n", "<br>")

    def _fetch_mysql_table_data(self, cursor):
        if self._param.db_type != "mysql":
            return ""
        sql_str = f"""
        SELECT 
            t.table_schema AS database_name,
            t.table_name,
            t.table_comment AS table_introduction,
            c.column_name,
            c.column_comment AS column_introduction
        FROM 
            INFORMATION_SCHEMA.TABLES t
        JOIN 
            INFORMATION_SCHEMA.COLUMNS c ON t.table_schema = c.table_schema AND t.table_name = c.table_name
        WHERE 
            t.table_schema = '{self._param.database}'
        ORDER BY 
            t.table_schema, t.table_name, c.ordinal_position;
        """
        try:
            table_info = {}
            cursor.execute(sql_str)
            res = cursor.fetchall()
            for row in res:
                table_name = row[1]
                table_desc = row[2]
                column_name = row[3]
                column_desc = row[4]
                key = (table_name, table_desc)
                if table_info.get(key) is None:
                    table_info[key] = []
                table_info[key].append((column_name, column_desc))

            table_summary_content = ""
            content = ""
            for t_key, t_info in table_info.items():
                table_summary_content += f"{t_key[0]},{t_key[1] if t_key[1] else ''}。"
                content += f"表名: {t_key[0]}, 表描述: {t_key[1]}。字段名称和描述: "
                for c_key, c_info in t_info:
                    content += f"{c_key},{c_info}\n"
                content += "\n"
            table_summary = f"下面是表名和表描述.表描述可能为空:\n {table_summary_content}。以上是表名和表描述信息"
            table_info_str = f"下面是已有的表结构详情:\n {content}。以上是已有的表结构详情"
            logging.debug(f"Table info length: {len(table_info_str)}")
            return table_summary, table_info_str
        except Exception as e:
            logging.warning(f"Failed to get table info: {e}")
            return "", ""

    def _refactor(self, ans):
        ans = ans if ans else ""
        ans = re.sub(r"<think>.*</think>", "", ans, flags=re.DOTALL)
        match = re.search(r"```sql\s*(.*?)\s*```", ans, re.DOTALL)
        if match:
            ans = match.group(1)  # Query content
            return ans
        else:
            print("no markdown")
        ans = re.sub(r"^.*?SELECT ", "SELECT ", (ans), flags=re.IGNORECASE)
        ans = re.sub(r";.*?SELECT ", "; SELECT ", ans, flags=re.IGNORECASE)
        ans = re.sub(r";[^;]*$", r";", ans)
        if not ans:
            raise Exception("SQL statement not found!")
        return ans


if __name__ == "__main__":
    # 需要安装 pip3 install  tabulate==0.9.0 psycopg2-binary==2.9.9 tabulate==0.9.0
    my_param = ExeSQLParam(
        db_type="mysql",
        database="yuanfang",
        username="root",
        host="***************",
        port=3306,
        password="infini_rag_flow",
        loop=3,
        top_n=30,
    )
    my_llm = LLMService(
        model="YuanFang-Agent-Model", api_key="sk", base_url="http://***************:20000/v1/chat/completions"
    )
    exe_sql = ExeSQL(my_param, my_llm)
    req = "查询表agent_tools中的前 2 条数据"
    loop = asyncio.get_event_loop()
    resp = loop.run_until_complete(exe_sql.run(question=req))
    print(f"问题: {req}\n\n结果:\n{resp}")
