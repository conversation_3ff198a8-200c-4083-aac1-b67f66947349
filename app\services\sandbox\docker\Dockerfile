FROM python:3.11-slim

# 更换为清华源，并安装中文字体、系统依赖和常用工具
RUN sed -i 's|deb.debian.org|mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && apt-get install -y --no-install-recommends \
    fonts-wqy-microhei \
    libpango-1.0-0 libpangocairo-1.0-0 libpango1.0-dev \
    libgdk-pixbuf-2.0-0 \
    libcairo2 libcairo2-dev \
    libffi-dev \
    libxml2 libxslt1.1 \
    libjpeg62-turbo \
    zlib1g \
    build-essential \
    curl wget telnet vim-tiny \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# 配置 pip 使用清华 PyPI 源
RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "index-url = https://pypi.tuna.tsinghua.edu.cn/simple" >> /root/.pip/pip.conf && \
    echo "trusted-host = pypi.tuna.tsinghua.edu.cn" >> /root/.pip/pip.conf

# 安装 Python 依赖
RUN pip install --no-cache-dir \
    weasyprint==65.1 \
    markdown==3.6 markdown-it-py==3.0.0 markdown-to-json==2.1.1 \
    pandas==2.2.3 numpy==1.26.4 \
    matplotlib==3.10.1 lxml==5.4.0\
    python-pptx==1.0.2 python-docx==1.1.2 \
    python-dateutil==2.8.2 pytz==2020.5 chardet==5.2.0 requests==2.32.2 orjson==3.10.15 \
    openpyxl==3.1.5 xlrd==2.0.1 xlsxwriter==3.2.2 \
    pillow==10.4.0  \
    aiohttp==3.11.13 \
    fastapi==0.115.12 

# 设置工作目录
WORKDIR /workspace

# 可选复制和启动命令
# COPY . .
# CMD ["python", "main.py"]
