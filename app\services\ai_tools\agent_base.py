import json
from sqlalchemy import select, delete, func, update

from app.const import ai_chat_list
from app.log import logger
from app.models.ai_chat_list import AiChatList
from app.const.isdelete import IS_DELETE, IS_NOT_DELETE
from app.const.agent_tools_const import AgentToolsConstants


class AgentBase:
    def __init__(self, db, params=[]):
        self.db = db
        self.params = params
        self.test = False # 本次测试的时候需要先打开开关。不然数据库里面查询不到数据

    def get_message(self,is_knowledge_base_search=False):
        # 获取message信息
        sql = select(AiChatList).filter(AiChatList.is_delete == IS_NOT_DELETE, AiChatList.session_id == self.params["session_id"])
        result = self.db.execute(sql)
        # 从结果中提取数据
        rows = result.scalars().all()
        message = []
        if  self.params["prompt"] != "":
            # 判断是否有AgentToolsConstants.AGENT_PROMPT[self.params["agent_id"]]是否存在，如果存在则进行赋值
                message.append({"role": "user", "content": self.params["prompt"]})
        # 判断rows是否为空,为空则返回
        if rows:
            for row in rows:
                if row.question:
                    message.append({"role": "user", "content": row.question})

                if row.answer:
                    if row.session_type == ai_chat_list.SESSION_TYPE_MULTI:
                        try:
                            answer_list = json.loads(row.answer)
                            if isinstance(answer_list, list) and answer_list:
                                first_answer = answer_list[0].get("answer")
                                if first_answer:
                                    message.append({"role": "assistant", "content": first_answer})
                        except (json.JSONDecodeError, IndexError, AttributeError, TypeError) as e:
                            logger.error(f"解析多模型 answer 错误：{e}, answer 参数：{row.answer}")
                            continue
                    else:
                        message.append({"role": "assistant", "content": row.answer})
        if self.test:
            message.append({"role": "user", "content": self.params["query"]})
        return message



    def get_params(self):
        return self.params

    def get_db(self):
        return self.db

    def get_agent_info(self):
        return {
            'params': self.params,
        }

    def get_agent_info_json(self):
        return json.dumps(self.get_agent_info())

    def __str__(self):
        return self.get_agent_info_json()

    def __repr__(self):
        return self.get_agent_info_json()


from app.db import get_session

if __name__ == '__main__':
    with get_session() as db:
        params = {"query": "我的上一个问题是什么"}
        t = AgentBase(db, params)
        f = t.get_message()
        print(f)
    pass
