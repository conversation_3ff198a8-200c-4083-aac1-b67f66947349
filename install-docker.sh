#!/bin/bash
# Docker CE 安装脚本（基于国内镜像，并使用 jammy 版本, bookworm版本不能用,离谱）
set -e
sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
sed -i 's|security.debian.org/debian-security|mirrors.aliyun.com/debian-security|g' /etc/apt/sources.list.d/debian.sources && \
# 1. 更新 apt 包索引并安装依赖
apt update
apt install -y apt-transport-https ca-certificates curl gnupg-agent software-properties-common

# 2. 添加 Docker 的 GPG 密钥（使用阿里云镜像）
curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | apt-key add -
# 3. 添加 Docker 软件仓库（使用阿里云源，强制使用 jammy 版本）
# 如存在旧的 bookworm 版本源，先删除它
rm -f /etc/apt/sources.list.d/archive_uri-https_mirrors_aliyun_com_docker-ce_linux_ubuntu-bookworm.list
# 写入新的仓库配置
echo "deb [arch=amd64] https://mirrors.aliyun.com/docker-ce/linux/ubuntu jammy stable" > /etc/apt/sources.list.d/docker.list
# 如果仓库文件中意外包含 bookworm，则替换为 jammy
sed -i 's/bookworm/jammy/g' /etc/apt/sources.list.d/docker.list
# 4. 再次更新 apt 包索引
apt update
# 5. 安装 Docker CE 及相关组件
apt install -y docker-ce
