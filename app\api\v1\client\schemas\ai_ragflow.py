from pydantic import BaseModel, Field


class RagFlowCreateDatasetAssistantInput(BaseModel):
    """创建知识库对话助手"""

    class LLMSettings(BaseModel):
        model_name: str | None = Field(default="", description="聊天模型名称")
        temperature: float = Field(default=0.6, description="控制模型预测的随机性")
        top_p: float = Field(default=0.96, description="核心抽样")
        presence_penalty: float = Field(
            default=0,
            description="通过惩罚对话中已经出现过的单词，阻止模型重复相同的信息",
        )
        frequency_penalty: float = Field(
            default=0, description="与存在惩罚类似，这会降低模型频繁重复相同单词的倾向"
        )
        # max_token: int = Field(
        #     default=512,
        #     description="The maximum length of the model's output, measured in tokens."
        # )

    class PromptSettings(BaseModel):
        # similarity_threshold: float = Field(
        #     default=0.2,
        #     description="Threshold for similarities between the user query and chunks."
        # )
        # keywords_similarity_weight: float = Field(
        #     default=0.7,
        #     description="Weight of keyword similarity in the hybrid similarity score."
        # )
        # top_n: int = Field(
        #     default=8,
        #     description="Number of top chunks fed to the LLM."
        # )
        # variables: list[dict] = Field(
        #     default_factory=lambda: [{"key": "knowledge", "optional": True}],
        #     description="Variables to use in the 'System' field of Chat Configurations."
        # )
        # rerank_model: str | None = Field(
        #     default=None,
        #     description="If specified, reranking score will be used; otherwise, vector cosine similarity will be used."
        # )
        # top_k: int = Field(
        #     default=1024,
        #     description="Top-k value for reordering or selecting items based on ranking."
        # )
        empty_response: str = Field(
            default="",
            description="Response when nothing is retrieved in the dataset for the user's question.",
        )
        # opener: str = Field(
        #     default="Hi! I am your assistant, can I help you?",
        #     description="The opening greeting for the user."
        # )
        show_quote: bool = Field(default=True, description="是否显示文本来源")
        prompt: str = Field(
            default="""你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据详细回答。当所有知识库内容都与问题无关时，利用大模型的回答。回答需要考虑聊天历史。
                以下是知识库：
                {knowledge}
                以上是知识库。""",
            description="The prompt content for the LLM.",
        )

    name: str = Field(default="", description="知识库名称")
    dataset_ids: list[str] = Field(default=[], description="关联数据集的ID")
    llm: LLMSettings = Field(
        default_factory=LLMSettings, description="要创建的聊天助手的 LLM 设置"
    )
    prompt: PromptSettings = Field(
        default_factory=PromptSettings, description="LLM 需要遵循的说明"
    )


class RagFlowCreateAssistantSessionInput(BaseModel):
    """创建Assistant会话"""

    chat_id: str = Field(description="CHAT ID")
    name: str = Field(description="session名称")
    user_id: str = Field(default=None, description="USER ID")


class RagFlowCreateDatasetAndAssistantAndSessionInput(BaseModel):
    """创建一个新的知识库,一个新的助手，一个新的会话"""

    pass


class RagFlowCreateAgentSessionInput(BaseModel):
    """创建AGENT会话"""

    agent_id: str = Field(description="AGENT ID")
    user_id: str = Field(default=None, description="USER ID")


class RagFlowRegisterInput(BaseModel):
    """注册接口"""

    nickname: str = Field(default="默认用户", description="注册名")
    email: str = Field(description="邮箱")
    password: str = Field(description="密码")


class RagFlowLoginInput(BaseModel):
    """登录接口"""

    email: str = Field(description="邮箱")
    password: str = Field(description="密码")


class RagFlowUpdatePasswordInput(BaseModel):
    """更新密码接口"""

    email: str = Field(description="邮箱")
    password: str = Field(description="旧密码")
    new_password: str = Field(description="新密码")


class RagFlowGenerateQuestionsInput(BaseModel):
    """生成问题接口"""

    type: str = Field(description="生成问题的类型:类型范围: [agent, knowledge_base]")
    id: str = Field(description="当type的agent时, id为agent的id。其他类型id同理。")
    num: int = Field(default=3, description="生成的问题数量", gt=0, le=10)
    extra_msg: str = Field(default="", description="附加信息")
