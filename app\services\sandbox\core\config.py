from pydantic import BaseModel, Field
from app.log import logger
from app.config.config import settings

SANDBOX_IMAGE = settings.get("manus", {}).get("sandbox_image", "sandbox:0.0.1")  # type: ignore

SANDBOX_BASENAME = "sandbox_."

logger.info(f"Sandbox default image name: {SANDBOX_IMAGE}")


def build_sandbox_name(suffix: str) -> str:
    return f"{SANDBOX_BASENAME}{suffix}"


class SandboxSettings(BaseModel):
    """Configuration for the execution sandbox"""

    use_sandbox: bool = Field(False, description="Whether to use the sandbox")
    image: str = Field(SANDBOX_IMAGE, description="Base image")
    work_dir: str = Field("/workspace", description="Container working directory")
    memory_limit: str = Field("512m", description="Memory limit")
    cpu_limit: float = Field(1.0, description="CPU limit")
    timeout: int = Field(300, description="Default command timeout (seconds)")
    network_enabled: bool = Field(True, description="Whether network access is allowed")
