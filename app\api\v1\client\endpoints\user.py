import re
from datetime import datetime

from fastapi import APIRouter, Request, Security

from app.api.v1.client.schemas.user import UserLoginInput
from app.const.user import SYS_ADMIN, EMAIL_REGEX, TEL_REGEX
from app.log import logger

from app.api.response import APIResponse
from app.const.response import MessageCode
from app.utils import create_jwt_token

router = APIRouter()


class UserClientApi(object):
    @staticmethod
    @router.post("/login")
    async def user_login(data: UserLoginInput, request: Request):
        return

