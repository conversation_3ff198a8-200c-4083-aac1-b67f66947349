import json
import datetime
import sqlalchemy.exc
from sqlalchemy.orm import Session

from app.models.ai_mcp import AiMcp
from app.models.ai_mcp_list import AiMcpList
from app.log import logger


class AiMcpListCurd():

    def __init__(self, db: Session):
        self.db = db

    # 批量插入到AiMcpList表中
    def add_mcp(self, mcp_list, mcp_config):
        # 验证输入是否为列表
        if not isinstance(mcp_list, list):
            raise ValueError("mcp_list参数必须是一个列表")
        # 验证列表中的每个元素是否包含必要的键
        for mcp in mcp_list:
            if not isinstance(mcp, dict):
                raise ValueError("mcp_list中的元素必须是字典")
            if 'name' not in mcp:
                raise ValueError("每个MCP项必须包含name")
        try:
            # 添加ai_mcp条件数据
            self.db.add(mcp_config)
            self.db.flush()
            mcp_id = mcp_config.id
            # 准备批量插入的数据
            current_time = datetime.datetime.now()
            mcp_tools_list = [
                AiMcpList(
                    name=mcp["name"],
                    type=2,
                    mcp_id=mcp_id,
                    schema=json.dumps(mcp["inputSchema"]),
                    ctime=current_time,
                    mtime=current_time,
                    desc=mcp.get("description", ""),
                )
                for mcp in mcp_list
            ]
            # 批量插入到数据库
            self.db.add_all(mcp_tools_list)
            self.db.commit()
            # 返回成功插入的记录数量
            return {"total":len(mcp_tools_list)}

        except sqlalchemy.exc.SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise ValueError(f"批量插入MCP列表失败: {e}") from e
        except ValueError as e:
            self.db.rollback()
            logger.error(f"输入验证失败: {e}")
            raise ValueError(f"输入验证失败: {e}") from e
        except Exception as e:
            self.db.rollback()
            logger.error(f"发生未知错误: {e}")
            raise RuntimeError(f"添加MCP服务器列表时发生未知错误") from e

    def delete_mcp(self, mcp_id: int):
        # 根据mcp_id删除 mcp记录和mcp_list记录
        try:
            self.db.query(AiMcp).filter_by(id=mcp_id).delete()
            self.db.query(AiMcpList).filter_by(mcp_id=mcp_id).delete()
            self.db.commit()
            return {"id": mcp_id}
        except sqlalchemy.exc.SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise ValueError(f"删除MCP失败: {e}") from e
        except Exception as e:
            self.db.rollback()
            logger.error(f"发生未知错误: {e}")
            raise RuntimeError(f"删除MCP时发生未知错误") from e
