import asyncio

from fastapi import APIRouter, Form, Request
from pydantic import Field

from app.api.response import APIResponse
from app.api.v1.client.schemas.ai_ragflow import (
    RagFlowCreateAgentSessionInput,
    RagFlowCreateAssistantSessionInput,
    RagFlowCreateDatasetAndAssistantAndSessionInput,
    RagFlowCreateDatasetAssistantInput,
    RagFlowGenerateQuestionsInput,
    RagFlowLoginInput,
    RagFlowRegisterInput,
    RagFlowUpdatePasswordInput,
)
from app.const.response import MessageCode
from app.services.ragflow.ragflow import RagFlowService


router = APIRouter()


class RagflowApi:

    @staticmethod
    @router.get("/dataset/list")
    async def get_dataset_list(page: int = 1, page_size: int = 30, name: str = None, dataset_id: str = None):
        """
        获取知识库列表
        """
        try:
            data = await RagFlowService().dataset_list(page, page_size, name, dataset_id)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.get("/agent/list")
    async def get_agent_list(page: int = 1, page_size: int = 30, name: str = None, agent_id: str = None):
        """
        获取agent列表
        """
        try:
            data = await RagFlowService().agent_summary(page, page_size, name, agent_id)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/dataset/assistant")
    async def create_dataset_assistant(data: RagFlowCreateDatasetAssistantInput):
        """
        创建知识库对话助手

        输入参数:

            - name: str = Field(default='', description='助手名称（不能重复）')
            - dataset_ids: list[str] =  Field(default=[], description='关联数据集的ID')
            - prompt: {
                    "prompt": "你是一个智能聊天机器人，请优先在附件中搜索答案，请列举附件中的数据详细回答，如果知识库没有内容或答案，则根据你的逻辑来回答。以下是附件：{knowledge} 以上是附件",
                    "empty_response": "",
                    "opener": "我是您的AI聊天助手",
                    "variables": [{"key": "knowledge", "optional": True}],
                },
            - 其余默认值就可以了
        """
        try:
            data = await RagFlowService().create_dataset_assistant(data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/dataset/assistant/session")
    async def create_dataset_assistant_session(data: RagFlowCreateAssistantSessionInput):
        """
        创建知识库对话助手

        输入参数:

                chat_id: str = Field(description="CHAT ID")
                user_id: str = Field(default=None, description="USER ID")


        """
        try:
            data = await RagFlowService().create_assistant_session(data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/dataset/assistant/session/file/upload_and_parse")
    async def dataset_file_upload_and_parse(request: Request):
        """
        上传文件到对话知识库，并解析文件。如果dataset_id为空，则创建一个新的知识库。

        输入参数：

            - dataset_id: str = Field(description="知识库 ID", default=None)
            - 其他配置参数: Content-Type为multipart/form-data
            eg: curl -X 'POST' \
                --url http://***************:8000/v1/ragflow/dataset/assistant/session/file/upload_and_parse \
                -H 'Content-Type: multipart/form-data' \
                -H 'Authorization: Bearer ragflow-U5ZGRkZjRhZmE4NjExZWZhOGVlMDI0Mm' \
                -F 'dataset_id=6c903da820b011f096089ac87589bd52' \
                -F 'file=@/Users/<USER>/Downloads/aa2.txt' \
                -F 'file=@/Users/<USER>/Downloads/aa3.txt'
        """
        try:
            data = await RagFlowService().dataset_file_upload_and_parse(request)  # type: ignore
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/agent/session")
    async def create_agent_session(
        request: Request,
    ):
        """
        创建AGENT会话

        输入参数：

            - agent_id: str = Field(description="AGENT ID")
            - user_id: str = Field(description="USER ID")
            - 其他配置参数: http://**************:81/user-setting/api#create-session-with-agent
              1. 当开始项(Begin component)项中没有文件时, Content-Type为application/json, 请求体为【配置项参数】。
              2. 当开始项(Begin component)项中有文件时, Content-Type为multipart/form-data, 请求体为【配置项参数】。
              3. 【配置项参数】的数据来自于前端表单中的数据: resp["dsl"]["components"]["begin"]["obj"]["params"]["query"])中的数据(key为键，value为值)
            eg: 配置项中有文件时：
                curl -X 'POST' \
                    'http://***************:8000/v1/ragflow/agent/session' \
                    -H 'Content-Type: multipart/form-data' \
                    -F 'agent_id=84a834140ec611f0a0b8beb1b0841f2d' \
                    -F 'is_chinese=true' \
                    -F 'num=2' \
                    -F 'line_1=333' \
                    -F 'hello=optional' \
                    -F 'ph=北京' \
                    -F 'file_1=@~/Downloads/song.jpg'    
        """
        try:
            data = await RagFlowService().create_agent_session(request)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/user/register")
    async def register(data: RagFlowRegisterInput):
        """
        注册接口

        输入参数:

            - nickname: str = Field(description="注册名")
            - email: str = Field(description="邮箱")
            - password: str = Field(description="密码")
        """
        try:
            await RagFlowService().register(data)
            resp = {"api_key": "待实现"}
            return APIResponse(resp)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/user/login")
    async def login(req_data: RagFlowLoginInput):
        """
        登录接口

        输入参数:

            - email: str = Field(description="邮箱")
            - password: str = Field(description="密码")
        """
        try:
            data = await RagFlowService().login(req_data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/user/update_password")
    async def update_password(req_data: RagFlowUpdatePasswordInput):
        """
        更新密码接口
        """
        try:
            data = await RagFlowService().update_password(req_data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post("/chat/generate_questions")
    async def generate_questions(req_data: RagFlowGenerateQuestionsInput):
        """
        基于智能体或知识库生成问题接口
        """
        try:
            data = await RagFlowService().generate_questions(req_data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)


if __name__ == "__main__":
    req_data = RagFlowLoginInput(
        email="<EMAIL>",
        password="H1cOwiuHaK+e24uPOr0GajHraNBf4mp2NoIWn1X/+7Ys2Lhyt7ld5FuYD9LmvbE9GJFqpE1qQsFjVw5rirw6JUGWNIhNsMzELblSzNANnQsFPa2AW8F8Sjn54lTsQYCIeMgeljxkGeu9TTx5NWQr9uu5vENY0t/eu7p/sZ7ATytcysfMO0I7VfJrl8ZlFlxVplvYFIddNev1pxXQdP92HYsI74dLQ1k7USL1Ex6g4mz68AjZvFz1kvB4pHST0jtzsgbRAUW9K1X1R7MVtZ62EugUFjpvC4YCk1QQNIju5LO0p04Gory4lHI4bEQtSDQIeBc8h5kYQrmmJFaCFs2rxg==",
    )

    print(req_data)
    data = asyncio.run(RagFlowService().login(req_data))
    print(data)
