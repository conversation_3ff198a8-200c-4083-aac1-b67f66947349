from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime

from app.models import Base


class AiChatList(Base):
    """用户操作日志"""
    __tablename__ = 'ai_chat_list'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    question: Mapped[str] = mapped_column(Text, comment="提问")
    answer: Mapped[str] = mapped_column(Text, comment="答案")
    params: Mapped[str] = mapped_column(Text, comment="具体请求参数")
    type: Mapped[str] = mapped_column(Text, comment="会话类型")
    session_id: Mapped[str] = mapped_column(Text, nullable=False, comment="会话id")
    session_type: Mapped[int] = mapped_column(BOOLEAN, default=False, comment="会话类型，0：普通会话 1：多模型会话")
    is_delete: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否删除：0 未删除， 1 删除')
    ctime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="创建时间",
                                       server_default=text("CURRENT_TIMESTAMP"))
    mtime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="修改时间",
                                       server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'question': self.question,
            'answer': self.answer,
            'params': self.params,
            'type': self.type,
            'is_delete': self.is_delete,
            "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
            "mtime": self.mtime.strftime("%Y-%m-%d %H:%M:%S"),
        }
