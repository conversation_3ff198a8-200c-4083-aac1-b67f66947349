from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

from app.middleware.auth import AuthMiddleware
from app.middleware.asgi_logger import AccessLoggerMiddleware
from app.middleware.exception_request import ExceptionHandlingMiddleware
from app.api.lifespan import lifespan
from app.config.config import settings

from app.router.v1 import router as v1_router
from app.router.v2 import router as v2_router


# 定义应用
app = FastAPI(lifespan=lifespan)

# 添加中间件
app.add_middleware(AccessLoggerMiddleware)
app.add_middleware(ExceptionHandlingMiddleware)
if settings.license.enable:  # type: ignore
    app.add_middleware(AuthMiddleware)


# 挂载路由
app.include_router(v1_router)
app.include_router(v2_router)


# 生成 OpenAPI schema
def custom_openapi():
    openapi_schema = get_openapi(
        title="Custom Swagger UI",
        # version="1.0.0",
        description="This is a custom Swagger UI",
        routes=app.routes,
        # 添加版本字段
        version="3.0.0",  # 或者 "3.0.n"，根据需要选择
    )

    return openapi_schema


# 将 custom_openapi 函数设置为应用的 OpenAPI schema 生成器
app.openapi = custom_openapi


__all__ = ["app"]
