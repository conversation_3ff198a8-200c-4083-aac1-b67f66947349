import os
import json
from urllib.parse import quote
import traceback
import async<PERSON>
from fastapi import APIRouter
from sse_starlette import EventSourceResponse

from app.const.manus_const import Prompt
from app.services.flow.message_builder import MessageBuilder
from app.services.flow.manus_llm import ManusLL<PERSON>
from fastapi import APIRouter, BackgroundTasks
from fastapi.responses import StreamingResponse
from app.api.v1.client.schemas.ai_manus import CreateTaskV2, SupplTask
from app.log import logger as logging
from app.services.flow.step_chain_builder import StepChainBuilder

router = APIRouter()


@staticmethod
@router.post("/task_v2/create_task")
async def create_task_by_question(req: CreateTaskV2, background_tasks: BackgroundTasks):

    step_chain_builder = StepChainBuilder()
    prompt = Prompt()
    # 定义异步生成器函数
    async def stream_generator():

        if req.step_chain is not None:
            step_chain = json.loads(req.step_chain)
            async for chunk in step_chain_builder.fix_task(step_chain, req.suppl_answer, req.suppl_info):
                yield json.dumps(chunk, ensure_ascii=False) + "\n"

        else:
            step_chain = {}
            async for chunk in step_chain_builder.create_task_chian(req.question):
                yield json.dumps(chunk, ensure_ascii=False) + "\n"
                if chunk.get("event")  ==prompt.Even_0["id"]:
                    step_chain = chunk.get("content")
                    break

        async for chunk in step_chain_builder.get_tool_map(req.question, step_chain, req.suppl_answer):
            yield json.dumps(chunk, ensure_ascii=False) + "\n"

    return EventSourceResponse(stream_generator(), ping=60 * 60 * 24)  # 前端会收到ping消息，暂时设置为24小时
    # return StreamingResponse(stream_generator(), media_type="application/json")


if __name__ == "__main__":
    # 配置日志

    # 创建测试数据
    task_list = json.dumps([
  {
    "step": "阶段1",
    "title": "明确PPT需求与内容规划",
    "todo": [
      "确定PPT的主题和目标受众。",
      "梳理PPT需要包含的核心内容和逻辑结构。",
      "规划PPT的整体风格和视觉元素。",
      "搜集与PPT主题相关的资料、图片、数据等。"
    ],
    "suppl_info": "请明确PPT的具体主题、目标受众以及希望达到的效果。例如，是用于产品演示、学术报告、培训分享还是其他用途？PPT大致需要多少页？"
  },
  {
    "step": "阶段2",
    "title": "制作PPT框架与大纲",
    "todo": [
      "根据阶段1规划的内容，搭建PPT的整体框架。",
      "为每个主要部分拟定详细的大纲，明确每页幻片的具体内容。",
      "确定每个部分的页数分配。",
      "初步设计PPT的封面和目录页。"
    ],
    "suppl_info": ""
  }
])
    suppl_answer = "目标受众是软件开发工程师、技术团队新成员；期望效果是让受众理解代码功能、逻辑和使用场景；页数预计 15 页。"
    suppl_info = "请明确PPT的具体主题、目标受众以及希望达到的效果。例如，是用于产品演示、学术报告、培训分享还是其他用途？PPT大致需要多少页？"

    # 创建请求对象
    req = SupplTask(
        task_list=task_list,
        suppl_answer=suppl_answer,
        suppl_info=suppl_info,
        question="请根据提供的信息生成一个详细的PPT"
    )

    # 运行异步测试
    async def suppl_task1():
        background_tasks = BackgroundTasks()
        # response = suppl_task(req, background_tasks)
        # async for chunk in response:
        #     print(json.dumps(chunk))
        # 收集流式响应内容

    # 执行测试
    asyncio.run(suppl_task1())
