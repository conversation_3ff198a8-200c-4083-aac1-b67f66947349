from typing import List, Dict, Optional
import json
from app.log.log import logger

class MessageBuilder:
    def __init__(self):
        self.messages: List[Dict[str, str]] = []

    def add_message(self,
                    role: str,
                    content: str,
                    validate_role: bool = True,
                    position: Optional[int] = None) -> 'MessageBuilder':
        """
        添加单条消息到消息列表

        参数:
            role: 消息角色，应为 'system', 'user', 'assistant' 之一
            content: 消息内容
            validate_role: 是否验证角色有效性
            position: 消息插入位置，None表示追加，0表示开头

        返回:
            返回自身实例，支持链式调用
        """
        if validate_role:
            valid_roles = {'system', 'user', 'assistant'}
            if role not in valid_roles:
                raise ValueError(f"角色必须是 {valid_roles} 中的一个，当前值: {role}")

        message = {"role": role, "content": content}
        if position is None:
            self.messages.append(message)
        else:
            self.messages.insert(position, message)
        return self

    def build_messages(self,
                       user_input: Optional[str] = None,
                       answer: Optional[str] = None,
                       system_prompt: Optional[str] = None) -> 'MessageBuilder':
        """
        构建消息列表

        参数:
            user_input: 用户输入内容
            answer: 助手回复内容
            system_prompt: 系统提示内容

        返回:
            返回自身实例，支持链式调用
        """
        # 重置消息列表
        self.clear_messages()

        # 按顺序添加消息
        if system_prompt:
            self.add_message("system", system_prompt)
        if user_input:
            self.add_message("user", user_input)
        if answer:
            self.add_message("assistant", answer)

        return self

    def add_system_message_at_beginning(self, content: str, validate_role: bool = True) -> 'MessageBuilder':
        """
        在消息列表开头添加system消息

        参数:
            content: 消息内容
            validate_role: 是否验证角色有效性

        返回:
            返回自身实例，支持链式调用
        """
        return self.add_message("system", content, validate_role, 0)

    def get_messages(self) -> List[Dict[str, str]]:
        """获取当前消息列表的副本"""
        logger.info(f"当前message信息为：{json.dumps(self.messages)}")
        return self.messages.copy()

    def clear_messages(self) -> 'MessageBuilder':
        """清空所有消息"""
        self.messages = []
        return self

    def remove_system_messages(self) -> 'MessageBuilder':
        """移除所有system类型的消息"""
        self.messages = [msg for msg in self.messages if msg["role"] != "system"]
        return self

    def remove_message_at_index(self, index: int) -> 'MessageBuilder':
        """
        移除指定位置的消息

        参数:
            index: 消息索引位置

        异常:
            IndexError: 当索引超出范围时抛出
        """
        if 0 <= index < len(self.messages):
            del self.messages[index]
        else:
            raise IndexError(f"消息索引超出范围，有效范围: 0-{len(self.messages) - 1}")
        return self

    def replace_system_message(self, new_content: str) -> 'MessageBuilder':
        """
        替换或添加system消息，并确保它位于消息列表的第一个位置

        参数:
            new_content: 新的system消息内容
        """
        # 先移除所有system消息
        self.remove_system_messages()

        # 在开头添加新的system消息
        self.add_message("system", new_content, position=0)

        return self