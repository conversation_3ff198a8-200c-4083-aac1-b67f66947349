import os
import time
import logging
from io import BytesIO
from minio import Minio
from minio.error import S3Error
from minio.commonconfig import Filter
from minio.lifecycleconfig import LifecycleConfig, Rule, Expiration
from app.db.redis_sessions import singleton
from app.config.config import settings


_YF_AGENT_FILE_BASE_URL = "/file"


def get_manus_file_url():
    return os.path.join("/v1/ai_manus", f"{_YF_AGENT_FILE_BASE_URL.lstrip('/')}")


@singleton
class MinioClient:
    def __init__(self):
        self.conn = None
        self.__open__()

    def __open__(self):
        try:
            if self.conn:
                self.__close__()
        except Exception:
            pass
        host: str = settings.minio.host  # type: ignore
        try:
            self.conn = Minio(
                host,
                access_key=settings.minio.user,  # type: ignore
                secret_key=settings.minio.password,  # type: ignore
                secure=False,
            )
        except Exception:
            logging.exception("Fail to connect %s " % host)

    def __close__(self):
        del self.conn
        self.conn = None

    def put(self, bucket, fnm, binary):
        for _ in range(3):
            try:
                if not self.conn.bucket_exists(bucket):
                    self.conn.make_bucket(bucket)

                r = self.conn.put_object(bucket, fnm, BytesIO(binary), len(binary))
                return r
            except Exception:
                logging.exception(f"Fail to put {bucket}/{fnm}:")
                self.__open__()
                time.sleep(1)
        raise

    def rm(self, bucket, fnm):
        try:
            self.conn.remove_object(bucket, fnm)
        except Exception:
            logging.exception(f"Fail to remove {bucket}/{fnm}:")
        raise

    def get(self, bucket, filename):
        for _ in range(1):
            try:
                r = self.conn.get_object(bucket, filename)
                return r.read()
            except Exception:
                logging.exception(f"Fail to get {bucket}/{filename}")
                self.__open__()
                time.sleep(1)
        raise

    @staticmethod
    def get_manus_files_bucket_name() -> str:
        return settings.minio.bucket_name  # type: ignore

    def set_agent_files_bucket_lifecycle_until_success(self):
        while True:
            try:
                return self.set_agent_files_bucket_lifecycle()
            except Exception as e:
                logging.warning(f"Failed: {e}")
                time.sleep(2)

    def set_agent_files_bucket_lifecycle(self, days_to_expire=7):
        # set_agent_files_bucket_lifecycle
        """
        创建带有生命周期规则的存储桶
        :param bucket_name: 存储桶名称
        :param days_to_expire: 对象过期天数，默认为7天
        :return: None
        """
        bucket_name = self.get_manus_files_bucket_name()
        for _ in range(3):  # 保持与其他方法一致的重试逻辑
            try:
                # 创建存储桶
                if not self.conn.bucket_exists(bucket_name):
                    self.conn.make_bucket(bucket_name)
                    logging.info(f"存储桶 {bucket_name} 已创建")
                else:
                    logging.info(f"存储桶 {bucket_name} 已存在")

                # 定义生命周期规则
                config = LifecycleConfig(
                    rules=[
                        Rule(
                            status="Enabled",
                            rule_id=f"expire-{bucket_name}-objects",
                            rule_filter=Filter(prefix=""),  # 匹配所有对象
                            expiration=Expiration(days=days_to_expire),
                        )
                    ]
                )
                # 应用生命周期规则
                self.conn.set_bucket_lifecycle(bucket_name, config)
                logging.debug(f"已为存储桶 {bucket_name} 设置生命周期规则：{days_to_expire} 天后自动删除文件")
                return
            except S3Error as e:
                logging.error(f"设置元方Agent文件存储桶: 设置生命周期规则失败: {e}")
                if e.code == "NoSuchBucket":
                    continue  # 如果桶不存在则重试
                raise
            except Exception as e:
                logging.exception(f"设置元方Agent文件存储桶: 操作失败: {e}")
                self.__open__()
                time.sleep(0.2)
                continue
        raise Exception(f"设置元方Agent文件存储桶: 无法为存储桶 {bucket_name} 设置生命周期规则")


minio_client = MinioClient()


if __name__ == "__main__":
    import uuid

    print(f"host: {settings.MINIO['host']}")  # type: ignore
    conn = MinioClient()
    _test_bucket_name = conn.get_manus_files_bucket_name()
    # 测试生命周期功能
    conn.set_agent_files_bucket_lifecycle(days_to_expire=1)
    # 原有测试代码
    fnm = "/Users/<USER>/codebase/rag-server/rag/yf/yf_minio_client.py"
    from PIL import Image

    # 将字符串转换为字节流（MinIO 需要 bytes 或 file-like 对象）
    _data = "Hello, MinIO 3!"
    _data_bytes = _data.encode("utf-8")
    # _data_stream = BytesIO(_data_bytes)

    _file_name = "/test/" + uuid.uuid4().hex[:16] + ".txt"
    r1 = conn.put(_test_bucket_name, _file_name, _data_bytes)
    print(111, r1.location, r1.object_name, r1.version_id)
    bts = conn.get(_test_bucket_name, _file_name.lstrip("/"))
    print(222, bts.decode("utf-8"))
