import datetime
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import Integer, String, TIMESTAMP, text, BOOLEAN, Text
from app.models import Base


class SwitchModelLog(Base):
    """模型切换日志"""

    __tablename__ = "switch_model_log"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, comment="操作人"
    )
    form_model: Mapped[str] = mapped_column(
        String(length=255), nullable=False, default="", comment="原模型"
    )
    to_model: Mapped[str] = mapped_column(
        String(length=255), nullable=False, default="", comment="切换后的模型"
    )
    status: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="模型切换状态 1:切换中 2:成功 3:失败",
    )
    log: Mapped[str] = mapped_column(Text, nullable=False, comment="切换日志")
    created_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP,
        nullable=False,
        comment="创建时间",
        server_default=text("CURRENT_TIMESTAMP"),
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP,
        nullable=False,
        comment="修改时间",
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "form_model": self.form_model,
            "to_model": self.to_model,
            "status": self.status,
            "log": self.log,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
        }
