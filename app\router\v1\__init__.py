from fastapi import APIRouter, Security

from app.api.v1.client.endpoints import (
    ai_chat,
    ai_knowledge_base,
    health,
    ai_ragflow,
    container,
    server_info,
    llm_manager,
    ai_manus,
    ai_manus_mcp,
    ai_manus_v2

)

router = APIRouter()
router.include_router(ai_chat.router, prefix="/v1", tags=["ai_chat"])
router.include_router(health.router, prefix="/v1", tags=["health"])
router.include_router(
    ai_knowledge_base.router, prefix="/v1/knowledge_base", tags=["knowledge_base"]
)
router.include_router(ai_ragflow.router, prefix="/v1/ragflow", tags=["ragflow"])
router.include_router(container.router, prefix="/v1/container", tags=["container"])
router.include_router(server_info.router, prefix="/v1/server", tags=["server"])
router.include_router(
    llm_manager.router, prefix="/v1/llm_manager", tags=["llm_manager"]
)
router.include_router(ai_manus.router, prefix="/v1/ai_manus", tags=["ai_manus"])
router.include_router(ai_manus_v2.router, prefix="/v1/ai_manus_v2", tags=["ai_manus_v2"])
router.include_router(ai_manus_mcp.router, prefix="/v1/mcp", tags=["ai_manus_mcp"])
