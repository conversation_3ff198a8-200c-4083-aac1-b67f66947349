from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime

from app.models import Base


class AiMcpList(Base):
    """用户操作日志"""
    __tablename__ = 'ai_mcp_list'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(Text, comment="mcp名称")
    desc: Mapped[str] = mapped_column(Text, comment="描述")
    type: Mapped[int] = mapped_column(Text, comment="mcp类型，1 内网 2 外网服务'")
    mcp_id: Mapped[int] = mapped_column(Integer, comment="关联mcp的id")
    schema: Mapped[str] = mapped_column(Integer, comment="请求参数")
    ctime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="创建时间",
                                       server_default=text("CURRENT_TIMESTAMP"))
    mtime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="修改时间",
                                       server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "desc": self.desc,
            "type": self.type,
            "mcp_id": self.mcp_id,
            "schema": self.schema,
            "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
            "mtime": self.mtime.strftime("%Y-%m-%d %H:%M:%S"),
        }
