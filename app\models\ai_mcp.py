from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import <PERSON><PERSON><PERSON>, String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime

from app.models import Base


class AiMcp(Base):
    """用户操作日志"""
    __tablename__ = 'ai_mcp'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(Text, comment="mcp名称")
    desc: Mapped[str] = mapped_column(Text, comment="描述")
    url: Mapped[str] = mapped_column(Text, comment="mcp名称")
    config: Mapped[str] = mapped_column(Text, comment="具体请求参数")
    enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        server_default=text("1"),
        comment="开启或关闭状态，True表示开启，False表示关闭",
    )
    ctime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="创建时间",
                                       server_default=text("CURRENT_TIMESTAMP"))
    mtime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="修改时间",
                                       server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "desc": self.desc,
            "url": self.url,
            "config": self.config,
            "enabled": self.enabled,
            "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
            "mtime": self.mtime.strftime("%Y-%m-%d %H:%M:%S"),
        }
