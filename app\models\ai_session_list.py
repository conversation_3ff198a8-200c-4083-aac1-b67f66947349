from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime

from app.models import Base


class AiSessionList(Base):
    """用户操作日志"""
    __tablename__ = 'ai_session_list'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    session_name: Mapped[str] = mapped_column(String(length=200), comment="会话名称")
    session_id: Mapped[str] = mapped_column(Text, nullable=False, comment="会话id")
    type: Mapped[str] = mapped_column(String(length=50), nullable=False, comment="会话类型")
    is_delete: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否删除：0 未删除， 1 删除')
    ctime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="创建时间",
                                       server_default=text("CURRENT_TIMESTAMP"))
    mtime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="修改时间",
                                       server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self):
        return {
            'id': self.id,
            'session_name': self.session_name,
            'session_id': self.session_id,
            'is_delete': self.is_delete,
            'type': self.type,
            "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
            "mtime": self.mtime.strftime("%Y-%m-%d %H:%M:%S"),
        }
