"""
CREATE TABLE `task` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` VARCHAR(50) NOT NULL COMMENT '任务标题',
  `step_chain` LONGTEXT COMMENT '步骤链',
  `step_chain_answer` LONGTEXT NULL COMMENT '步骤链的大模型的答案',
  `message` LONGTEXT COMMENT '历史聊天记录',
  `status` INT NOT NULL DEFAULT 0 COMMENT '任务状态: 0=UNSTARTED, 1=RUNNING, 2=FINISHED, 3=FAILED, 4=CANCELLED',
  `type` INT NOT NULL DEFAULT 1 COMMENT '任务类型: 1=AUTO, 2=MANUAL',
  `user_id` INT DEFAULT NULL COMMENT '用户ID',
  `log` LONGTEXT NULL COMMENT 'json格式日志',
  `input_and_output` LONGTEXT NULL COMMENT 'json格式输入输出',
  `stat` LONGTEXT NULL COMMENT 'json格式统计信息',
  `workspace` VARCHAR(100) COMMENT '工作目录路径',
  `sanbox_id` VARCHAR(32) DEFAULT NULL COMMENT '沙盒ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_created_at_user_id` (`created_at`, `user_id`),
  KEY `idx_status_user_id` (`status`, `user_id`),
  KEY `idx_title_user_id` (`title`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

"""

import json
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import Integer, String, Text, TIMESTAMP, text, Index
from app.models import Base


class Task(Base):
    """任务表"""

    __tablename__ = "task"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(50), nullable=False, comment="任务标题")
    step_chain: Mapped[str] = mapped_column(Text, comment="步骤链")
    step_chain_answer: Mapped[str] = mapped_column(Text, nullable=True, comment="生成步骤链的大模型的答案")
    message: Mapped[str] = mapped_column(Text, comment="历史聊天记录")
    status: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        server_default=text("0"),
        comment="任务状态: 0=UNSTARTED, 1=RUNNING, 2=FINISHED, 3=FAILED, 4=CANCELLED",
    )
    type: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        server_default=text("1"),
        comment="任务类型: 1=AUTO, 2=MANUAL",
    )
    user_id: Mapped[int] = mapped_column(Integer, nullable=True, comment="用户ID，默认为NULL")
    log: Mapped[str] = mapped_column(Text, nullable=True, comment="json格式日志")
    input_and_output: Mapped[str] = mapped_column(Text, nullable=True, comment="json格式输入输出")
    stat: Mapped[str] = mapped_column(Text, nullable=True, comment="json格式统计信息")
    workspace: Mapped[str] = mapped_column(String(100), comment="工作目录路径")
    sanbox_id: Mapped[str] = mapped_column(String(32), nullable=True, comment="沙盒ID，默认为NULL")
    created_at: Mapped[str] = mapped_column(
        TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at: Mapped[str] = mapped_column(
        TIMESTAMP,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )

    __table_args__ = (
        Index("idx_created_at_user_id", "created_at", "user_id"),
        Index("idx_status_user_id", "status", "user_id"),
        Index("idx_title_user_id", "title", "user_id"),
        {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"},
    )

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "step_chain": json.loads(self.step_chain) if self.step_chain else None,
            "step_chain_answer": self.step_chain_answer,
            "status": self.status,
            "type": self.type,
            "user_id": self.user_id,
            "workspace": self.workspace,
            "sanbox_id": self.sanbox_id,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),  # type: ignore
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S"),  # type: ignore
        }
