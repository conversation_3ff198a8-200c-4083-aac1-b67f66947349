# AI Agent 真实系统测试指南

## 🎯 测试目标

本测试套件专门用于验证AI Agent系统的**任务编排准确性**和**工具调用准确性**的优化效果，通过真实的API调用和实际的系统组件来进行测试。

### 核心测试指标

1. **任务编排准确性**
   - 步骤分解的合理性
   - 步骤数量的准确性
   - 任务复杂度识别
   - 上下文传递效果

2. **工具调用准确性**
   - 组件选择的正确性
   - 参数生成的准确性
   - 工具执行成功率
   - 错误处理能力

## 🚀 快速开始

### 1. 环境准备

确保你的系统满足以下要求：

```bash
# 1. API服务器运行
# 确保你的AI Agent API服务器运行在 http://localhost:8081

# 2. 模型服务器配置
# 模型服务器: http://**************:8000/v1
# 模型名称: /home/<USER>/.cache/modelscope/hub/models/google/medgemma-27b-text-it/

# 3. Python依赖
pip install aiohttp asyncio
```

### 2. 一键运行测试

```bash
# 运行交互式测试
python run_real_tests.py
```

测试将提供4种模式：
- **快速测试**: 基础功能验证 (3个测试用例)
- **标准测试**: 完整功能测试 (6个测试用例)
- **深度对比测试**: 优化效果对比 (4个专项测试)
- **全面测试**: 所有测试 + 综合分析

## 📋 测试用例设计

### 基础功能测试用例

#### 1. 简单知识库查询
```
问题: "TCT细胞药品有哪些？"
期望组件: ["知识库查询"]
测试目标: 验证单一工具调用的准确性
```

#### 2. 搜索后分析
```
问题: "搜索最新的人工智能发展趋势，然后分析其对医疗行业的影响"
期望组件: ["搜索引擎查询", "数据分析"]
测试目标: 验证多步骤任务编排
```

#### 3. 代码计算任务
```
问题: "用Python计算斐波那契数列的前10项，并生成可视化图表"
期望组件: ["执行代码"]
测试目标: 验证计算类任务处理
```

#### 4. 复杂多步骤任务
```
问题: "查询TCR-T治疗相关信息，搜索最新研究进展，分析治疗效果数据，最后生成一份综合报告"
期望组件: ["知识库查询", "搜索引擎查询", "数据分析", "普通对话"]
测试目标: 验证复杂任务分解和编排
```

### 专项对比测试用例

#### 1. 重试逻辑测试
```
问题: "查询不存在的数据库表test_nonexistent_table的用户信息"
测试目标: 验证错误处理和重试机制
关键指标: 是否出现无限循环，错误恢复能力
```

#### 2. 工具选择准确性测试
```
问题: "我需要查询TCT细胞药品的信息，然后搜索相关的最新研究进展"
测试目标: 验证工具选择算法的准确性
关键指标: 是否正确选择知识库查询和搜索引擎
```

#### 3. 任务分解准确性测试
```
问题: "分析用户注册数据：首先查询最近30天的注册用户数量，然后计算增长率，最后生成可视化图表"
测试目标: 验证复杂任务的分解能力
关键指标: 步骤数量、组件选择、逻辑顺序
```

#### 4. 上下文感知测试
```
问题: "先查询TCR-T治疗的基本信息，然后基于查询结果搜索相关的临床试验数据"
测试目标: 验证上下文传递和利用能力
关键指标: 第二步是否利用第一步的结果
```

## 📊 评分标准

### 准确性评分算法

每个测试用例的准确性分数由以下部分组成：

1. **基础成功率 (30%)**
   - 任务是否成功执行完成
   - 是否产生有效结果

2. **步骤数量合理性 (25%)**
   - 实际步骤数是否在期望范围内
   - 步骤分解的逻辑性

3. **组件使用准确性 (30%)**
   - 必须使用的组件是否被正确调用 (20%)
   - 应该使用的组件是否被合理选择 (10%)

4. **结果质量 (15%)**
   - 最终结果的长度和完整性
   - 结果的相关性和有用性

### 优化效果对比指标

| 指标类别 | 优化前估计值 | 测试目标值 | 改进目标 |
|----------|-------------|-----------|----------|
| 任务成功率 | 75% | 90%+ | +15% |
| 工具选择准确率 | 60% | 85%+ | +25% |
| 任务分解准确率 | 55% | 80%+ | +25% |
| 错误恢复率 | 30% | 70%+ | +40% |
| 平均执行时间 | 45s | 30s | -33% |

## 🔧 测试结果分析

### 输出文件说明

测试完成后会生成以下文件：

1. **test_execution.log** - 详细执行日志
2. **real_system_test_report.json** - 基准测试报告
3. **comprehensive_optimization_report.json** - 综合优化报告
4. **test_results_[mode]_[timestamp].json** - 特定模式测试结果

### 关键指标解读

#### 任务编排准确性指标
- **步骤数量准确率**: 实际步骤数与期望步骤数的匹配度
- **复杂度处理能力**: 不同复杂度任务的处理效果
- **逻辑顺序正确性**: 步骤间的依赖关系是否合理

#### 工具调用准确性指标
- **组件选择准确率**: 选择正确组件的比例
- **参数生成正确性**: 工具参数的准确性
- **执行成功率**: 工具调用的成功比例

### 性能基准对比

```json
{
  "优化前": {
    "任务成功率": "75%",
    "工具选择准确率": "60%", 
    "平均执行时间": "45秒",
    "错误恢复率": "30%"
  },
  "优化后": {
    "任务成功率": "90%+",
    "工具选择准确率": "85%+",
    "平均执行时间": "30秒",
    "错误恢复率": "70%+"
  }
}
```

## 🐛 故障排除

### 常见问题

1. **API服务器连接失败**
   ```
   ❌ API服务器: 连接失败 - Connection refused
   ```
   **解决方案**: 
   - 检查API服务器是否在 `localhost:8081` 运行
   - 确认防火墙设置
   - 检查服务器日志

2. **模型服务器异常**
   ```
   ⚠️ 模型服务器: HTTP 404
   ```
   **解决方案**:
   - 验证模型服务器地址: `http://**************:8000/v1`
   - 检查模型是否正确加载
   - 确认网络连接

3. **测试用例执行超时**
   ```
   任务执行超时
   ```
   **解决方案**:
   - 检查系统负载
   - 增加超时时间设置
   - 简化测试用例

4. **组件调用失败**
   ```
   组件 'xxx' 执行失败
   ```
   **解决方案**:
   - 检查组件配置
   - 验证依赖服务状态
   - 查看详细错误日志

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 测试报告示例

### 成功的测试输出
```
🎉 AI Agent 系统优化效果综合分析
============================================================

📈 关键性能改进:
  成功率: 75.0% → 91.7% (提升 16.7%)
  执行时间: 42.3s → 28.7s (改善 32.1%)
  准确率: 68.0% → 87.5% (提升 19.5%)

🎯 优化效果评估:
  任务编排准确性: 88.2%
  工具调用准确性: 91.3%
  综合优化分数: 89.8%
```

### 需要改进的测试输出
```
⚠️ 发现需要改进的问题:
  - 工具选择准确率偏低 (67%)
  - 复杂任务分解需要优化
  - 错误恢复机制有待加强

💡 改进建议:
  1. 优化工具选择算法的语义匹配
  2. 增强复杂任务的分解逻辑
  3. 完善错误分类和恢复策略
```

## 🎯 测试最佳实践

1. **测试前准备**
   - 确保系统环境稳定
   - 清理之前的测试数据
   - 检查所有依赖服务

2. **测试执行**
   - 从快速测试开始
   - 逐步进行更复杂的测试
   - 记录异常情况

3. **结果分析**
   - 关注关键指标趋势
   - 对比优化前后差异
   - 识别改进机会

4. **持续改进**
   - 定期运行测试
   - 根据结果调整优化策略
   - 扩展测试用例覆盖

## 📞 技术支持

如果在测试过程中遇到问题：

1. 查看 `test_execution.log` 获取详细错误信息
2. 检查生成的JSON报告文件
3. 参考故障排除部分
4. 根据测试结果调整系统配置

---

**开始测试**: `python run_real_tests.py`

通过这套真实测试，你可以准确评估AI Agent系统优化的实际效果！
