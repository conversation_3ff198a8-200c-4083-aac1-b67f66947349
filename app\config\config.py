import os
from pathlib import Path
from dynaconf import Dynaconf
from dynaconf.loaders.toml_loader import write


def get_settings(settings_file: str='settings.toml',secrets_file: str='.secrets.toml') -> Dynaconf:
    files = [settings_file]
    if Path(secrets_file).exists():
        files.append(secrets_file)
    return Dynaconf(
        envvar_prefix="DYNACONF",
        settings_files=files,
    )


def set_settings(config: dict, settings_file: str='settings.toml'):
    write(settings_file, config, merge=True)

# `envvar_prefix` = export envvars with `export DYNACONF_FOO=bar`.
# `settings_files` = Load these files in the order.


settings = get_settings(os.environ.get('settings_file', 'settings.toml'))


assert settings.service.bind
assert settings.service.port
assert settings.mysql.host
assert settings.mysql.port
assert settings.mysql.user
assert settings.mysql.passwd
assert settings.mysql.dbname
assert settings.log.level
assert settings.log.write_file
assert settings.log.path
assert settings.auth.seckey
assert settings.auth.algorithm
assert settings.path.icons
assert settings.path.file_path
assert settings.chatapi.api
