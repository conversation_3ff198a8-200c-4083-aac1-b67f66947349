import asyncio
from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager

from app.log import logger
from app.config import ASYNC_DB_PATH
from app.models import Base
from app.db.asyncmysql import async_engine
from app.db.minio_client import minio_client
from app.utils.sensitiva_words import SensitiveWords


@asynccontextmanager
async def lifespan(app: FastAPI):
    # ---- 初始化应用 ----
    logger.info(f"项目启动中...")
    # 设置桶文件的生命周期
    minio_client.set_agent_files_bucket_lifecycle_until_success()
    while True:
        try:
            async with async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
        except Exception as e:
            logger.exception(f"初始化数据库'{ASYNC_DB_PATH}'错误: {str(e)}")
            await asyncio.sleep(5)
            # raise
        else:
            logger.info(f"初始化数据库成功: {ASYNC_DB_PATH}!")
            break
    yield
