import ahocorasick

def build_automaton(sensitive_words):
    A = ahocorasick.Automaton()
    for idx, key in enumerate(sensitive_words):
        A.add_word(key, (idx, key))
    A.make_automaton()
    return A

def load_sensitive_words(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f.readlines()]

def detect_sensitive_words(text, automaton):
    for end_index, (insert_order, original_value) in automaton.iter(text):
        return True
    return False

# 加载敏感词文件
sensitive_words = load_sensitive_words('sensitive_words.txt')

# 构建自动机
automaton = build_automaton(sensitive_words)

# 待检测的文本
text = "nihao"

# 检测是否存在敏感词
if detect_sensitive_words(text, automaton):
    print("文本中存在敏感词")
else:
    print("文本中不存在敏感词")