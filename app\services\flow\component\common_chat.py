from app.services.flow import logger, now_ts
from app.services.flow.component.base import ComponentBase
from app.services.flow.namespace import FlowNamespace


class CommonChat(ComponentBase):
    function_call = "普通对话"
    component_name = "common_chat"
    desc = "通过大模型deep-seek 需要问题答案。"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        super().__init__(namespace, **kwargs)

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """处理普通对话请求"""
        start_ts = now_ts()
        user_input = step["question"]
        context_display = self.get_last_context(step_result_list, max_length=200)
        log_msg = f"进入普通对话处理流程: {user_input} 当前问题的上下文: {context_display}"
        logger.info(log_msg)
        yield self.namespace.add_log(log_msg, step)
        # history = self.get_last_chat_content(step_result_list)
        # if history is not None:
        #     mess = f"提问: {user_input} 需要整理的数据：{context}"
        # else:
        #     mess = user_input
        # 构造对话模型的请求参数
        dialogue_messages = [
            {
                "role": "system",
                "content": "你是一个智能助手，负责处理用户的普通对话请求。可以进行闲聊、解答常见问题等。",
            },
            *self.get_last_chat_content(step_result_list),
            {"role": "user", "content": user_input},
        ]
        while attempt_count > 0:
            try:
                success, response = await self.call_llm(dialogue_messages)
                if not success:
                    result = "抱歉，当前对话功能暂时出现故障，请稍后再试。"
                    attempt_count -= 1
                    continue
                else:
                    clean_response = self.filter_think_tags(response)
                    result = (
                        clean_response if clean_response else "抱歉，我暂时没有理解你的意思，可以再详细说明一下吗？"
                    )
                break
            except Exception as e:
                attempt_count -= 1
                logger.error(f"普通对话处理失败: {e}")
                result = "抱歉，当前对话功能暂时出现故障，请稍后再试。"

        formatted_input = user_input + f"\n上下文: {context_display[:256]}"
        yield self.save_input_and_output(formatted_input, result, start_ts, step)
        yield result


if __name__ == "__main__":
    import json

    namespace = FlowNamespace()
    coder = CommonChat(namespace)
    step = {
        "step": "1",
        "question": "中国官方统计的民族数量是多少？",
        "call_function": "普通对话",
        "component_name": CommonChat.component_name,
        "component_id": f"{CommonChat.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = coder.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
