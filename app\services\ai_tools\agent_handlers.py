import json
from typing import AsyncGenerator
import re
import logging
import uuid
import time
from app.log import logger
from app.utils.http import AsyncHTTPRequest
from app.services.ai_tools.agent_base import AgentBase
from app.config import settings
from app.utils import sensitive_words_handler
from app.services.ai_tools.agent_search import SearchXngEngine

# 这里把 `register_handler` 移到类外部
handler_registry = {}  # 作为全局变量存储所有注册的处理函数


def register_handler(key):
    """用于注册处理函数的装饰器"""

    def decorator(func):
        handler_registry[key] = func  # 直接存到全局 `handler_registry`
        return func

    return decorator


class ChatHandlers:
    chat_url = settings.chatchat.api
    ragflow_url = settings.ragflow.api_base
    dialog_id = settings.ragflow.dialog_id
    api_key = settings.ragflow.token
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }

    def __init__(self) -> None:
        self.__need_think = False

    async def ai_search(self,db, param) -> AsyncGenerator[str, None]:
        """AI联网搜索"""
        SearchEngine = SearchXngEngine()
        # 获取ai搜索相关结果，这里直接使用自己本地的模型去请求的。不走ragflow
        request =  SearchEngine.perform_search(param["query"])
        combined_output = ""
        async for data in request:
            result, combined_output, has_sensitive = await self.format_reason_content_stream(
                data["data"], combined_output, None,True,data["search_result"]
            )
            if result is None:
                return
            yield result
            if has_sensitive:
                break
        # logger.info("开始第二次输出")
        # request = SearchEngine.perform_search(param["query"])
        # combined_output = ""
        # async for data in request:
        #     result, combined_output, has_sensitive = await self.format_reason_content_stream(
        #         data["data"], combined_output, None, True, data["search_result"]
        #     )
        #     if result is None:
        #         return
        #     yield result
        #     if has_sensitive:
        #         break

    @register_handler("knowledge_base_search")
    async def knowledge_base_search(self, db, param) -> AsyncGenerator[str, None]:
        """
        :param param:  知识库查询参数，具体可以查看请求参数内容
        :return:
        """
        # # 获取上下文内容
        # agent_base = AgentBase(db, param)
        # # 拼接上下文内容
        # messages = agent_base.get_message()
        # # 部分参数先固定在函数保持不变
        # param["messages"] = messages
        param["question"] = param["query"]
        chat_id = param.pop("chat_id", None)  # 知识库临时使用chat_id，其实生成的是助手
        # url = f"{self.ragflow_url}/api/v1/chats_openai/{chat_id}/chat/completions"
        url = f"{self.ragflow_url}/api/v1/chats/{chat_id}/completions"
        request = AsyncHTTPRequest("POST", url, headers=self.headers, params=param)
        combined_output = ""

        async for data in request():
            result, combined_output, has_sensitive = await self.format_reason_content_stream(
                data, combined_output, True
            )
            if result is None:
                return
            yield result
            if has_sensitive:
                break

    @register_handler("file_chat")
    async def file_chat(self, db, param) -> AsyncGenerator[str, None]:
        """
        RagFlow, 文件聊天
        :param param:  知识库查询参数，具体可以查看请求参数内容
        :return:
        """
        # 获取上下文内容
        agent_base = AgentBase(db, param)
        # 拼接上下文内容
        messages = agent_base.get_message()
        # 部分参数先固定在函数保持不变
        param["messages"] = messages
        chat_id = param["chat_id"]  # 知识库临时使用chat_id，其实生成的是助手
        url = f"{self.ragflow_url}/api/v1/chats_openai/{chat_id}/chat/completions"

        request = AsyncHTTPRequest("POST", url, headers=self.headers, params=param)
        combined_output = ""

        async for data in request():
            # 如果是流式对话，需要判断是否需要继续思考，并且把后续内容追加到新reason_content字段中
            if param["stream"]:
                result, combined_output, has_sensitive = await self.format_reason_content_stream(
                    data, combined_output, should_check_reference=True
                )
                yield result
                if has_sensitive:
                    break
            else:
                # 获取 content 字段的值并判断直接是否data["choices"][0]["message"]["content"]是否报错
                if "choices" not in data or not data["choices"]:
                    # 抛出异常
                    raise Exception("模型返回异常,请检查模型是否启动")
                # 使用正则把 data["choices"][0]["message"]["content"]的think字段放到reason_content字段中
                content = data["choices"][0]["message"]["content"]
                # 使用正则表达式提取 <think> 标签内的内容
                pattern = r"<think>(.*?)</think>"
                matches = re.findall(pattern, content, re.DOTALL)
                reason_content = "".join(matches)
                # 从 content 中移除 <think> 标签及其内容
                content = re.sub(pattern, "", content, flags=re.DOTALL)
                # 更新数据
                data["choices"][0]["message"]["content"] = content
                data["choices"][0]["message"]["reason_content"] = reason_content
                yield data

    @register_handler("file_chat_cc")
    async def file_chat_cc(self, db, param) -> AsyncGenerator[str, None]:
        """
        ChatChat, 文件聊天
        :param param:  知识库查询参数，具体可以查看请求参数内容
        :return:
        """
        # 获取上下文内容
        agent_base = AgentBase(db, param)
        # 拼接上下文内容
        messages = agent_base.get_message()
        file_params = dict()
        # 部分参数先固定在函数保持不变
        file_params["query"] = param["query"]
        file_params["prompt_name"] = ""
        file_params["knowledge_id"] = param["knowledge_id"]
        file_params["stream"] = param["stream"]
        # file_params["max_tokens"] = param["max_tokens"]
        file_params["model_name"] = param["model"]
        file_params["history"] = messages
        file_params["top_k"] = 3
        file_params["prompt_name"] = "default"
        file_params["score_threshold"] = 2
        #
        url = f"{self.chat_url}/chat/file_chat"
        request = AsyncHTTPRequest("POST", url, headers=self.headers, params=file_params)
        combined_output = ""
        async for data in request():
            result, combined_output, has_sensitive = await self.format_reason_content_stream(
                data, combined_output, file_params
            )
            yield result
            if has_sensitive:
                break

    @register_handler("agent_chat")
    async def agent_chat(self, db, param) -> AsyncGenerator[str, None]:
        """
        :param param:  知识库查询参数，具体可以查看请求参数内容
        :return:
        """
        # 获取上下文内容
        agent_base = AgentBase(db, param)
        # 拼接上下文内容
        messages = agent_base.get_message()
        file_params = dict()
        # 部分参数先固定在函数保持不变
        file_params["question"] = param["query"]
        file_params["session_id"] = param["session_id"]
        file_params["stream"] = True
        agent_id = param["agent_id"]
        url = f"{self.ragflow_url}/api/v1/agents/{agent_id}/completions"
        request = AsyncHTTPRequest(
            "POST", url, headers=self.headers, params=file_params
        )
        combined_output = ""
        async for data in request():
            is_agent_event, event_msg = self.check_agent_event_stream(data)
            if is_agent_event:
                resp = self.get_default_openai_response()
                resp["id"] = uuid.uuid4().hex
                resp["choices"][0]["delta"]["content"] = ""
                resp["choices"][0]["delta"]["reason_content"] = ""
                resp["choices"][0]["delta"]["sensitive_words"] = ""
                resp["created"] = int(time.time())
                resp["is_agent_running"] = True
                resp["node_event"] = event_msg
                logger.debug(f"Agent resp: {resp}")
                yield resp
            else:
                result, combined_output, has_sensitive = await self.format_reason_content_stream(
                    data, combined_output, file_params
                )
                yield result
                if has_sensitive:
                    break

    @register_handler("completions")
    # openai根式对话
    async def completions(self, db, param) -> AsyncGenerator[str, None]:
        # 获取上下文内容
        agent_base = AgentBase(db, param)
        # 拼接上下文内容
        messages = agent_base.get_message()
        url = (
            f"{self.ragflow_url}/api/v1/chats_openai/{self.dialog_id}/chat/completions"
        )
        param["messages"] = messages
        request = AsyncHTTPRequest("POST", url, headers=self.headers, params=param)
        combined_output = ""
        async for data in request():
            # 如果是流式对话，需要判断是否需要继续思考，并且把后续内容追加到新reason_content字段中
            if param["stream"]:

                result, combined_output, has_sensitive = (
                    await self.format_reason_content_stream(data, combined_output)
                )
                yield result
                if has_sensitive:
                    break
            else:
                # 获取 content 字段的值并判断直接是否data["choices"][0]["message"]["content"]是否报错
                if "choices" not in data or not data["choices"]:
                    # 抛出异常
                    raise Exception("模型返回异常,请检查模型是否启动")
                # 使用正则把 data["choices"][0]["message"]["content"]的think字段放到reason_content字段中
                content = data["choices"][0]["message"]["content"]
                # 使用正则表达式提取 <think> 标签内的内容
                pattern = r"<think>(.*?)</think>"
                matches = re.findall(pattern, content, re.DOTALL)
                reason_content = "".join(matches)
                # 从 content 中移除 <think> 标签及其内容
                content = re.sub(pattern, "", content, flags=re.DOTALL)
                # 更新数据
                data["choices"][0]["message"]["content"] = content
                data["choices"][0]["message"]["reason_content"] = reason_content
                yield data

    async def open_chat(self, db, param) -> AsyncGenerator[str, None]:
        url = (
            f"{self.ragflow_url}/api/v1/chats_openai/{self.dialog_id}/chat/completions"
        )
        param["messages"] = param.get("content", [])
        logger.info(f"开放 api 参数：{param}")
        request = AsyncHTTPRequest("POST", url, headers=self.headers, params=param)
        combined_output = ""
        async for data in request():
            # 如果是流式对话，需要判断是否需要继续思考，并且把后续内容追加到新reason_content字段中
            if param["stream"]:

                result, combined_output, has_sensitive = (
                    await self.format_reason_content_stream(data, combined_output)
                )
                yield result
                if has_sensitive:
                    break
            else:
                # 获取 content 字段的值并判断直接是否data["choices"][0]["message"]["content"]是否报错
                if "choices" not in data or not data["choices"]:
                    # 抛出异常
                    raise Exception("模型返回异常,请检查模型是否启动")

                data["choices"][0]["delta"] = data["choices"][0]["message"].copy()

                content = data["choices"][0]["delta"].get('content', '')
                # 使用正则表达式提取 <think> 标签内的内容
                pattern = r"<think>(.*?)</think>"
                matches = re.findall(pattern, content, re.DOTALL)
                reason_content = "".join(matches)
                # 从 content 中移除 <think> 标签及其内容
                content = re.sub(pattern, "", content, flags=re.DOTALL)

                data["choices"][0]["delta"]["content"] = content
                data["choices"][0]["delta"]["reason_content"] = reason_content
                data["choices"][0]["delta"]["sensitive_words"] = ""

                del data["choices"][0]["message"]

                yield data

    async def open_agent_chat(self, db, param) -> AsyncGenerator[str, None]:
        """
            :param param:  知识库查询参数，具体可以查看请求参数内容
            :return:
        """
        file_params = dict()
        # 部分参数先固定在函数保持不变
        file_params["question"] = param["query"]
        file_params["session_id"] = param["session_id"]
        file_params["stream"] = True
        agent_id = param["agent_id"]
        url = f"{self.ragflow_url}/api/v1/agents/{agent_id}/completions"
        request = AsyncHTTPRequest(
            "POST", url, headers=self.headers, params=file_params
        )
        combined_output = ""
        async for data in request():
            is_agent_event, event_msg = self.check_agent_event_stream(data)
            if is_agent_event:
                resp = self.get_default_openai_response()
                resp["id"] = uuid.uuid4().hex
                resp["choices"][0]["delta"]["content"] = ""
                resp["choices"][0]["delta"]["reason_content"] = ""
                resp["choices"][0]["delta"]["sensitive_words"] = ""
                resp["created"] = int(time.time())
                resp["is_agent_running"] = True
                resp["node_event"] = event_msg
                logger.debug(f"Agent resp: {resp}")
                yield resp
            else:
                result, combined_output, has_sensitive = await self.format_reason_content_stream(
                    data, combined_output, file_params
                )
                yield result
                if has_sensitive:
                    break

    @staticmethod
    async def format_file_data(data, combined_output=""):
        """
        把data格式化成openai标称格式
        :param data:
        :return:
        """
        answer = json.loads(data)
        # print(answer)
        # 判断是否存在data字段，如果存在则认为是ragflow返回的数据
        if "data" in answer:
            answer = answer["data"]
        # 判断当前是否存在引用，如果存在引用，则这部分先不返回给前端 临时处理

        running_status = False
        content = ""
        reference = []
        # 如果不是bool类型则进行解析。ragflow返回的数据是bool类型
        if not isinstance(answer, bool):
            running_status = (
                answer["running_status"] if "running_status" in answer else False
            )
            content = answer["answer"] if "answer" in answer else ""
            reference = answer["reference"] if "reference" in answer else []

            # if "reference" in answer and answer["reference"] != {}:
            #     content = ""
            if isinstance(reference, dict):
                # 大模型有思考过程时，返回的数据最后一次会把之前的内容重放一次。
                # 如果没有思考过程, 大模型会一次返回结果，没有think过程。此时需要清空content
                if len(reference.get("chunks", [])) > 0:
                    content = ""
                if "reference" in answer and reference.get("chunks", None) is None:
                    # 前端会解析reference里面的doc_aggs字段，手动加一个该字段
                    answer["reference"]["doc_aggs"] = []

        format_data = dict()
        format_data["id"] = uuid.uuid4().hex
        format_data["is_agent_running"] = running_status
        format_data["choices"] = list()
        format_data["choices"].append(
            {
                "delta": {
                    "content": content,
                    "is_sensitive": False,
                    "function_call": None,
                    "role": "assistant",
                    "tool_calls": None,
                    "reason_content": "1111",
                },
                "reference": reference,
                "finish_reason": None,
                "index": 0,
            }
        )
        # 生成当前时间戳

        format_data["created"] = int(time.time())
        return format_data

    # 整理流式输出内容，把think内容单独追加到字段中
    # @staticmethod
    async def format_reason_content_stream(
            self, data, combined_output="", file_params=None,need_sensitive = True,search_result=None,should_check_reference=False
    ):
        # 转化知识库内容
        # should_check_reference参数: 是否检查引用字段的存在
        if file_params is not None:
            format_data = await self.format_file_data(data, combined_output)
        else:
            try:
                format_data = json.loads(data)
            except json.JSONDecodeError as e:
                logger.warning(f"Error decoding JSON，格式是: {e} 数据是: {data}")
        # 判断choices 和delta 以及content是否存在
        if "choices" not in format_data or not format_data["choices"]:
            # 抛出异常
            logger.error(f"模型返回异常,请检查模型是否启动,返回参数：{data}")
            return self.get_default_openai_response(), "", False
        # 部分模型content存在null的情况
        try:
            if (
                    "is_agent_running" in format_data
                    and not format_data["is_agent_running"]
            ) and format_data["choices"][0]["delta"]["content"] != "":
                # 找到 combined_output 在 answer["answer"] 中的起始位置
                index = format_data["choices"][0]["delta"]["content"].index(
                    combined_output
                )
                # 计算 combined_output 的长度
                length = len(combined_output)
                # 截取 index + length 之后的内容
                format_data["choices"][0]["delta"]["content"] = format_data["choices"][
                                                                    0
                                                                ]["delta"]["content"][index + length:]

            # if "is_agent_running" in format_data and format_data["is_agent_running"]:
            #     format_data["choices"][0]["delta"]["content"] = ""
        except ValueError:
            content = format_data["choices"][0]["delta"]["content"]

        content = format_data["choices"][0]["delta"]["content"]
        if content is None:
            content = ""
        # 拼接reference字段
        if should_check_reference:  # isinstance(reference, dict)
            reference = format_data["choices"][0].get("reference", {})
            if isinstance(reference, dict):
                if len(reference.get("chunks", [])) > 0:
                    content = ""
                if reference.get("chunks", None) is None:
                    reference["chunks"] = []
                    # 前端会解析reference里面的doc_aggs字段，手动加一个该字段
                    reference["doc_aggs"] = []
                format_data["choices"][0]["reference"] = reference
        # 拼接之前所有的输出
        if "is_agent_running" in format_data and not format_data["is_agent_running"]:
            combined_output += content
        has_sensitive = False
        original_value = ""
        if sensitive_words_handler is not None and need_sensitive:
            # 敏感词检测
            has_sensitive, original_value = (
                sensitive_words_handler.detect_sensitive_words(combined_output)
            )
        else:
            logging.error("敏感词检测实例未初始化")
        # 如果存在敏感词，直接返回
        format_data["choices"][0]["delta"]["sensitive_words"] = ""
        # 如果有敏感词则进行复制
        # 如果有think字段，说明需要继续思考
        format_data["choices"][0]["delta"]["reason_content"] = ""
        if content is not None and content.startswith("<think>"):
            self.__need_think = True

        # 把后续内容追加到新reason_content字段中
        if self.__need_think:
            format_data["choices"][0]["delta"]["reason_content"] = content
            format_data["choices"][0]["delta"]["content"] = ""
        if content is not None and "</think>" in content and self.__need_think:
            # 找到 </think> 的索引位置
            index = content.find("</think>")
            # 获取 </think> 前面的内容
            reason_content = content[:index]
            # 获取 </think> 后面的内容
            new_content = content[index + len("</think>"):]
            format_data["choices"][0]["delta"]["reason_content"] = (
                    reason_content + "</think>"
            )
            format_data["choices"][0]["delta"]["content"] = new_content
            self.__need_think = False

        if has_sensitive:
            format_data["choices"][0]["delta"]["content"] = "包含违规内容"
            format_data["choices"][0]["delta"]["sensitive_words"] = original_value
        if search_result is not None:
            format_data["choices"][0]["delta"]["search_result"] = search_result

        return format_data, combined_output, has_sensitive

    @staticmethod
    def check_agent_event_stream(data):
        try:
            format_data = json.loads(data)
            event_data = format_data.get("data")
            if event_data is None:
                return False, event_data
            else:
                if event_data.get("event") is None:
                    return False, event_data
                else:
                    return True, event_data
        except Exception as e:
            logger.warning(f"Error decoding JSON， error: {e} data: {data}")
            format_data = {}
            return False, format_data

    @staticmethod  # 添加静态方法装饰器
    def get_handler(key):
        return handler_registry.get(key)  # 这里改为访问全局 `handler_registry`

    @staticmethod
    # 定义默认的 OpenAI 响应字典结构
    def get_default_openai_response():
        return {
            "id": "error",
            "choices": [
                {
                    "delta": {
                        "content": "模型出现异常",
                        "role": "assistant",
                        "function_call": None,
                        "tool_calls": None,
                    },
                    "finish_reason": None,
                    "index": 0,
                    "logprobs": None,
                }
            ],
            "created": 0,  # 可以根据实际情况设置合适的值
            "model": "default_model",
            "object": "chat.completion.chunk",
            "system_fingerprint": "",
            "usage": None,
        }


import asyncio

if __name__ == "__main__":
    params2 = {
        "user": "111",
        "timeout": 30,
        "messages": [{"content": "你是谁", "role": "user", "name": "test"}],
        "model": "/home/<USER>/.cache/modelscope/hub/models/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
        "frequency_penalty": 0,
        "prompt": "你是一个机器人",
        "max_tokens": 4096,
        "n": 1,
        "presence_penalty": 0,
        "response_format": {"type": "text"},
        "seed": 1,
        "stream": True,
        "temperature": 0.7,
        "tools": [],
        "top_p": 1,
    }

from app.db import get_session


async def main():
    with get_session() as db:
        params = {
            "query": "假设公司计划开拓海外新兴化工市场，考虑当地环保法规、原材料供应、劳动力成本和市场需求，为在东南亚地区新建一座化工生产基地设计一套可行性方案，包括选址分析、产能规划、运营模式 。并使用饼状图分析出来各个环境所需要的经费",
            "prompt_name": "default",
            "knowledge_id": "49682307a4254832b495216ca346c0e1",
            "stream": True,
            "max_tokens": 8192,
            "model": "deepseek-r1-32b",
            "history": [{"role": "user", "content": "模型名称有哪些"}],
            "score_threshold": 1,
            "top_k": 3,
            "session_id": "1",
            "agent_id": "",
            "type": "file_chat",
        }

        chat_handler = ChatHandlers()

        # 获取的是一个异步生成器
        handler = chat_handler.file_chat(db, params)
        # 正确迭代异步生成器，获取数据，
        has_code = False
        code = ""
        str1 = ""
        async for chunk in handler:
            print(json.dumps(chunk))
            # 如果是流式对话，需要把代码块提取出来
            content = str(chunk["choices"][0]["delta"]["content"])
            if content.startswith("```"):
                if not has_code:
                    has_code = True
                else:
                    has_code = False
                    code += content
            if has_code:
                code += content

            str1 += content

        print(f"源数据:{str1}")
        print(f"code:{code}")

        asyncio.run(main())
