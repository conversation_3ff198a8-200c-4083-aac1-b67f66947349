import platform
from pathlib import Path as OsPath
from typing import Annotated
import docker
from fastapi import Depends

from app.config.config import settings


# 依赖函数：获取 Docker 客户端
def get_docker_client():
    """
    创建一个通用的 Docker 客户端，兼容 macOS 和 Linux 系统。
    """
    if platform.system() == "Linux":  # Linux
        docker_url = "unix://" + str(settings.container.socket_path).lstrip("/")  # type: ignore
    elif platform.system() == "Darwin":  # for debug: macOS(在mac系统本地开发时使用)
        docker_url = f"unix://{OsPath.home()}/.docker/run/docker.sock"
    else:
        raise RuntimeError("不支持的操作系统")
    client = docker.DockerClient(base_url=docker_url)
    try:
        yield client
    finally:
        # 确保客户端被正确关闭
        client.close()


DockerClientDep = Annotated[docker.DockerClient, Depends(get_docker_client)]
