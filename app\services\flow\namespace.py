import os
import json
import random
import re
import shutil
import traceback
from enum import Enum
from sqlalchemy.orm import Session
from app.db.mysql import get_mcp_db
from app.models.task import Task
from app.services.flow import _DP_API_URL, call_chat_mdl, filter_think_tags, logger, TaskStatus
from app.services.flow.task_curd import TaskCurd
from app.services.sandbox.manager import SandboxManager
from app.config.config import settings


# TODO： 取消运行中的任务，才可以重新运行
# TODO:  current_workspace_abs_path 的设置不合理，没和用户关联
class TaskType(Enum):
    AUTO = 1
    MANUAL = 2


class FlowNamespace:
    # 定义存储的数据的文件名
    workspace_base_dir = ".flow_workspace"
    # workspace_base_dir = settings.manus.sandbox_workspace_basedir  # type: ignore

    def __init__(
        self,
        session: Session | None = None,
        question=None,
        step_chain=None,
        step_chain_answer=None,
        task_type=TaskType.AUTO.value,
        task_id=None,
    ):
        if session is None:
            session = next(get_mcp_db())
        self.__task_curd = TaskCurd(session)
        self.__workspace_basepath = self.__init_workspace(self.workspace_base_dir)
        self.sandbox_id = None  # TODO:  sandbox_id 和  __sandbox 只需要一个变量
        self.__sandbox = None
        # 定义工作目录
        if step_chain:
            task = self.__task_curd.add_task(
                Task(
                    step_chain=json.dumps(step_chain),
                    step_chain_answer=step_chain_answer,
                    title=question[:50],  # type: ignore
                    type=task_type,
                    message=json.dumps(self.__build_history_message(question)),
                    status=TaskStatus.UNSTARTED.value,
                )
            )
        else:
            if task_id is not None:
                task: dict | None = self.__task_curd.get_meta(task_id)
                if task is None:
                    raise ValueError(f"Task not Found: {task_id}")
            else:
                raise ValueError("Task id is required")
        task_id = task["id"]
        workspace = task["workspace"]
        self.current_workspace_abs_path = self.__get_current_flow_workspace(task_id, workspace)
        self.id: int = task_id

    async def exe_python(self, script_content, filename, install_request_cmd=None):
        self.__save_file(script_content, filename)
        await self.ensure_sandbox_inited()
        success, full_cmd = await self.__ensure_python_env_inited(
            filename,
            install_request_cmd,
            script_content,
        )
        if not success:
            full_cmd["code"] = script_content
            return full_cmd
        result = await self.exe_shell(full_cmd["cmd"], script_content)
        result["code"] = script_content
        return result

    async def ensure_sandbox_inited(self):
        if self.__sandbox is None:
            logger.info("Sandbox is not initialized")
            sandbox_manager = SandboxManager()
            # 挂载这个地方需要调整一下：TODO: workspace的存储。需要调整，处理。
            relative_path = self.current_workspace_abs_path.split(FlowNamespace.workspace_base_dir)[-1].lstrip("/")
            host_workspace_base_dir: str = settings.manus.sandbox_workspace_basedir  # type: ignore
            host_workspace = os.path.join(host_workspace_base_dir, relative_path)
            logger.info(f"Host workspace: {host_workspace}")
            sandbox_id = await sandbox_manager.create_sandbox(volume_bindings={host_workspace: "/workspace"})
            self.sandbox_id = sandbox_id
            self.__sandbox = await sandbox_manager.get_sandbox(sandbox_id)

        return self.__sandbox

    async def exe_shell(self, cmd, script_content=None):
        try:
            await self.ensure_sandbox_inited()
            cmd_str = cmd.strip('"')
            logger.debug(f"执行shell命令: {cmd_str}")
            result = await self.__sandbox.run_command(cmd)
            logger.info(f"执行shell: {cmd_str}\n执行结果: {result}")
            success = await self._validate_code_result(cmd_str, result, script_content)
            return {
                "success": success,
                "cmd": cmd_str,
                "result": result,
            }
        except Exception as e:
            current_error = traceback.format_exc()
            logger.error(f"Error executing Python script: {current_error}")
            return {
                "success": False,
                "cmd": cmd_str,
                "result": current_error,
            }

    def get_step_chain(self):
        return self.__task_curd.get_step_chain(self.id)

    def update_step_chain(self, step_chain):
        self.reset()
        # 更新步骤
        self.__task_curd.update_step_chain(self.id, step_chain)

    def reset(self):
        # log, input_and_output, node_stat. 历史对话只保留问题。
        self.__task_curd.reset(id=self.id)

    @staticmethod
    def get_instance_from_id(task_id, session):
        meta = TaskCurd(session).get_meta(task_id)
        if meta is None:
            raise ValueError("任务不存在")
        task_id = meta["id"]
        return FlowNamespace(session, task_id=task_id)

    def add_chat_history(self, chat_msg: dict):
        # 记录聊天信息
        return self.__task_curd.add_chat_history(self.id, chat_msg)

    def get_chat_histories(self):
        return self.__task_curd.get_chat_histories(self.id)

    def add_log(self, msg: str, step: dict, level: str = "info"):
        # 记录日志
        log_msg = self.__task_curd.add_log(self.id, msg, step, level)
        return {"event": "log", "payload": log_msg}

    def get_logs(self):
        return self.__task_curd.get_logs(self.id)

    def add_input_and_output(self, msg: dict):
        # 记录输出信息 #
        # TODO: 数据的处理和保存。是否重叠，是否应该在run时重新保存一份。
        # TODO: 重新启动时加载未完成的任务(running)。
        return self.__task_curd.add_input_and_output(self.id, msg)

    def add_node_stat_msg(self, msg: dict):
        # 记录输出信息 #
        return self.__task_curd.add_node_stat_msg(self.id, msg)

    def get_stat(self):
        return self.__task_curd.get_stat(self.id)

    def get_outputs(self):
        return self.__task_curd.get_outputs(self.id)

    def update_meta(self, field, value):
        # 设置任务的状态
        return self.__task_curd.update_meta(self.id, field, value)

    def get_meta(self):
        # 获取任务的元信息
        return self.__task_curd.get_meta(self.id)

    @staticmethod
    def delete_by_id(task_id, session):
        task_curd = TaskCurd(session)
        task = TaskCurd(session).get_meta(task_id)
        if task:
            workspace = task["workspace"]
            if workspace:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                flow_workspace_path = os.path.join(current_dir, workspace)
                # 删除命名空间的数据
                if os.path.exists(flow_workspace_path):
                    shutil.rmtree(flow_workspace_path)
                    logger.info(f"成功删除目录: {flow_workspace_path}")
                else:
                    logger.info(f"目录不存在: {flow_workspace_path}")
        task_curd.delete_by_id(task_id)

    @staticmethod
    def get_all_ns(session, page: int = 1, page_size=100, user_id=None):
        """
        使用 pathlib 遍历目录树，查找第四层文件夹中的 meta.json
        """
        # TODO: 分页
        task_curd = TaskCurd(session)
        return task_curd.get_all_ns(page, page_size)

    def __save_file(self, content, filename):
        logger.info(f"Saving file: {filename}")
        if content is None:
            logger.warning(f"Content is null: {filename}")
            return
        self.__ensure_in_current_workspace()
        with open(filename, "w") as f:
            f.write(content)

    async def __ensure_python_env_inited(
        self,
        python_filename,
        install_request_cmd=None,
        script_content=None,
    ):
        """确保 Python 虚拟环境已初始化，并返回直接调用虚拟环境 Python 的命令"""
        if install_request_cmd:
            output = await self.__sandbox.run_command(install_request_cmd)
            logger.info(f"Install request cmd: {install_request_cmd}, result: {output}")
            if not await self._validate_code_result(install_request_cmd, output):
                return False, {"success": False, "cmd": install_request_cmd, "result": output}
        # 返回直接调用虚拟环境 Python 的命令
        full_cmd = f"python {python_filename}"
        logger.info(f"Init Python script env: cmd: {full_cmd}")
        return True, {"success": True, "cmd": full_cmd, "result": ""}

    async def _validate_code_result(self, cmd: str, result: str, content=None) -> bool:
        """校验步骤结果是否符合预期"""
        logger.debug(f"校验代码运行结果")
        if content:
            question = f"执行的命令: {cmd}, 控制台输出: {result}。\n 脚本的内容为: {content}"
        else:
            question = f"执行的命令: {cmd}, 控制台输出: {result}"
        messages = [
            {
                "role": "system",
                "content": "判断代码执行输出结果是否正常。当控制台输出的结果为空，你又无法判断时则返回true。如果正常，返回 true；否则返回 false。不需要过多解释，直接返回true或false。",
            },
            {"role": "user", "content": question},
        ]
        response = await call_chat_mdl(messages, _DP_API_URL)  # TODO: 环境变量的配置
        logger.info(f"检查code运行:\n{response}\n")
        # 去掉思考过程
        response = filter_think_tags(response)
        match = re.search(r"(true|false)", response.lower())
        is_valid = False
        if match:
            # 如果匹配到true则为结果有效
            if match.group(1) == "true":
                is_valid = True
        logger.info(f"校验代码运行结果: {cmd}: {'有效' if is_valid else '无效'}")
        return is_valid

    def __init_workspace(self, workspace_dir):
        # 初始化工作目录的基础目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        flow_workspace_path = os.path.join(current_dir, workspace_dir)
        self.__ensure_dir_existed(flow_workspace_path)
        return flow_workspace_path

    def __get_current_flow_workspace(self, task_id, workspace_dir=None):
        current_dir = self.__workspace_basepath
        if not workspace_dir:
            first_dir_name = str(random.randint(0, 1024)).zfill(4)
            second_dir_name = str(random.randint(0, 1024)).zfill(4)
            third_dir_name = str(random.randint(0, 1024)).zfill(4)
            workspace_path = os.path.join(current_dir, first_dir_name, second_dir_name, third_dir_name)
            self.__ensure_dir_existed(workspace_path)  # TODO: 检查目录是否存在逻辑重复
            self.__task_curd.update_meta(task_id, "workspace", workspace_path)
        else:
            workspace_path = os.path.join(current_dir, workspace_dir)
            if not os.path.exists(workspace_path):
                logger.info(f"Workspace dir not exist, mkdir: {workspace_dir}")
                self.__ensure_dir_existed(workspace_path)
        logger.info(f"Current flow workspace: {workspace_path}")
        return workspace_path

    def __ensure_dir_existed(self, path):
        os.makedirs(path, exist_ok=True)

    def __ensure_in_current_workspace(self):
        self.__ensure_dir_existed(self.current_workspace_abs_path)
        # Change to the current working directory
        os.chdir(self.current_workspace_abs_path)

    def __build_history_message(self, question):
        message = []
        if question:
            message.append({"role": "user", "content": question})
        return message

    def __get_id(self):
        return "".join(self.current_workspace_abs_path.split("/")[-3:])


# 测试
if __name__ == "__main__":
    import json

    task_list = {
        "task_list": [
            {"step": 1, "question": "北京和山东省的2022年GDP总量及增长率分别是多少？", "call_function": "数据库查询"},
            {
                "step": 2,
                "question": "北京和山东省的主要产业结构是什么？各产业占GDP比重如何？",
                "call_function": "知识库查询",
            },
            {
                "step": 3,
                "question": "北京和山东省的财政收入和固定资产投资额分别是多少？",
                "call_function": "数据库查询",
            },
            {"step": 4, "question": "北京和山东省的人均GDP水平及排名如何？", "call_function": "数据分析"},
            {
                "step": 5,
                "question": "北京和山东省的第三产业占比差异及其对经济的影响是什么？",
                "call_function": "数据分析",
            },
        ]
    }
    import asyncio

    session = next(get_mcp_db())
    _ns = FlowNamespace(session, step_chain=task_list["task_list"], question="这是一个测试")
    _script = """import os;print('Current working directory: ' + os.getcwd())"""
    _script_name = "script.py"
    _requires = "pip install aiohttp"
    _res = asyncio.run(_ns.exe_python(_script, _script_name, _requires))
    print(json.dumps(_res, ensure_ascii=True, indent=2))
    print(f"namespace id: {_ns.id}")


# 数据元信息: 问题名称，时间。
# 对话: 聊天记录。
# 流程画布: 输入和输出。（整体套局部）
# 执行日志: 执行日志。全部[整体]。
# 结果展示: 最后一条工作流的结果。
#
