import re
import uuid
import datetime
import aiohttp
from enum import Enum
from typing import Dict, List
from app.log import logger


class ManusLLM:
    def __init__(self, deepseek_api_url="http://192.168.22.191:8000/v1/chat/completions",
                 model_name="/home/<USER>/.cache/modelscope/hub/models/google/medgemma-27b-text-it/"):
        self.deepseek_api_url = deepseek_api_url
        self.model_name = model_name

    def get_llm(self):
        return self.deepseek_api_url

    def get_embedding(self):
        pass

    def get_rag(self):
        pass

    def get_chain(self):
        pass

    def get_agent(self):
        pass

    def get_tool(self):
        pass

    def now_ts(self):
        # 毫秒级时间戳
        return int(datetime.datetime.now().timestamp() * 1000)

    def now_date_str(self):
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    async def call_chat_mdl(self, messages: List[Dict[str, str]]) -> str:
        """调用DeepSeek模型"""
        attempt = 3
        while attempt > 0:
            try:
                payload = {"model": self.model_name, "messages": messages}
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.deepseek_api_url,
                        headers={"Content-Type": "application/json"},
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=60 * 2),
                    ) as response:
                        response.raise_for_status()
                        data = await response.json()
                        return data["choices"][0]["message"]["content"]
            except Exception as e:
                logger.error(f"调用DeepSeek模型失败: {e}")
                attempt -= 1
        raise ValueError(f"调用DeepSeek模型失败")

    def filter_think_tags(self, response: str) -> str:
        """
        过滤DeepSeek返回结果中的<think>标签内容
        """
        # 使用非贪婪匹配模式，确保正确处理多个标签
        clean_response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL)
        return clean_response.strip()

    # 定义默认的 OpenAI 响应字典结构
    def build_openai_response(self, task_id: str, content="", node_event=None, step_chain=None):
        return {
            "id": uuid.uuid4().hex[:24],
            "choices": [
                {
                    "delta": {
                        "content": content,
                        "role": "assistant",
                        "function_call": None,
                        "tool_calls": None,
                    },
                    "finish_reason": None,
                    "index": 0,
                    "logprobs": None,
                }
            ],
            "created": self.now_date_str(),
            "model": "default_model",
            "node_event": node_event,
            "step_chain": step_chain,
            "task_id": task_id,
            "object": "chat.completion.chunk",
            "system_fingerprint": "",
            "usage": None,
        }
