from sqlalchemy.orm import Session
from sqlalchemy import select, delete, func, update
import sqlalchemy.exc
import copy

from app.db import get_session
from app.models.ai_session_list import AiSessionList
from app.log import logger
from app.const.isdelete import IS_DELETE, IS_NOT_DELETE


class AiSessionListCurd(object):

    def __init__(self, db: Session):
        self.db = db

    # def __exit__(self, exc_type, exc_val, exc_tb):
    #     self.db.close()
    def add_session_info(self, session_info: dict):
        log_obj = AiSessionList(**session_info)
        self.db.add(log_obj)
        self.db.commit()
        return

    def get_list(self, offset: int, page_size: int, name: str, group_id: str, ctime: str, process_time: str,
                 is_root: bool) -> tuple[int, list[AiSessionList]]:
        offset = (offset - 1) * page_size
        count_stmt = select(func.count(AiSessionList.id)).filter(AiSessionList.is_delete == IS_NOT_DELETE)
        stmt = select(AiSessionList).filter(AiSessionList.is_delete == IS_NOT_DELETE)
        if not is_root:
            stmt = stmt.filter(AiSessionList.group_id == group_id)
            count_stmt = count_stmt.filter(AiSessionList.group_id == group_id)

        if name:
            count_stmt = count_stmt.filter(AiSessionList.username.like(f'%{name}%'))
            stmt = stmt.filter(AiSessionList.username.like(f'%{name}%'))

        if ctime:
            count_stmt = count_stmt.filter(AiSessionList.ctime >= ctime)
            stmt = stmt.filter(AiSessionList.ctime >= ctime)
        if process_time:
            stmt = stmt.order_by(AiSessionList.process_time.desc()).offset(offset).limit(page_size)
        else:
            stmt = stmt.order_by(AiSessionList.id.desc()).offset(offset).limit(page_size)
        try:
            count_r = self.db.execute(count_stmt)
            words_r = self.db.execute(stmt)
            return count_r.scalar(), [word for word in words_r.scalars()]
        except Exception as e:
            raise Exception(f"获取敏感词分页出错: {str(e)}")

    def delete_by_ids(self, ids: list[str], group_id: str, is_root: bool) -> int:
        stmt = update(AiSessionList).where(AiSessionList.id.in_(ids)).values({"is_delete": IS_DELETE})
        if not is_root:
            stmt = stmt.where(AiSessionList.group_id == group_id)
        result = self.db.execute(stmt)
        self.db.commit()
        return result.rowcount
