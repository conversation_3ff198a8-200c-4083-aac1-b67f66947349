
def format_error_msg(session_id):
    """
    错误信息拼接
    :param session_id:
    :return:
    """
    errmsg_dict = {
        "request_id": session_id,
        "stop": "error",
        "errmsg": "",
        "is_dialog": True
        }
    return errmsg_dict


def format_answers_sse(session_id, content, avatar, aimodel, ai_name):
    """
    拼接答案sse数据
    :param session_id: 会话id
    :param content: 消息内容
    :param avatar: ai头像
    :param aimodel: ai模型id
    :param ai_name: ai模型id
    :return:
    """
    msg = {
        "request_id": session_id,
        "stop": "",
        "errmsg": "",
        "is_dialog": False,
        "result": {
            "stop": "",
            "errmsg": "",
            "content": content,
            "good_answer": False,
            "session_id": session_id,
            "avatar": avatar,
            "aimodel": aimodel,
            "ai_name": ai_name
        }
    }
    return msg