from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime

from app.models import Base


class SensitiveWords(Base):
    """用户操作日志"""
    __tablename__ = 'sensitive_words'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(length=200), comment="敏感词名称")
    ai_n_hits: Mapped[int] = mapped_column(Integer, comment="命中次数")
    user_n_hits: Mapped[int] = mapped_column(Integer, comment="命中次数")
    user_id: Mapped[str] = mapped_column(Text, nullable=False, comment="用户id")
    status: Mapped[str] = mapped_column(Integer, nullable=False, comment="用户id")
    created_at: Mapped[dict] = mapped_column(String, nullable=False, comment="创建时间")
    deleted_at: Mapped[dict] = mapped_column(String, nullable=False, comment="删除时间")



if __name__ == "__main__":
    from app.db.mysql import get_session, get_db, Session
    with get_session() as db:
        # 创建 SensitiveWords 实例并赋值
        sw = SensitiveWords(
            name="test_word",
            ai_n_hits="10",
            user_n_hits="10",
            user_id="123",
            created_at={"time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
            deleted_at={"time": None}
        )
        # 将实例添加到会话中

        print(sw.to_dict())