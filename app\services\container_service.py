import os
import shutil
import time
import docker
import docker.errors
import ruamel.yaml
from dotenv import load_dotenv, dotenv_values
from fastapi import APIRouter
from docker.types import Ulimit, DeviceRequest
from docker.models.containers import Container as DockerContainer
from app.const.response import MessageCode
from app.config.config import settings
from app.api.response import APIResponse
from app.api.deps import DockerClientDep


from app.log import logger


router = APIRouter()


class ContainerService(object):
    LLM_DOCKER_COMPOSE_YML: str = settings.llm_manager.docker_compose_file  # type: ignore
    LLM_DOCKER_COMPOSE_ENV: str = settings.llm_manager.docker_compose_env_file  # type: ignore
    # 备份和恢复目录
    ES_DATA_DIR: str = settings.container.es_data_mount_path  # type: ignore
    ES_DATA_DIR_BACKUP: str = settings.container.es_data_mount_path_backup  # type: ignore
    MINIO_DATA_DIR: str = settings.container.minio_data_mount_path  # type: ignore
    MINIO_DATA_DIR_BACKUP: str = settings.container.minio_data_mount_path_backup  # type: ignore

    @staticmethod
    def recreate_and_run(docker_client: DockerClientDep, container_name: str):
        """
        重新创建容器
        :return:
        """
        # 读取 yml config
        config = ContainerService.get_container_config_from_yml(container_name)
        logger.info(f"Config: {config}")
        # 配置 ulimit 参数
        ulimits = ContainerService.get_ulimits(config.get("ulimits"))
        # 获取资源配置
        device_requests = ContainerService.get_device_requests(
            config.get("deploy", {}).get("resources", {}).get("reservations", {}).get("devices", None)
        )
        # 获取设备配置
        devices = ContainerService.get_devices(config.get("devices", {}))
        # 获取端口表
        ports = ContainerService.get_ports(config.get("ports", []))
        # 获取网络
        networks = ContainerService.get_networks(config["networks"], docker_client)
        if len(networks) <= 0:
            return APIResponse("网络配置错误, 请稍后再试", MessageCode.SYSTEMERR)
        # 挂载文件
        volume_mapping = ContainerService.get_volumes_from_env(config["volumes"])
        # 获取健康检查
        health_check = ContainerService.get_health_check(config.get("healthcheck"))

        # 拼接env中环境变量
        env_mapping = ContainerService.get_vars_from_env(config.get("environment"))
        # 拼接env_file中的环境变量
        env_file_mapping = ContainerService.get_vars_from_env_file(config.get("env_file"))
        # 合并后的环境变量
        environment_mapping = {**env_mapping, **env_file_mapping}
        logger.info(f"Env variables: {environment_mapping}")
        # 关闭旧的vllm服务
        container: DockerContainer | None = ContainerService.get_container_by_name(docker_client, container_name)
        if container:
            container.remove(force=True)
        if container_name == "elasticsearch":
            try:
                time.sleep(1)
                if os.path.exists(ContainerService.ES_DATA_DIR):
                    shutil.rmtree(ContainerService.ES_DATA_DIR)
                # /elasticsearch/data /backup/elasticsearch/data
                shutil.copytree(ContainerService.ES_DATA_DIR_BACKUP, ContainerService.ES_DATA_DIR)
                logger.info(f"Copied: {ContainerService.ES_DATA_DIR_BACKUP} to {ContainerService.ES_DATA_DIR}")
            except Exception as e:
                logger.warning(f"Error occurred: {e}")
        elif container_name == "minio":
            try:
                time.sleep(1)
                if os.path.exists(ContainerService.MINIO_DATA_DIR):
                    shutil.rmtree(ContainerService.MINIO_DATA_DIR)
                # /minio/data  /backup/minio/data
                shutil.copytree(ContainerService.MINIO_DATA_DIR_BACKUP, ContainerService.MINIO_DATA_DIR)
                logger.info(f"Copied: {ContainerService.MINIO_DATA_DIR_BACKUP} to {ContainerService.MINIO_DATA_DIR}")
            except Exception as e:
                logger.warning(f"Error occurred: {e}")
        else:
            pass
        # Docker启动vllm服务
        first_network_name = list(networks.keys())[0]
        current_container = docker_client.containers.run(
            image=config["image"],
            # 传递容器启动后的命令参数
            command=config.get("command"),  # 新的命令行
            name=config["container_name"],
            restart_policy={"Name": config["restart"]},
            network=first_network_name,
            networking_config={
                first_network_name: docker_client.api.create_endpoint_config(
                    ipv4_address=networks.get(first_network_name)
                )
            },
            ports=ports,
            ulimits=ulimits,
            ipc_mode=config.get("ipc", None),
            environment=environment_mapping,
            volumes=volume_mapping,
            device_requests=device_requests,
            devices=devices,
            healthcheck=health_check,
            working_dir=config.get("working_dir", None),
            mem_limit=config.get("mem_limit", None),
            detach=True,  # 后台运行容器, 这个参数也让run函数返回一个容器对象
        )
        # 逐个连接网络并分配IP
        # 创建容器时分配了第一个网络, 从第二个网络开始连接和分配IP
        for network_name in list(networks.keys())[1:]:
            try:
                ip_addr = networks.get(network_name)
                network = docker_client.networks.get(network_name)
                network.connect(container=current_container.id, ipv4_address=ip_addr)  # type: ignore
            except docker.errors.NotFound:
                logger.warning(f"network {network_name} not found")
        logger.info(f"Container networks: {current_container.attrs['NetworkSettings']['Networks']}")
        return None

    @staticmethod
    def get_basename(path: str | None):
        return os.path.basename(path.rstrip("/")) if isinstance(path, str) else ""

    @staticmethod
    def get_container_config_from_yml(container_name):
        yaml = ruamel.yaml.YAML()
        yaml.width = 4096  # 尽量减少折叠换行
        yaml.preserve_quotes = True  # 保持字符串格式，避免意外格式变化
        with open(ContainerService.LLM_DOCKER_COMPOSE_YML, "r", encoding="utf-8") as f:
            data = yaml.load(f)
            config = data["services"][container_name]
        return config

    @staticmethod
    def get_vars_from_env(env_list: list | None) -> dict:
        if not env_list:
            return {}
        # ['TZ=${TIMEZONE}', 'OMP_NUM_THREADS=10', 'CUDA_VISIBLE_DEVICES=0,1']
        load_dotenv(ContainerService.LLM_DOCKER_COMPOSE_ENV, override=True)
        env_dict = {}
        for item in env_list:
            key, value = item.split("=", 1)
            # 使用Template替换${VAR}
            value = os.path.expandvars(value)
            env_dict[key] = value
        logger.info(f"Env dict: {env_dict}")
        return env_dict

    @staticmethod
    def get_vars_from_env_file(env_file: str | None) -> dict:
        if not env_file:
            return {}
        base_dir = os.path.dirname(ContainerService.LLM_DOCKER_COMPOSE_ENV)
        env_file_path = os.path.join(base_dir, env_file)

        logger.info(f"env_file_path: {env_file_path}")
        return dict(dotenv_values(env_file_path))

    @staticmethod
    def get_volumes_from_env(volumes) -> dict:
        load_dotenv(ContainerService.LLM_DOCKER_COMPOSE_ENV, override=True)
        volume_mapping = {}
        for v in volumes:
            path = os.path.expandvars(v)
            paths = path.split(":")
            if len(paths) <= 2:
                mode = "rw"
            else:
                mode = paths[2]
            volume_mapping[paths[0]] = {"bind": paths[1], "mode": mode}
        logger.info(f"Volume mapping: {volume_mapping}")
        return volume_mapping

    @staticmethod
    def get_ports(ports):
        port_mapping = {}
        for p in ports:
            port_mapping[f"{p.split(':')[-1]}/tcp"] = p.split(":")[0]
        logger.info(f"Port: {port_mapping}")
        return port_mapping

    @staticmethod
    def get_networks(yaml_config_networks, docker_client: DockerClientDep):
        # {'work-network': {'ipv4_address': '************'}}
        all_network_mapping = {}
        for n in docker_client.networks.list():
            all_network_mapping[n.name] = n

        network_name = None
        config_networks = {}
        for abbr_name, data in yaml_config_networks.items():
            network_name = None
            for full_name in all_network_mapping.keys():
                if full_name.endswith(abbr_name):
                    network_name = full_name
                    break
            if network_name is None:
                logger.info(f"Unknown network: {abbr_name}")
                continue
            config_networks[network_name] = data["ipv4_address"]
        # {'data_work-network': '************'}
        logger.info(f"Networks: {config_networks}")
        return config_networks

    @staticmethod
    def get_ulimits(items: dict | None) -> list[Ulimit] | None:
        if not items:
            return None
        ulimits = []
        for name, value in items.items():
            if isinstance(value, dict):
                ulimits.append(Ulimit(name=name, soft=value["soft"], hard=value["hard"]))
            else:
                ulimits.append(Ulimit(name=name, soft=value, hard=value))
        return ulimits

    @staticmethod
    def get_device_requests(items: list | None) -> list[DeviceRequest] | None:
        if not items:
            return None
        device_requests = []
        for device in items:  # TODO: 只解析了3个参数:count, capabilities, driver
            device_requests.append(
                DeviceRequest(
                    # 配置 GPU 参数，等同于 --gpus all
                    count=-1 if device["count"] == "all" else device["count"],
                    capabilities=[device["capabilities"]],  # [[gpu]]
                    driver=device["driver"],
                )
            )
        return device_requests

    @staticmethod
    def get_devices(items: list | None) -> list[str] | None:
        if not items:
            return None

        result = []
        for item in items:
            if len(item.split(":")) <= 2:
                result.append(f"{item}:rwm")
            else:
                result.append(item)
        return result

    @staticmethod
    def get_health_check(items: dict | None) -> dict | None:
        if not items:
            return None
        return {
            "test": items["test"],
            "interval": int(items["interval"][:-1]) * 1_000_000_000,
            "timeout": int(items["timeout"][:-1]) * 1_000_000_000,
            "retries": int(items["retries"]),
        }

    @staticmethod
    def get_container_by_name(docker_client: DockerClientDep, name):
        try:
            return docker_client.containers.get(name)
        except docker.errors.NotFound:
            logger.info(f"Container is not Found: {name}")
            return None


if __name__ == "__main__":
    from app.api.deps import get_docker_client

    t = ContainerService()

    def recreate_test():
        for name in [
            "valkey",
            # "vllm",
            # "minio",
        ]:
            t.recreate_and_run(next(get_docker_client()), name)
            # loop = asyncio.get_event_loop()
            # loop.run_until_complete(t.recreate_and_run(next(get_docker_client()), name))

    # r2 = recreate_test()
    r2 = t.get_volumes_from_env(t.get_container_config_from_yml("node-exporter")["volumes"])
    print("recreate_test: result: ", r2)
