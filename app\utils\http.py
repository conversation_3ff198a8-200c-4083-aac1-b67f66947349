from fastapi import HTTPException, Response
from fastapi.responses import StreamingResponse
import aiohttp
import urllib
import urllib.parse
import json
import asyncio

from app.log import logger


STREAM_MIMETYPE = set([
    'application/octet-stream',
    'image/jpeg', 
    'image/png', 
    'image/gif', 
    'image/webp',
    'image/svg+xml', 
    'image/x-icon',
    'image/avif',
    'image/bmp',
    'image/vnd.microsoft.icon',
    'application/pdf',
    'application/rtf',
    'application/zip',
    'application/rar',
    'application/x-gzip',
    'application/x-tar',
    'application/msword',  # MS WORD 97-2004
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # Word文档
    'application/vnd.ms-powerpoint',  # MS PPT 97-2003
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
])

class AsyncHTTPRequest:
    def __init__(self, method: str, url: str, params: dict = None, 
                 headers: dict = None, total_timeout: int = None, sslctx=False, proxy: str = None, files: list = None) -> None:
        self.method = method
        self._url = url
        self.sslctx = sslctx
        self.proxy = proxy
        self._params = params
        self.total_timeout = total_timeout
        self.files = files
        # 使用传入的 headers；如果上传文件，则不要主动设置 Content-Type，让 aiohttp 自动生成
        self.headers = headers.copy() if headers else {}
        if self.method == 'POST' and self.files is None and ('Content-Type' not in self.headers):
            self.headers['Content-Type'] = 'application/json'

    @property
    def url(self) -> str:
        if self.method == 'POST':
            return self._url
        if not self._params:
            return self._url
        url_parts = urllib.parse.urlparse(self._url)
        query_params = dict(urllib.parse.parse_qs(url_parts.query))
        query_params.update(self._params)
        new_query_string = urllib.parse.urlencode(query_params, doseq=True)
        return urllib.parse.urlunparse((
            url_parts.scheme, url_parts.netloc, url_parts.path,
            url_parts.params, new_query_string, url_parts.fragment
        ))
    
    @property
    def params(self):
        # 如果有文件上传，则不通过此属性进行处理
        if self.method != 'POST':
            return self._params
        if self.files:
            # 使用上面的 serialize_params 来处理 params 数据
            form_data = aiohttp.FormData(self.serialize_params(self._params), quote_fields=False)  # 创建表单数据对象，包含其他参数
            for file in self.files:
                form_data.add_field(
                    'files',  # 这里是字段名称
                    file.file,  # 传入 UploadFile 对象的内容
                    filename=file.filename  # 提供文件的原始文件名
                )
            return form_data
        ct = self.headers.get('Content-Type')
        if ct is None:
            return self._params
        main_ct = ct.split(';')[0]
        if main_ct == 'application/json':
            try:
                return json.dumps(self._params)
            except Exception as e:
                logger.error(f"参数JSON序列化失败: {str(e)}, 类型: {type(self._params)}, {self._params}")
                return str(self._params)
        elif main_ct == 'application/x-www-form-urlencoded':
            return aiohttp.FormData(self._params)
        else:
            logger.warning(f"unknown content-type {ct}")
            return str(self._params)

    async def __call__(self):
        try:
            async with aiohttp.ClientSession(
                    headers=self.headers, 
                    timeout=aiohttp.ClientTimeout(sock_connect=20, total=self.total_timeout)) as client:
                logger.info(f"{self.method} {self.url} headers: {self.headers} params: {self.params}")
                async with client.request(self.method, self.url, data=self.params, ssl=self.sslctx, proxy=self.proxy) as resp:
                    if resp.status != 200:
                        text = await resp.text()
                        logger.warning(f"{self.method} {self.url} Got status {resp.status}, data '{text}'")
                        raise HTTPException(resp.status, text)
                    if 'Content-Type' not in resp.headers and self.files is None:
                        raise Exception(f"not found 'Content-Type' in response headers")
                    
                    has_event_ct = False
                    has_app_json = False
                    has_media = False
                    media_mimetype = ''
                    encoding = 'utf-8'

                    content_disposition = resp.headers.get("Content-Disposition", "")

                    for ct in resp.headers['Content-Type'].split(';'):
                        ct = ct.strip()
                        if ct.startswith('charset='):
                            encoding = ct[8:]
                        elif ct == 'text/event-stream':
                            has_event_ct = True
                        elif ct == 'application/json':
                            has_app_json = True
                        elif ct in STREAM_MIMETYPE or "filename" in content_disposition or "filename*" in content_disposition:
                            has_media = True
                            media_mimetype = ct
                            
                    if has_event_ct:
                        async for line_bytes in resp.content:
                            if line := line_bytes.decode(encoding):
                                line = line.rstrip('\n').rstrip('\r')
                                if line == '':          # empty line
                                    continue
                                elif line == "data:[DONE]":
                                    continue
                                elif line == "data: [DONE]":
                                    continue
                                elif line[0] == ':':    # comment line
                                    logger.debug(f"SSE COMMENT {line}")
                                    continue
                                elif ':' in line:       # sse data, key has: id, event, data, retry
                                    key, value = line.split(':', 1)
                                    if key == 'data':
                                        yield value.lstrip()
                                    else:
                                        logger.info(f'SSE: {key}: {value}')
                                else:
                                    raise Exception(f"SSE ERROR MSG: {line}")
                        logger.info(f"SSE FINISH")
                        return

                    if has_app_json:
                        data = await resp.json()
                        logger.info(f"{self.method} {self.url} data: {data}")
                    elif has_media:
                        content = await resp.content.read()
                        async def iter_media():
                            yield content
                        data = StreamingResponse(iter_media(), media_type=media_mimetype)
                        logger.info(f"{self.method} {self.url} media size: {len(content)}, mimetype: {media_mimetype}")
                    else:
                        data = await resp.text()
                        logger.warning(f"unexpected content type headers received: {resp.headers}")
                        logger.info(f"{self.method} {self.url} data: {data}")
                    yield data
        except HTTPException as e:
            logger.error(f"{self.method} {self.url} {self.params} error: {str(e)}")
            raise
        except Exception as e:
            logger.exception(f"{self.method} {self.url} {self.params} error: {str(e)}")
            raise
        finally:
            logger.info(f"{self.method} {self.url} FINISH")
            await asyncio.sleep(0.250 if self.sslctx else 0)    # wait underlying connections to close

    @staticmethod
    def serialize_params(params):
        serialized = {}
        for key, value in params.items():
            if value is None:  # 如果值是 None
                serialized[key] = ""  # 将 None 转换为空字符串（可以选择其他默认值）
            elif isinstance(value, (bool, int, float)):  # 处理布尔值、整数、浮动类型
                serialized[key] = str(value)  # 将其转换为字符串
            else:
                serialized[key] = value  # 其他类型保留原样
        return serialized