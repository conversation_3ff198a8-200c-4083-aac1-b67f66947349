import json
from app.config import settings
from app.const.chatchat import CCKnowledgeBaseApi
from app.const.response import MessageCode
from app.log import logger
from app.utils.http import AsyncHTTPRequest


class CCKnowledgeBaseService:

    def __init__(self):
        self.headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json',
        }
        self.api = settings.chatchat.api

    async def list_knowledge_base(self):
        """知识库列表"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.LIST_KNOWLEDGE_BASES_URL}'
            request = AsyncHTTPRequest('GET', url, headers=self.headers)
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-知识库列表加载出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-知识库列表加载出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def create_knowledge_base(self, data):
        """创建知识库"""
        try:
            # 上传知识库信息到chatchat
            url = f'{self.api}{CCKnowledgeBaseApi.CREATE_KNOWLEDGE_BASE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for response in request():
                if response['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-知识库创建出错：{response["msg"]}')
        except Exception as e:
            errmsg = f'chatchat-创建知识库出错：{str(e)}'
            raise Exception(errmsg)
        
    async def delete_knowledge_base(self, name):
        """删除知识库"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.DELETE_KNOWLEDGE_BASE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=name)
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-知识库删除出错：{data["msg"]}')
        except Exception as e:
            errmsg = f'chatchat-删除知识库出错：{str(e)}'
            raise Exception(errmsg)
        
    async def list_files(self, name):
        """知识库文件列表"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.LIST_FILES_URL}'
            payload = {
                "knowledge_base_name": name
            }
            request = AsyncHTTPRequest('GET', url, headers=self.headers, params=payload)
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-知识库文件列表加载出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-知识库文件列表加载出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        
    async def search_docs(self, data):
        """搜索知识库"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.SEARCH_DOCS_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                return data
        except Exception as e:
            errmsg = f'chatchat-搜索知识库出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def upload_docs(self, knowledge_base_name, override, to_vector_store, chunk_size,
                                                chunk_overlap, zh_title_enhance, docs, not_refresh_vs_cache, files):
        """上传文件到知识库，并/或进行量化"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.UPLOAD_DOCS_URL}'
            payload = {
                "knowledge_base_name": knowledge_base_name,
                "override": override,
                "to_vector_store": to_vector_store,
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap,
                "zh_title_enhance": zh_title_enhance,
                "docs": docs,
                "not_refresh_vs_cache": not_refresh_vs_cache,
            }

            headers = {
                'accept': 'application/json'
                }
                
            request = AsyncHTTPRequest('POST', url, headers=headers, params=payload, files=files)
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-上传文件到知识库出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-上传文件到知识库出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def delete_docs(self, data):
        """删除知识库内指定文件"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.DELETE_DOCS_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-删除知识库内指定文件出错：{data["msg"]}')
        except Exception as e:
            errmsg = f'chatchat-删除知识库内指定文件出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def update_info(self, data):
        """更新知识库介绍"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.UPDATE_INFO_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-更新知识库介绍出错：{data["msg"]}')
        except Exception as e:
            errmsg = f'chatchat-更新知识库介绍出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def update_docs(self, data):
        """更新现有文件到知识库"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.UPDATE_DOCS_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-更新现有文件到知识库出错：{data["msg"]}')
        except Exception as e:
            errmsg = f'chatchat-更新现有文件到知识库出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

    async def download_docs(self, knowledge_base_name, file_name, preview):
        """下载知识库中的文件"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.DOWNLOAD_DOCS_URL}'
            payload = {
                "knowledge_base_name": knowledge_base_name,
                "file_name": file_name,
                "preview": preview
            }
            request = AsyncHTTPRequest('GET', url, headers=self.headers, params=payload)
            async for data in request():
                return data
        except Exception as e:
            errmsg = f'chatchat-下载知识库中的文件出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
    
    async def recreate_vector_store(self, data):
        """根据content中文本重新计算向量，流式输出处理进度"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.RECREATE_VECTOR_STORE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                yield data
        except Exception as e:
            errmsg = f'chatchat-重新计算向量出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        
    async def upload_temp_docs(self, prev_id, chunk_size, chunk_overlap, zh_title_enhance, files):
        """上传文件到临时目录"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.UPLOAD_TEMP_DOCS_URL}'

            payload = {
                "prev_id": prev_id,
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap,
                "zh_title_enhance": zh_title_enhance,
            }
            
            headers = {
                'accept': 'application/json'
                }
                
            request = AsyncHTTPRequest('POST', url, headers=headers, params=payload, files=files)
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-上传文件到临时目录出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-上传文件到临时目录出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        
    async def search_temp_docs(self, data):
        """从临时 FAISS 知识库中检索文档，用于文件对话"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.SEARCH_TEMP_DOCS_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                return data
        except Exception as e:
            errmsg = f'chatchat-搜索临时 FAISS 知识库中文档出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        

    async def summary_file_to_vector_store(self, data):
        """单个知识库根据文件名称摘要"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.SUMMARY_FILE_TO_VECTOR_STORE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-单个知识库根据文件名称摘要出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-单个知识库根据文件名称摘要出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        
    async def summary_doc_ids_to_vector_store(self, data):
        """单个知识库根据doc_ids摘要"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.SUMMARY_DOC_IDS_TO_VECTOR_STORE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-单个知识库根据doc_ids摘要出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-单个知识库根据doc_ids摘要出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)
        
    async def recreate_summary_vector_store(self, data):
        """重建单个知识库文件摘要"""
        try:
            url = f'{self.api}{CCKnowledgeBaseApi.RECREATE_SUMMARY_VECTOR_STORE_URL}'
            request = AsyncHTTPRequest('POST', url, headers=self.headers, params=data.dict())
            async for data in request():
                if data['code'] != MessageCode.SUCCESS.status_code:
                    raise Exception(f'chatchat-重建单个知识库文件摘要出错：{data["msg"]}')
                return data['data']
        except Exception as e:
            errmsg = f'chatchat-重建单个知识库文件摘要出错：{str(e)}'
            logger.error(errmsg)
            raise Exception(errmsg)

if __name__ == "__main__":
    import asyncio

    async def main():
        data = await CCKnowledgeBaseService().list_knowledge_base()
        print(data)
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())