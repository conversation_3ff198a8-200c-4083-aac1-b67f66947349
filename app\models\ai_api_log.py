from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, Integer, TIMESTAMP, text, BOOLEAN, JSON, Float, Text
import datetime


from app.models import Base


class AiApiLog(Base):
    """用户操作日志"""
    __tablename__ = 'ai_api_log'
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    client_ip: Mapped[str] = mapped_column(String(length=200), comment="请求ip")
    api_path: Mapped[str] = mapped_column(Text, nullable=False, comment="请求接口")
    api_params: Mapped[dict] = mapped_column(JSON, nullable=False, comment="请求参数")
    cookie: Mapped[dict] = mapped_column(JSON, nullable=False, comment="请求cookie")
    header: Mapped[dict] = mapped_column(JSON, nullable=False, comment="请求头")
    method: Mapped[str] = mapped_column(String(length=200), comment="请求方法")
    process_time: Mapped[str] = mapped_column(String(length=200), comment="接口相应时间")
    is_delete: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否删除：0 未删除， 1 删除')
    ctime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="创建时间",
                                       server_default=text("CURRENT_TIMESTAMP"))
    mtime: Mapped[int] = mapped_column(TIMESTAMP, nullable=False, comment="修改时间",
                                       server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self):
        return {
            'id': self.id,
            'client_ip': self.client_ip,
            'username': self.username,
            'api_path': self.api_path,
            'api_params': self.api_params,
            'cookie': self.cookie,
            'header': self.header,
            'method': self.method,
            'process_time': self.process_time,
            "user_id": self.user_id,
            "group_id": self.group_id,
            "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
            "mtime": self.mtime.strftime("%Y-%m-%d %H:%M:%S"),
        }