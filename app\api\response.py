from fastapi.responses import J<PERSON>NResponse
from functools import wraps
import json
from app.const.response import MessageCode
from app.log import logger
import typing

# responses定义到这里
RESPONSE = "response must include result and code"


class APIBaseException(Exception):
    def __init__(self, msg: str = ""):
        super(APIBaseException, self).__init__(msg)


class ResponseException(APIBaseException):
    pass


class APIResponse(JSONResponse):
    def __init__(self,content={},status=MessageCode.SUCCESS):
        self.content = content
        self.status = status
        super().__init__(content=content, status_code=status.status_code)

    def render(self, content: typing.Any) -> bytes:
        # content check
        #  如果不是正确结果，则吧content赋值给msg

        try:
            msg = self.content if self.status == MessageCode.SYSTEMERR else self.status.msg
            result = {} if self.status == MessageCode.SYSTEMERR else self.content
        
            response = {
                "msg": msg,
                "code": self.status.status_code,
                "content": result
            }

            return json.dumps(
                response,
                ensure_ascii=False,
                allow_nan=False,
                indent=None,
                separators=(",", ":"),
            ).encode("utf-8")

        except Exception as e:
            raise ResponseException(e)


def _format_response(r):
    if isinstance(r, MessageCode):
        return APIResponse(content={'code': r, 'result': {}})  # 默认 content 为空字典
    elif isinstance(r, tuple):
        if len(r) != 2 or not isinstance(r[0], MessageCode):
            raise ValueError(f"invalid return value: {r}")
        if r[0] == MessageCode.SUCCESS:
            return APIResponse(content={'code': r[0], 'result': r[1]})
        else:
            if not isinstance(r[1], str):
                raise TypeError(f"invalid error msg type {type(r[1])}: {r[1]}")
            return APIResponse(content={'code': r[0], 'msg': r[1], 'result': {}})
    else:
        return APIResponse(content={'code': MessageCode.SUCCESS, 'result': r})


def response_formatter(func):
    if hasattr(func, '__await__'):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                r = await func(*args, **kwargs)
            except Exception as e:
                logger.error(str(e))
                return APIResponse(content={
                    'code': MessageCode.SYSTEMERR,
                    'msg': f"{MessageCode.SYSTEMERR.value[1]}: {str(e)}",
                    'result': {}
                })
            return _format_response(r)

        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                r = func(*args, **kwargs)
            except Exception as e:
                logger.error(str(e))
                return APIResponse(content={
                    'code': MessageCode.SYSTEMERR,
                    'msg': f"{MessageCode.SYSTEMERR.value[1]}: {str(e)}",
                    'result': {}
                })
            return _format_response(r)

        return sync_wrapper


if __name__ == '__main__':
     t = APIResponse({})
     h= t.render("ss",MessageCode.SYSTEMERR)
     print(h)

