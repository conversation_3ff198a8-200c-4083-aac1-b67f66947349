import aiohttp
from app.log import logger
import requests
import json
from app.curd.ai_mcp_list_curd import AiMcpListCurd
from app.models.ai_mcp_list import AiMcpList
from app.models.ai_mcp import AiMcp
import datetime
import requests
import json
import re
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.db.mysql import get_mcp_db


class McpService:
    """
    Service class for handling MCP (Multi-Channel Publishing) related operations.
    """

    def __init__(self, ):
        """
        Initializes the McpService with a repository for MCP operations.

        :param mcp_repository: An instance of a repository that handles MCP data.
        """
        self.mcp_repository = {}

    def execute_tool(self, servers, params, tool, name):
        """使用 tools/call 接口执行选择的工具"""
        url, _ = self.get_dynamic_url(servers)

        base_url = url.replace("/sse", "/rpc")
        logger.info(f"Executing tool '{tool['name']}' with parameters: {params}")

        payload = {
            "method": "tools/call",
            "params": {
                "name": name,
                "arguments": params
            },
            "jsonrpc": "2.0"
        }

        headers = {
            "Content-Type": "application/json"
        }
        logger.error(f"工具请求参数: {payload}")

        try:
            response = requests.post(base_url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to execute tool: {e}")
            return {"error": str(e)}

    def fetch_tools_via_rpc(self, url):
        """
          通过 RPC 请求从 MCP 服务器获取工具列表。

          :param url: 包含 MCP 服务器配置信息的 JSON 数据，用于提取服务器 URL。
          :return: 成功时返回工具列表，失败时返回空列表。
        """
        if not url:
            logger.error("No URL found in MCP servers configuration.")
            # 抛出异常
            raise ValueError("No URL found in MCP servers configuration.")

        # 将 sse 替换为 rpc 接口
        base_url = url.replace("/sse", "/rpc")
        logger.info(f"Sending RPC request to: {base_url} 原：({url})")

        payload = {
            "method": "tools/list",
            "params": {
                "_meta": {
                    "progressToken": 2
                }
            },
            "jsonrpc": "2.0",
            "id": 2
        }

        headers = {
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(base_url, json=payload, headers=headers)
            response.raise_for_status()
            data = response.json()

            tools = data.get("result", {}).get("tools", [])
            logger.info(f"Found {len(tools)} tools in {tools}")
            for tool in tools:
                logger.info(
                    f"- {tool.get('name')} (description: {tool.get('description')}), 请求参数：{tool.get('inputSchema', {})}")

            return tools

        except requests.RequestException as e:
            logger.error(f"Failed to fetch tools from {url}: {e}")
            return []

    def get_dynamic_url(self, json_data, key="url"):
        try:
            # 尝试从 json_data 中获取 "mcpServers" 对应的值
            mcp_servers = json_data["mcpServers"]
            # 尝试解析 JSON 字符串
            # mcp_servers = json.loads(mcp_servers_str)
            if mcp_servers:
                # 检查 mcp_servers 字典是否为空
                first_service_key = next(iter(mcp_servers))
                # 尝试获取指定键对应的值
                url = mcp_servers[first_service_key][key]
                return url, first_service_key
            else:
                logger.error("MCP servers configuration is empty.")
        except KeyError:
            error_msg = "Key 'mcpServers' not found in json_data."
        except json.JSONDecodeError:
            error_msg = "Failed to decode JSON string in mcpServers."
        except StopIteration:
            error_msg = "No services found in mcpServers."
        except Exception as e:
            error_msg = f"An unexpected error occurred: {e}"
        logger.error(error_msg)
        return None, error_msg

    def add_mcp_server(self, db, config, name, desc=""):
        """添加MCP服务器"""
        ai_mcp_list_curd = AiMcpListCurd(db)
        url, mcp_name_parsed = self.get_dynamic_url(config)
        if not url:
            raise ValueError(f"解析url失败: {mcp_name_parsed}")
        if not name:
            name = mcp_name_parsed
        # 检查是否存在相同的mcp_name
        existing_mcp = db.query(AiMcp).filter_by(name=name).first()
        if existing_mcp:
            raise ValueError(f"MCP服务器名称 '{name}' 已存在。")
        existing_mcp = db.query(AiMcp).filter_by(url=url).first()
        if existing_mcp:
            raise ValueError(f"MCP服务器地址 '{url}' 已存在, 请勿重复添加。")
        # 获取mcp列表
        mcp_list = self.fetch_tools_via_rpc(url)
        # 判断mcp_list存在result 并且 result不是空的
        if not mcp_list:
            raise ValueError("未获取到mcp工具列表。请核查mcp服务地址是否正确。")
        # 向AI_mc表插入数据
        mcp_detail = AiMcp(
            config=json.dumps(config),
            enabled=True,
            ctime=datetime.datetime.now(),
            mtime=datetime.datetime.now(),
            name=name,
            desc=desc,
            url=url,
        )
        ai_mcp_list_curd.add_mcp(mcp_list, mcp_detail)

    def delete_mcp_server(self, db, mcp_id):
        """删除MCP服务器"""
        ai_mcp_list_curd = AiMcpListCurd(db)
        ai_mcp_list_curd.delete_mcp(mcp_id)

    def set_mcp_server_enabled(self, db, mcp_id: int, enabled: bool):
        """设置开启或关闭mcp服务"""
        db.query(AiMcp).filter_by(id=mcp_id).update({"enabled": enabled})
        db.commit()

    def get_mcp_server_list(self, db: Session, page: int, page_size: int):
        """从数据库分页查找mcp列表"""
        # 查询时返回列表，列表里面是字典
        # 目前使用的数据拼接的形式
        mcp_list: list[AiMcp] = db.query(AiMcp).offset((page - 1) * page_size).limit(page_size).all()
        mcp_ids = [row.id for row in mcp_list]
        if mcp_ids:
            tools = db.query(AiMcpList).filter(AiMcpList.mcp_id.in_(mcp_ids)).all()
        else:
            tools = []
        tools_mappings = {}
        for tool in tools:
            tools_mappings.setdefault(tool.mcp_id, [])
            tools_mappings[tool.mcp_id].append(tool.to_dict())
        result = []
        for row in mcp_list:
            item = row.to_dict()
            item["tools"] = tools_mappings.get(row.id, [])
            result.append(item)
        return result

    def get_mcp_server_tools(self, db, mcp_id: int):
        """根据mcp_id查找tools的数据"""
        tools = db.query(AiMcpList).filter_by(mcp_id=mcp_id).all()
        return [row.to_dict() for row in tools]

    def test_mcp_server_tool(self, db, tool_id: int, params: Dict[str, Any]):
        """测试 tool"""
        tool: AiMcpList | None = db.query(AiMcpList).filter_by(id=tool_id).first()
        if tool is None:
            raise ValueError("无效的ID")
        mcp: AiMcp = db.query(AiMcp).filter_by(id=tool.mcp_id).first()
        tool_info = {"parameters": params, "tool": {"name": tool.name}}
        return self.execute_tool_agent(tool_info, mcp.url)

    def test_mcp_server_tool_by_name(self, db, url: str, tool_name: str, params: Dict[str, Any]):
        """通过工具名称, 测试 tool"""
        tool_info = {"parameters": params, "tool": {"name": tool_name}}
        return self.execute_tool_agent(tool_info, url)

    def call_deepseek(self, messages: List[Dict[str, str]]) -> str:
        """调用DeepSeek模型"""
        try:
            payload = {"model": "YuanFang-Agent-Model", "messages": messages}
            response = requests.post(
                LLM_CONFIG["api_url"], headers={"Content-Type": "application/json"}, json=payload, verify=False
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"调用DeepSeek模型失败: {e}")
            return ""

    def _filter_think_tags(self, response: str) -> str:
        """
        过滤DeepSeek返回结果中的<think>标签内容

        Args:
            response: DeepSeek返回的原始字符串

        Returns:
            过滤掉<think>标签后的最终回答
        """
        # 使用非贪婪匹配模式，确保正确处理多个标签
        clean_response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL)
        return clean_response.strip()

    def _extract_json(self, content: str) -> str:
        """从文本中提取JSON内容"""
        # 处理常见的JSON包裹情况
        content = content.strip()

        # 移除可能的Markdown代码块标记
        if content.startswith("```json") and content.endswith("```"):
            content = content[7:-3].strip()
        elif content.startswith("```") and content.endswith("```"):
            content = content[3:-3].strip()

        # 提取第一个完整的JSON对象或数组
        # 使用更健壮的正则表达式，匹配最外层的括号
        json_pattern = r"(\[.*\]|\{.*\})"

        # 使用非贪婪模式匹配，并尝试找到有效的JSON
        matches = re.finditer(json_pattern, content, re.DOTALL)

        valid_json = None
        for match in matches:
            json_str = match.group(0)
            try:
                # 验证JSON是否有效
                json.loads(json_str)
                valid_json = json_str
                break
            except json.JSONDecodeError:
                continue

        if valid_json:
            return valid_json

        # 如果没有找到有效的JSON，尝试解析原始内容
        try:
            json.loads(content)
            return content
        except json.JSONDecodeError:
            logger.error(f"无法从内容中提取有效的JSON: {content}")
            raise ValueError("未能从响应中提取有效的JSON内容")

    def analyze_user_query_with_llm(self, user_query: str, available_tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用DeepSeek模型分析用户查询，选择合适的工具并生成参数"""
        # 构建工具列表描述
        tools_description = []
        for tool in available_tools:
            tool_desc = {
                "name": tool["name"],
                "parameters": tool["inputSchema"].get("properties", {}),
                "required": tool["inputSchema"].get("required", []),
                "description": tool["description"]
            }
            tools_description.append(tool_desc)

        # 构建提示
        prompt = f"""
        用户查询: "{user_query}"

        可用工具列表:
        {json.dumps(tools_description, indent=2)}

        请选择合适的工具来处理用户查询，并生成调用该工具所需的参数。
        请以JSON格式返回，包含以下字段:
        {{
            "name": "工具名称",
            "parameters": {{
                "参数1": "值1",
                "参数2": "值2"
                // 其他必要参数
            }}
        }}
        """

        # 调用DeepSeek模型
        messages = [
            {"role": "system", "content": "你是一个智能助手，帮助选择合适的工具并生成参数。"},
            {"role": "user", "content": prompt}
        ]

        model_response = self.call_deepseek(messages)

        if not model_response:
            logger.error("DeepSeek模型返回空响应")
            return None
        model_response = self._filter_think_tags(model_response)
        model_response = self._extract_json(model_response)
        # 解析模型响应
        try:
            tool_selection = json.loads(model_response)
            logger.info(f"大模型选择的工具: {tool_selection['name']}")
            logger.info(f"生成的参数: {json.dumps(tool_selection['parameters'], indent=2)}")

            # 查找实际工具定义
            for tool in available_tools:
                if tool["name"] == tool_selection["name"]:
                    return {
                        "tool": tool,
                        "parameters": tool_selection["parameters"]
                    }

            logger.error(f"未找到工具: {tool_selection['name']}")
            return None

        except json.JSONDecodeError as e:
            logger.error(f"解析模型响应失败: {e}, 响应内容: {model_response}")
            return None
        except Exception as e:
            logger.error(f"处理模型响应失败: {e}")
            return None

    def execute_tool_agent(self, tool_info: Dict[str, Any],base_url:str="") -> Dict[str, Any]:
        """使用 tools/call 接口执行选择的工具"""
        tool = tool_info["tool"]
        params = tool_info["parameters"]

        logger.info(f"Executing tool '{tool['name']}' with parameters: {params}")

        payload = {
            "method": "tools/call",
            "params": {
                "name": tool["name"],
                "arguments": params
            },
            "jsonrpc": "2.0"
        }

        headers = {
            "Content-Type": "application/json"
        }
        logger.error(f"工具请求参数: {payload}")

        try:
            response = requests.post(base_url.replace("/sse", "/rpc"), json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to execute tool: {e}")
            return {"error": str(e)}

    async def execute_tool_agent_async(self, tool_info: Dict[str, Any], base_url: str = "") -> Dict[str, Any]:
        """异步使用 tools/call 接口执行选择的工具"""
        tool = tool_info["tool"]
        params = tool_info["parameters"]

        logger.info(f"Executing tool '{tool['name']}' with parameters: {params}")

        payload = {"method": "tools/call", "params": {"name": tool["name"], "arguments": params}, "jsonrpc": "2.0"}

        headers = {"Content-Type": "application/json"}
        logger.error(f"工具请求参数: {payload}")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    base_url.replace("/sse", "/rpc"),
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30),  # 设置总超时时间为30秒
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to execute tool: {e}")
            return {"error": str(e)}


if __name__ == "__main__":

    def test():
        mcp_service = McpService()
        url = "https://mcp.higress.ai/mcp-time/cmb5yrsw100d08r01xajzzom6/sse".replace("/sse", "/rpc")
        tool_info = {"parameters": {"timeZone": "Asia/Shanghai"}, "tool": {"name": "get-current-time"}}
        tool_info = {
            "mcpServers": {"mcp-e2bdev": {"url": "https://mcp.higress.ai/mcp-e2bdev/cmb5yrsw100d08r01xajzzom6/sse"}}
        }
        # res = mcp_service.get_dynamic_url(tool_info, url)
        res = mcp_service.get_mcp_server_list(next(get_mcp_db()), 1, 100)
        print(f"====>: {res}")

    test()
