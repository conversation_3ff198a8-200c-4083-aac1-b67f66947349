from app.utils.http import AsyncHTTPRequest, HTTPException
from app.config import settings
from app.const import MsgCode
from app.api.response import response_formatter
from app.const.llm_resource import urlformat
from app.log import logger
from app.db import get_async_db
from urllib.parse import urljoin
from fastapi.responses import StreamingResponse
from enum import IntEnum
import json
import time
from datetime import datetime


class ServerCode(IntEnum):
    SYSTEMERR           = 201
    PARAMERR            = 202
    TOKENDENIED         = 205
    MODELNOTFOUND       = 2006
    AVATARNOTFOUND      = 2007
    AVATARNOTSUPPORT    = 2008



class LLMAPIService:
    API = settings.chatapi.api

    def __init__(self):
        self.headers = {
            'Content-Type': 'application/json'
        }

    async def get_model_status(self, models: list[int], start: str, end: str,
                               interval: str, tz: str):
        url = self.API + '/api/v1/status/'
        payload = {}
        if models:
            payload['models'] = models
        if start:
            payload['start'] = start
        if end:
            payload['end'] = end
        if interval:
            payload['interval'] = interval
        if tz:
            payload['tz'] = tz
        request = AsyncHTTPRequest(
            'POST', url, headers=self.headers,
            params=payload
        )
        try:
            async for data in request():
                return data['result']
        except HTTPException as e:
            logger.error(f"请求远程服务器监控状态错误: "
                         f"code: {e.status_code}, detail: {e.detail}")
            if e.status_code == 422:
                errmsg = "请求参数格式错误"
                try:
                    detail = json.loads(e.detail)['detail'][0]
                    msg = detail['msg']
                except Exception as _e:
                    raise Exception(errmsg)
                else:
                    raise Exception(f"{errmsg}: {msg}")
            else:
                raise Exception(f"请求远程服务器错误: {e.status_code}")

    async def get_llm_list(self):
        url = self.API + '/api/v1/llm/list_all'
        request = AsyncHTTPRequest('GET', url, headers=self.headers)
        async for data in request():
            return data['result']
        
    async def get_llm_search(self, models: list[int]=None, channel: str='',
                             company: str='', nickname: str='', offset: int=None,
                             page_size: int=None):
        url = self.API + '/api/v1/llm/list_search'
        payload = {}
        if models:
            payload['models'] = list(set(models))
        if channel:
            payload['channel'] = channel
        if company:
            payload['company'] = company
        if nickname:
            payload['nickname'] = nickname
        if offset and page_size:
            payload['offset'] = offset
            payload['page_size'] = page_size
        request = AsyncHTTPRequest('POST', url, headers=self.headers, params=payload)
        async for data in request():
            return data['result']
        
    async def get_llm_by_ids(self, models: list[int]=None, channelname: str='',
                             company: str='', nickname: str='', offset: int=None,
                             page_size: int=None):
        result = await self.get_llm_search(
            models, channelname, company, nickname, offset, page_size
        )
        channels = {}
        for llm in result['data']:
            channel = llm['channel']
            if channel not in channels:
                channels[channel] = {
                    'id': llm['channel_id'],
                    'channel': channel,
                    'total': 0,
                    'llms': []
                }
            del llm['channel']
            del llm['channel_id']
            llm['avatar'] = urlformat(llm['id'])
            for resource in llm['resource']:
                resource['link'] = urlformat(llm['id'], resource['type'])
            channels[channel]['llms'].append(llm)
            channels[channel]['total'] += 1
        return list(channels.values())
    
    async def get_llm_info(self, model: int) -> dict:
        result = await self.get_llm_search(models=[model])
        data = result['data']
        if not data:
            return None
        llm = data[0]
        llm['avatar'] = urlformat(llm['id'])
        llm['expertise_areas'] = [
            area['area'] for area in llm['expertise_areas']
        ]
        for resource in llm['resource']:
            resource['link'] = urlformat(llm['id'], resource['type'])
        return llm
    
    @response_formatter
    def msg_code_convert(self, code: int, msg: str):
        errmsg = f"服务错误: {msg}"
        match code:
            case ServerCode.SYSTEMERR:
                return MsgCode.SYSTEMERR, errmsg
            case ServerCode.AVATARNOTFOUND:
                return MsgCode.AVATARNOTFOUND, errmsg
    
    async def get_llm_resource(self, id: int, type: str='avatar'):
        url = urljoin(self.API, f'/api/v1/llm/{type}/{id}')
        request = AsyncHTTPRequest('GET', url, headers=self.headers)
        async for data in request():
            if isinstance(data, StreamingResponse):
                return data
            # 错误码转换
            return self.msg_code_convert(data['code'], data['msg'])
    
    async def get_llm_avatar(self, id: int):
        return await self.get_llm_resource(id, type='avatar')
        
    async def get_llm_paper(self, id: int):
        return await self.get_llm_resource(id, type='paper')
    
    async def get_llm_report(self, id: int):
        return await self.get_llm_resource(id, type='report')
    
    async def get_llm_infos(self, models: list[int], nickname: str=''):
        result = {}
        r = await self.get_llm_search(models, nickname=nickname)
        for llm in r['data']:
            mid = llm['id']
            result[mid] = {
                'id': mid, 'avatar': urlformat(mid),
                'company': llm['company'],
                'nickname': llm['nickname'],
                'name': llm['model'],
                'channel': llm['channel'],
                'channel_id': llm['channel_id'],
                'qpm': llm['qpm'], 'tpm': llm['tpm'],
                "description": llm['description']
            }
        return result

    async def talk(self, question: str, targets: list[dict],
                   session_id: str=''):
        url = urljoin(self.API, '/api/v1/chat/completions')
        payload = {
            "question": question,
            "targets": targets
        }
        request = AsyncHTTPRequest(
            'POST', url, headers=self.headers, params=payload
        )
        async for msg in request():
            return session_id, msg  # 同步接口
        
    async def talk_stream(self, question: str, targets: list[dict],
                          session_id: str='', userid: int=0, orgid: int=0,
                          groupid: int=0):
        url = urljoin(self.API, '/api/v1/chat/completions/sse')
        payload = {
            "question": question,
            "request_id": session_id,
            "targets": targets
        }
        request = AsyncHTTPRequest(
            'POST', url, headers=self.headers, params=payload
        )
        models_set = {t['model'] for t in targets}
        async for session in get_async_db():
            repo = APICountRepo(session)
            start = time.time()
            try:
                async for msg in request():
                    if isinstance(msg, str):
                        data = json.loads(msg)
                        result = data['result']
                        if result and result['stop'] in ('finish', 'error'):
                            model = result['model']
                            await repo.add_record(
                                model, userid, orgid, groupid,
                                is_failed=result['stop'] == 'error',
                                tokens=result['total_tokens'],
                                response_time=int((time.time() - start) * 1000)
                            )
                            models_set.remove(model)
                    yield msg  # 异步接口
            finally:
                end = time.time()
                for model in models_set:
                    await repo.add_record(
                        model, userid, orgid, groupid, is_failed=1,
                        tokens=0, response_time=int((end - start) * 1000)
                    )
                logger.debug(f"[{session_id}]对话任务完成: {int(end - start)}s")


if __name__ == '__main__':
    '''
    以下为测试调用方法,等功能合并完毕需删除
    '''
    from pprint import pprint
    import json
    import asyncio

    session_id = 'e1d48777ccdc421784cb6d16b21cfb25'
    async def main():
        service = LLMAPIService()

        # 进行对话
        question = "你好,你是谁?"
        payloads = [
            {
                "model": 1,
                "keyid": 1,
                "prompt": "",
                "temperature": 0.9,
                "top_p": 1.0,
                "max_tokens": 4096,
                "history": []
            },
            {
                "model": 2,
                "keyid": 2,
                "prompt": "",
                "temperature": 0.9,
                "top_p": 1.0,
                "max_tokens": 2048,
                "history": []
            }
        ]
        request_id, data = await service.talk(
            question, session_id=session_id, targets=payloads
        )
        print(f"[talk msg]:")
        pprint(data)

        async for msg in service.talk_stream(
                question, targets=payloads, session_id=session_id):
            data = json.loads(msg)
            if data['stop'] == '':
                r = data['result']
                if r['stop'] == 'finish':
                    print(f"\ntotal_tokens: {data['result']['total_tokens']}")
                elif r['stop'] == 'error':
                    print(f"\nSubError: {r['errmsg']}")
                elif r['stop'] == '':
                    print(f"\b\r[{data['index']} {r['model_nickname']}] {r['content']}", end='')
            elif data['stop'] == 'error':
                print(f"\nError: {data['errmsg']}")
    
    asyncio.run(main())