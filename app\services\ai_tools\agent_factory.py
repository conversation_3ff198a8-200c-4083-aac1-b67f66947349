import logging

from app.services.ai_tools.agent_handlers import handler_registry
from app.services.ai_tools.agent_handlers import ChatHandlers


class AgentFactory:
    @staticmethod
    def get_function(param):
        # 将params传递给对应的处理函数
        chat_handers = ChatHandlers()
        if param.get("type") == "knowledge_base_search":
            return chat_handers.knowledge_base_search
            # return handler_registry.get("knowledge_base_search", lambda **kwargs: None)
        elif param.get("type") == "completions":
            return chat_handers.completions
            # return handler_registry.get("completions", lambda **kwargs: None)
        elif param.get("type") == "file_chat":
            return chat_handers.file_chat
            # return handler_registry.get("file_chat", lambda **kwargs: None)
        elif param.get("type") == "agent_chat":
            return chat_handers.agent_chat
        elif param.get("type") == "ai_search":

            return chat_handers.ai_search
        elif param.get("type") == "open_chat":
            return chat_handers.open_chat
        elif param.get("type") == "open_agent_chat":
            return chat_handers.open_agent_chat
        else:
            logging.error(f"未找到对应的处理函数: {param}")
