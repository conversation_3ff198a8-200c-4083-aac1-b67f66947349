import threading
from functools import wraps
from redis import Redis
from redis.asyncio import Redis as AsyncRedis

from app.config import settings


def singleton(cls):
    """线程安全的单例装饰器"""
    instances = {}
    lock = threading.Lock()

    @wraps(cls)
    def wrapper(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:  # 双重检查锁定
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return wrapper


@singleton
class RedisSession(Redis):
    def __init__(self):
        super().__init__(
            host=settings.redis.host,
            port=settings.redis.port,
            db=settings.redis.db,
            password=settings.redis.passwd,
        )


@singleton
class AsyncRedisSession(AsyncRedis):
    def __init__(self):
        super().__init__(
            host=settings.redis.host,
            port=settings.redis.port,
            db=settings.redis.db,
            password=settings.redis.passwd,
        )


if __name__ == "__main__":

    def test_op():
        key = "test_key_01"
        value = "test_value_01"
        with RedisSession() as redis:
            redis.set(key, value)
            res = redis.get(key)
            data = res.decode("utf-8") if res else None
            print("test op", data)
            assert value == data

    async def test_async_op():
        key = "test_key_02_async"
        value = "test_value_02_async"
        async with AsyncRedisSession() as redis:
            await redis.set(key, value)
            res = await redis.get(key)
            print("test async op", res)
            assert value == res

    test_op()
