import urllib.parse
from typing import List, Optional, AsyncGenerator
from fastapi import APIRouter, Form, Response, UploadFile, File, Body
from fastapi.responses import StreamingResponse
from sse_starlette import EventSourceResponse
from app.api.response import APIResponse
from app.api.v1.client.schemas.ai_knowledge_base import CCKnowledgeBaseCreateInput, CCKnowledgeBaseDeleteDocsInput, CCKnowledgeBaseDeleteInput, CCKnowledgeBaseRecreateSummaryVectorStoreInput, CCKnowledgeBaseRecreateVectorStoreInput, CCKnowledgeBaseSearchDocsInput, CCKnowledgeBaseSearchTempDocsInput, CCKnowledgeBaseSummaryDocsIdsToVectorStoreInput, CCKnowledgeBaseSummaryFileToVectorStoreInput, CCKnowledgeBaseUpdateDocsInput, CCKnowledgeBaseUpdateInfoInput
from app.const.response import MessageCode
from app.services.chatchat.knowledge_base import CCKnowledgeBaseService

router = APIRouter()

class KnowledgeBaseApi:
    @staticmethod
    @router.get('/list')
    async def list():
        """
        知识库列表

        输出字段:

            - id: 知识库ID
            - kb_name: 知识库名称
            - kb_info: 知识库简介
            - vs_type: 向量库类型
            - embed_model: Embeddings模型
            - file_count: 文件数量
            - create_time: 创建时间

        """
        try:
            data = await CCKnowledgeBaseService().list_knowledge_base()
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post('/create')
    async def create(data: CCKnowledgeBaseCreateInput):
        """
        创建知识库

        输入字段:

            - knowledge_base_name: 知识库名称
            - vector_store_type: 向量库类型
            - kb_info: 知识库简介
            - embed_model:Embeddings模型

        """
        try:
            await CCKnowledgeBaseService().create_knowledge_base(data)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post('/delete')
    async def delete(data:CCKnowledgeBaseDeleteInput):

        """
        删除知识库

        输入字段:

            - knowledge_base_name 知识库名称

        """
        try:
            await CCKnowledgeBaseService().delete_knowledge_base(data.knowledge_base_name)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
    
    @staticmethod
    @router.post('/update_info')
    async def update_info(data: CCKnowledgeBaseUpdateInfoInput):
        """
        更新知识库简介

        输入字段:

            - knowledge_base_name: 知识库名称
            - kb_info: 知识库简介
        """
        try:
            await CCKnowledgeBaseService().update_info(data)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.get('/file/list')
    async def file_list(knowledge_base_name: str):
        """
        知识库文件列表

        输入字段:

            - knowledge_base_name：知识库名称
        """
        try:
            data = await CCKnowledgeBaseService().list_files(knowledge_base_name)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
        
    @staticmethod
    @router.post('/file/search_docs')
    async def file_search_docs(data: CCKnowledgeBaseSearchDocsInput):
        """
        知识库文件搜索

        输入字段:
            - query: 查询内容
            - knowledge_base_name：知识库名称
            - top_k：返回数量
            - score_threshold：分数阈值
            - file_name：文件名称
            - metadata：元数据
        """
        try:
            data = await CCKnowledgeBaseService().search_docs(data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)

    @staticmethod
    @router.post('/file/upload')
    async def file_upload(
            files: List[UploadFile] = File(..., description="上传文件，支持多文件"),
            knowledge_base_name: str = Form(..., description="知识库名称"),
            override: bool = Form(False, description="覆盖已有文件"),
            to_vector_store: bool = Form(True, description="上传后是否进行向量化"),
            chunk_size: Optional[int] = Form(750, description="知识库单段文本最大长度"),
            chunk_overlap: Optional[int] = Form(150, description="知识库单段文本重合长度"),
            zh_title_enhance: bool = Form(False, description="是否开启中文标题增强"),
            docs: Optional[str] = Form('', description="自定义的docs，需要为json字符串"),
            not_refresh_vs_cache: bool = Form(False, description="暂不保存向量库(用于FAISS)")
        ):
        """上传文件到知识库，并/或进行量化"""
        try:
            await CCKnowledgeBaseService().upload_docs(knowledge_base_name, override, to_vector_store, chunk_size,
                                                 chunk_overlap, zh_title_enhance, docs, not_refresh_vs_cache, files)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
   
    @staticmethod    
    @router.post('/file/delete')
    async def file_delete(data: CCKnowledgeBaseDeleteDocsInput):
        """
        删除知识库文件
        
        输入字段:

            -knowledge_base_name: 知识库名称
            -file_names: 文件名列表
            -delete_content: 是否删除文件内容 使用默认值True
            -not_refresh_vs_cache: 暂不保存向量库(用于FAISS) 使用默认值True
        """
        try:
            await CCKnowledgeBaseService().delete_docs(data)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
     
    @staticmethod
    @router.post('/file/update_docs')
    async def file_update_docs(data: CCKnowledgeBaseUpdateDocsInput):
        """
        更新知识库文件

        输入字段:

            - knowledge_base_name: 知识库名称
            - file_names: 文件名列表
            - chunk_size: 知识库单段文本最大长度
            - chunk_overlap: 知识库单段文本重合长度
            - zh_title_enhance: 是否开启中文标题增强
            - override_custom_docs: 覆盖已有文件
            - docs: 自定义的docs，需要为json字符串
            - not_refresh_vs_cache: 暂不保存向量库(用于FAISS)
        """
        try:
            await CCKnowledgeBaseService().update_docs(data)
            return APIResponse()
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
    
    @staticmethod
    @router.get('/file/download')
    async def file_download(knowledge_base_name: str, file_name: str, pretty: bool = False):
        """下载知识库文件"""
        try:
            result = await CCKnowledgeBaseService().download_docs(knowledge_base_name, file_name, pretty)
            filename = file_name.split('/')[-1]
            inline = 'inline' if pretty else 'attachment'
            filename_encoded = urllib.parse.quote(filename)
            result.headers["Content-Disposition"] = f"{inline}; filename*=UTF-8''{filename_encoded}"
            return result
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
    
    @staticmethod
    @router.post('/file/recreate_vector_store')
    async def file_recreate_vector_store(data: CCKnowledgeBaseRecreateVectorStoreInput):
        """重新创建向量库"""
        try:
            return EventSourceResponse(CCKnowledgeBaseService().recreate_vector_store(data))
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)


    @staticmethod
    @router.post('/file/upload_temp')
    async def file_upload_temp(
            files: List[UploadFile] = File(..., description="上传文件，支持多文件"),
            prev_id: str = Form('', description="前知识库ID"),
            chunk_size: Optional[int] = Form(750, description="知识库单段文本最大长度"),
            chunk_overlap: Optional[int] = Form(150, description="知识库单段文本重合长度"),
            zh_title_enhance: bool = Form(False, description="是否开启中文标题增强"),
        ):
        """上传文件到临时目录"""
        try:
            data = await CCKnowledgeBaseService().upload_temp_docs(prev_id, chunk_size, chunk_overlap, zh_title_enhance, files)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)     
        
    @staticmethod
    @router.post('/file/search_docs_temp')
    async def file_search_docs_temp(data: CCKnowledgeBaseSearchTempDocsInput):
        """
        临时知识库文件搜索

        输入字段:

            - knowledge_id: 临时文件id
            - query: 查询内容
            - top_k：返回数量
            - score_threshold：分数阈值
        """
        try:
            data = await CCKnowledgeBaseService().search_temp_docs(data)
            return APIResponse(data)
        except Exception as e:
            return APIResponse(str(e), MessageCode.SYSTEMERR)
    
    # @staticmethod
    # @router.post('/file/summary/file_to_vector_store')
    # async def file_summary_file_to_vector_store(data: CCKnowledgeBaseSummaryFileToVectorStoreInput):
    #     """
    #     单个知识库根据文件名称摘要

    #     输入字段:

    #         -knowledge_base_name: 知识库名称
    #         -file_name: 文件名
    #         -allow_empty_kb: 是否允许空知识库
    #         -vs_type: 向量库类型
    #         -embed_model: Embeddings模型
    #         -file_description: 文件描述
    #         -model_name: 模型名称
    #         -temperature: 温度参数
    #         -max_tokens: 最大token数
    #     """
    #     try:
    #         await CCKnowledgeBaseService().summary_file_to_vector_store(data)
    #         return APIResponse({})
    #     except Exception as e:
    #         return APIResponse({}).render(str(e), MessageCode.SYSTEMERR)

    # @staticmethod
    # @router.post('/file/summary/docs_ids_to_vector_store')
    # async def file_summary_docs_ids_to_vector_store(data: CCKnowledgeBaseSummaryDocsIdsToVectorStoreInput):
    #     """
    #     单个知识库根据doc_ids摘要

    #     输入字段:

    #         -knowledge_base_name: 知识库名称
    #         -doc_ids: 文档ID列表
    #         -vs_type: 向量库类型
    #         -embed_model: Embeddings模型
    #         -file_description: 文件描述
    #         -model_name: 模型名称
    #         -temperature: 温度参数
    #         -max_tokens: 最大token数
    #     """
    #     try:
    #         await CCKnowledgeBaseService().summary_docs_ids_to_vector_store(data)
    #         return APIResponse({})
    #     except Exception as e:
    #         return APIResponse({}).render(str(e), MessageCode.SYSTEMERR)
        
    # @staticmethod
    # @router.post('/file/recreate_summary_vector_store')
    # async def file_recreate_summary_vector_store(data: CCKnowledgeBaseRecreateSummaryVectorStoreInput):
    #     """
    #     重建单个知识库文件摘要

    #     输入字段:

    #         -knowledge_base_name: 知识库名称
    #         -allow_empty_kb: 是否允许空知识库
    #         -vs_type: 向量库类型
    #         -embed_model: Embeddings模型
    #         -file_description: 文件描述
    #         -model_name: 模型名称
    #         -temperature: 温度参数
    #         -max_tokens: 最大token数
    #     """
    #     try:
    #         await CCKnowledgeBaseService().recreate_summary_vector_store(data)
    #         return APIResponse({})
    #     except Exception as e:
    #         return APIResponse({}).render(str(e), MessageCode.SYSTEMERR)
