import os
import json
from urllib.parse import quote
import traceback
from fastapi import APIRouter, Depends, File, UploadFile, Form
from fastapi.responses import StreamingResponse
from sse_starlette import EventSourceResponse
from sqlalchemy.orm import Session
from app.api.v1.client.schemas.ai_manus import (
    CreateTask,
    CreateTaskManual,
    DeleteTask,
    ListTask,
    RunTask,
    UpdateStepChain,
)
from app.db.minio_client import _YF_AGENT_FILE_BASE_URL, MinioClient
from app.db.mysql import get_mcp_db
from app.services.flow import TaskStatus, build_openai_response, merge_messages, now_date_str
from app.services.flow.step_chain_builder import Step<PERSON>hainBuilder
from app.services.flow.step_chain_runner import Step<PERSON>hainRunner
from app.services.flow.namespace import FlowNamespace, TaskType
from app.api.response import APIResponse
from app.const.response import MessageCode
from app.services.flow.step_chain_builder import get_function_calls_list
from app.services.flow.step_chain_runner import get_step_chain_runner_config

from app.log import logger
from app.services.sandbox.manager import SandboxManager

router = APIRouter()

# 先临时写死
config = get_step_chain_runner_config()


@staticmethod
@router.post("/task/create")
async def create_task_by_question(req: CreateTask, session: Session = Depends(get_mcp_db)):
    """
    创建任务 (方式1: 通过问题自动创建)。一个任务包含: 问题, 步骤链
    """
    try:
        step_chain_builder = StepChainBuilder(deepseek_api_url=config["deepseek_api_url"])
        step_chain_info = await step_chain_builder.create_step_chain(req.question)
        # 大模型拆解任务链的过程
        # step_chain_answer = step_chain_info["answer"]
        step_chain = step_chain_info["step_chain"]  # 任务链
        step_chain_answer = step_chain_info["step_chain_answer"]  # 根据问题生成的第一次的答案(包含任务链)
        fns = FlowNamespace(
            session=session,
            step_chain=step_chain,
            question=req.question,
            step_chain_answer=step_chain_answer,
        )  # 生成任务链
        return APIResponse({"id": fns.id, "step_chain": step_chain, "question": req.question})
    except Exception as e:
        logger.error(f"Error: {traceback.format_exc()}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/task/create/manual")
async def create_task_by_user_upload(
    file: UploadFile = File(...),
    user_id: int = Form(default=0, title="用户ID", description="默认为0（未指定用户）", ge=0),
    session: Session = Depends(get_mcp_db),
):
    """
    创建任务(方式2: 通过手动上传json文件)

    上传JSON文件并解析其内容，json中必须包含 question(问题) 和 step_chain(步骤链) 两个字段
    ```json
    {
        "question": "制作一个关于健康的ppt",
        "step_chain": [
            {
                "step": "1",
                "question": "确定PPT的主题和内容需求",
                "call_function": "普通对话",
            },
            {
                "step": "2",
                "question": "制作PPT的内容",
                "call_function": "代码执行",
            },
            {
                "step": "3",
                "question": "生成最终的PPT文件",
                "call_function": "代码执行",
            }
        ]
    }
    ```
    """
    try:
        if not file.filename.endswith(".json"):
            return APIResponse("文件类型必须是JSON", MessageCode.SYSTEMERR)
        try:
            contents = await file.read()
            parsed_data = json.loads(contents)
        except json.JSONDecodeError as e:
            return APIResponse(f"JSON解析错误: {str(e)}", MessageCode.SYSTEMERR)
        question = parsed_data.get("question", "")
        if not question:
            return APIResponse(f"无效的问题", MessageCode.SYSTEMERR)
        step_chain = parsed_data.get("step_chain")
        if not (isinstance(step_chain, list) and len(step_chain) > 0):
            return APIResponse(f"步骤链必须为数组,且长度大于1", MessageCode.SYSTEMERR)
        new_step_chain = []
        for index, step in enumerate(step_chain):
            step_question = step.get("question", "")
            if not step_question:
                return APIResponse(f"step_chain中存在无效的问题", MessageCode.SYSTEMERR)
            call_function = step["call_function"]
            component_name, component_id = StepChainBuilder.get_component_name_and_id(call_function)
            new_step_chain.append(
                {
                    "step": f"{index + 1}",
                    "question": step_question,
                    "call_function": call_function,
                    "component_name": component_name,
                    "component_id": component_id,
                }
            )
        fns = FlowNamespace(
            session=session,
            step_chain=new_step_chain,
            question=question,
            step_chain_answer="",
            task_type=TaskType.MANUAL.value,
        )
        return APIResponse({"id": fns.id, "step_chain": new_step_chain, "question": question})
    except Exception as e:
        logger.error(f"Error: {traceback.format_exc()}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)
    finally:
        # 关闭文件
        await file.close()


@staticmethod
@router.post("/task/update_step_chain")
async def update_step_chain_by_id(req: UpdateStepChain, session: Session = Depends(get_mcp_db)):
    """
    更新任务链
    """
    try:
        # TODO: 检查前端传过来的step_chain是否合法
        fns = FlowNamespace.get_instance_from_id(req.id, session)
        meta = fns.get_meta()
        if meta is None:
            return APIResponse("任务已不存在\n", MessageCode.SYSTEMERR)
        if meta["status"] == TaskStatus.RUNNING.value:
            return APIResponse("任务正在运行中\n", MessageCode.SYSTEMERR)
        for step in req.step_chain:
            function_call = step["call_function"].strip()
            if step["component_name"] not in step:
                component_name, component_id = StepChainBuilder.get_component_name_and_id(function_call)
                step["component_name"] = component_name
                step["component_id"] = component_id
        fns.update_step_chain(json.dumps(req.step_chain))
        fns.update_meta("step_chain", json.dumps(req.step_chain))
        return APIResponse({"id": req.id})
    except Exception as e:
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/task/run")
async def run_one_task(req: RunTask, session: Session = Depends(get_mcp_db)):
    """
    创建任务(包括问题拆解和任务创建及执行; 流式返回)
    1. 事件类型(node_event): 日志
    ```json
     {
        "event": "log",
        "payload": {
            "msg": " 问题: 中国国家民族事务委员会最新官方统计显示，截至2023年，中国共有多少个民族（含汉族）？, 上下文: ...",
            "level": "info",
            "created_at": "2025-05-27 13:00:35",
            "call_function": "知识库查询",
            "component_name": "knowledge_base",
            "component_id": "knowledge_base:54938d",
            "step": "1"
        }
    }
    ```
    2. 事件类型: 节点输入输出等信息
    ```json
    {
        "event": "input_and_output",
        "payload": {
            "input": "中国官方统计的民族数量是多少？",
            "output": "未找到答案。",
            "output_files": "节点生成的文件，格式如下 : [{filename: 文件名, url: url地址}]",
            "prompt_used_tokens": "提示词消耗的token数量",
            "history_used_tokens": "对话历史消耗的token数量",
            "answer_used_tokens": "答案消耗的token数量",
            "total_used_tokens": "当前节点消耗的总的token数量",
            "use_ts": 0.62,
            "current_date": "2025-05-27 13:00:25",
            "component_name": "knowledge_base",
            "component_id": "knowledge_base:54938d",
            "call_function": "knowledge_base:54938d",
            "step": "1",
            "step_question": "当前步骤的问题",
            "retry_count": "重试次数",
        }
    }
    ```
    3. 事件类型: 节点总统计信息
    ```json
    {
        "event": "node_msg_stat",
        "payload": {
            "use_ts": "节点运行总耗时",
            "prompt_used_tokens": "提示词使用的token数量",
            "history_used_tokens": "历史会话使用的token数量",
            "answer_used_tokens": "回答使用的token数量",
            "total_used_tokens": "总使用的token数量",
            "current_date": "2025-05-27 13:00:25",
        },
    }
    """
    try:
        # 这里可以添加代码来处理任务，例如将其存储在数据库中或发送到消息队列等
        async def try_run_task():
            fns = FlowNamespace.get_instance_from_id(req.id, session)
            step_chain = fns.get_step_chain()
            meta = fns.get_meta()
            if meta is None:
                raise ValueError("任务不存在")
            start_task_content = "开始执行任务链...\n"
            yield json.dumps(build_openai_response(req.id, content=start_task_content), ensure_ascii=False) + "\n"
            try:
                runner = StepChainRunner(**config, flow_namespace=fns)
                async for msg in runner.process_task():
                    yield json.dumps(msg, ensure_ascii=False) + "\n"
                logger.info("Task Down")
            except Exception as e:
                logger.error(traceback.format_exc())
                sys_error = f"\n**系统错误**: {e}"
                yield json.dumps(build_openai_response(req.id, content=sys_error), ensure_ascii=False) + "\n"
                fns.add_chat_history({"role": "assistant", "content": sys_error})
            # TODO: 容器清理逻辑
            # TODO: 工作区处理逻辑, 删除工作区目录。 这个清理可以放到析构函数里面
            if fns.sandbox_id is not None:
                await SandboxManager().delete_sandbox(fns.sandbox_id)

        return EventSourceResponse(try_run_task(), ping=60 * 60 * 24)  # 前端会收到ping消息，暂时设置为24小时
    except Exception as e:
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/task/delete")
def delete_task(req: DeleteTask, session: Session = Depends(get_mcp_db)):
    """
    删除任务
    """
    try:
        # TODO: 运行中的任务不能删
        FlowNamespace.delete_by_id(req.id, session)
        return APIResponse({"id": req.id})
    except Exception as e:
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.get("/task/list")
def list_task(req: ListTask = Depends(), session: Session = Depends(get_mcp_db)):
    """
    返回任务列表(任务状态, 默认返回所有任务, 0-未开始, 1-进行中, 2-已完成, 3-已失败, 4-已取消")
    """
    try:
        # TODO: 状态判断, TODO: 分页
        all_task = FlowNamespace.get_all_ns(session, page_size=1000)
        return APIResponse(all_task)
    except Exception as e:
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.get("/task/{id}")
def get_task(id: str, session: Session = Depends(get_mcp_db)):
    """
    返回任务详情
    """
    try:
        fns = FlowNamespace.get_instance_from_id(id, session)
        chat_histories = fns.get_chat_histories()
        outputs = fns.get_outputs()
        step_chain = fns.get_step_chain()
        logs = fns.get_logs()
        stat = fns.get_stat()

        merged_messages = []
        if chat_histories is not None:
            # 合并对话: 前端容易处理
            merged_messages = merge_messages(chat_histories)

        return APIResponse(
            {
                "id": id,
                "step_chain": step_chain,
                "input_and_output": outputs,
                "message": merged_messages,
                "log": logs,
                "stat": stat,
            }
        )
    except Exception as e:
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.get("/function_calls")
def get_function_calls():
    result = get_function_calls_list()
    return APIResponse(result)


@router.get(f"{_YF_AGENT_FILE_BASE_URL}/{{filename:path}}")
def minio_proxy_download(filename: str):
    """
    下载 Manus 生成的文件
    """
    try:
        filename = filename.lstrip("/")
        # 防止路径遍历攻击
        if "../" in filename:
            raise APIResponse("无效的文件名", MessageCode.SYSTEMERR)
        logger.info(f"Downloading file: {filename}")
        # 从MinIO获取文件流
        minio_client = MinioClient()
        bucket_name = minio_client.get_manus_files_bucket_name()
        blob = minio_client.get(bucket_name, filename)
        if not blob:
            raise APIResponse("获取文件失败", MessageCode.SYSTEMERR)
        # 构建响应（自动识别MIME类型）
        filename_base = os.path.basename(filename)
        if filename_base.endswith(".pdf"):
            content_type = "application/pdf"
        elif filename_base.endswith((".png", ".jpg", ".jpeg")):
            ext = filename_base.split(".")[-1].lower()
            content_type = f"image/{ext}"
        else:
            content_type = "application/octet-stream"
        response = StreamingResponse(iter([blob]), media_type=content_type)
        # 3. 使用StreamingResponse处理字节流
        response = StreamingResponse(
            iter([blob]),  # 直接将字节流包装为迭代器
            media_type=content_type,
            # 设置为作为附件下载。 使用 UTF-8 编码支持中文和特殊字符
            headers={"Content-Disposition": f"attachment; filename*=UTF-8''{quote(filename_base)}"},
        )
        return response
    except Exception as e:
        logger.warning(f"Failed to download file: {e}")
        # 修改7: 返回统一的API响应格式
        return APIResponse(f"未查询到该文件:{filename}", MessageCode.SYSTEMERR)
