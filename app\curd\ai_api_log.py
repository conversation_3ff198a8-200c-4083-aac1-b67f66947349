from sqlalchemy.orm import Session
from sqlalchemy import select, delete, func, update
import sqlalchemy.exc
import copy

from app.db import get_session
from app.models.ai_api_log import AiApiLog
from app.log import logger
from app.const.isdelete import IS_DELETE, IS_NOT_DELETE


class AiApiLogCurd(object):

    def __init__(self, db: Session):
        self.db = db

    # def __exit__(self, exc_type, exc_val, exc_tb):
    #     self.db.close()
    @staticmethod
    def add_log(api_log: dict):
        with get_session() as session:
            log_obj = AiApiLog(**api_log)
            session.add(log_obj)
            session.commit()
        return

    def get_list(self, offset: int, page_size: int, name: str, group_id: str, ctime: str,process_time: str,
                 is_root: bool) -> tuple[int, list[AiApiLog]]:
        offset = (offset - 1) * page_size
        count_stmt = select(func.count(AiApiLog.id)).filter(AiApiLog.is_delete == IS_NOT_DELETE)
        stmt = select(AiApiLog).filter(AiApiLog.is_delete == IS_NOT_DELETE)
        if not is_root:
            stmt = stmt.filter(AiApiLog.group_id == group_id)
            count_stmt = count_stmt.filter(AiApiLog.group_id == group_id)

        if name:
            count_stmt = count_stmt.filter(AiApiLog.username.like(f'%{name}%'))
            stmt = stmt.filter(AiApiLog.username.like(f'%{name}%'))

        if ctime:
            count_stmt = count_stmt.filter(AiApiLog.ctime >= ctime)
            stmt = stmt.filter(AiApiLog.ctime >= ctime)
        if process_time:
            stmt = stmt.order_by(AiApiLog.process_time.desc()).offset(offset).limit(page_size)
        else:
            stmt = stmt.order_by(AiApiLog.id.desc()).offset(offset).limit(page_size)
        try:
            count_r = self.db.execute(count_stmt)
            words_r = self.db.execute(stmt)
            return count_r.scalar(), [word for word in words_r.scalars()]
        except Exception as e:
            raise Exception(f"获取敏感词分页出错: {str(e)}")

    def delete_by_ids(self, ids: list[str], group_id: str, is_root: bool) -> int:
        stmt = update(AiApiLog).where(AiApiLog.id.in_(ids)).values({"is_delete": IS_DELETE})
        if not is_root:
            stmt = stmt.where(AiApiLog.group_id == group_id)
        result = self.db.execute(stmt)
        self.db.commit()
        return result.rowcount
