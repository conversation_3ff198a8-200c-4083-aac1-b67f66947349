from typing import Optional
from pydantic import BaseModel, Field, model_validator


class ChatParams(BaseModel):
    query: str = Field(description="对话内容")
    content: Optional[list] = Field(default=None, description="open ai 消息内容")
    prompt: Optional[str] = Field(default="", description="系统提示词")
    kb_name: Optional[str] = Field(default=None, description="知识库名称,如果type= knowledge_base_search必传")
    knowledge_id: Optional[str] = Field(default=None, description="知识库名称,如果type= file_chat必传")
    top_k: Optional[int] = Field(default=3, description="返回的前 k 个结果,如果type= knowledge_base_search必传")
    score_threshold: Optional[int] = Field(default=2, description="分数阈值,如果type= knowledge_base_search必传")
    stream: Optional[bool] = Field(default=True, description="是否流式返回结果")
    model: Optional[str] = Field(default="deepseek-r1-distill-qwen", description="使用的模型名称")
    temperature: Optional[float] = Field(default=0.7, description="温度参数，控制随机性")
    max_tokens: Optional[int] = Field(default=8192, description="最大生成的 token 数")
    type: str = Field(
        default="completions",
        description="knowledge_base_search 知识库；completions 普通对话; file_chat 文件对话；open_chat 开放 api",
    )
    frequency_penalty: Optional[int] = Field(default=None, description="频率惩罚")
    presence_penalty: Optional[int] = Field(default=None, description="存在惩罚")
    n: Optional[int] = Field(default=3, description="生成的数量")
    top_p: Optional[int] = Field(default=1, description="top_p ")
    session_id: Optional[str] = Field(default="", description="助手id")
    chat_id: Optional[str] = Field( description="会话id，和user_id关联，用户查找对话上下文 ")
    user_id: Optional[int] = Field( description="用户id，用户查找用户会话列表 ")
    agent_id: Optional[str] = Field(default="",description="工具id，后续会动态维护，现在暂时固定 数据分析固定1")

    @model_validator(mode='after')
    def validate_knowledge_base_search(cls, values):
        if values.type == 'knowledge_base_search':
            required_fields = ['kb_name']
            for field in required_fields:

                if getattr(values, field) is None:
                    raise ValueError(f"当 type 为 knowledge_base_search 时，{field} 字段是必需的。")
                # if field == "score_threshold":
                #     if getattr(values, field) > 2 and getattr(values, field) < 0:
                #         raise ValueError(f"score_threshold 字段的值必须在 0 到 2 之间。")
        return values


class CreateChatSessionParams(BaseModel):
    session_id: Optional[str] = Field(default=None, description="会话id，如果不传则自动生成")
    name: Optional[str] = Field(default=None, description="会话名称")
    user_id: int = Field(description="用户id")

class CreateHtml(BaseModel):
    code: Optional[str] = Field( description="代码")

#
# {
#
#     "model": "/data/DeepSeek-R1-Distill-Qwen-14B/",
#     "frequency_penalty": 0,
#     "prompt": "你是一个机器人",
#     "max_tokens": 4096,
#     "n": 1,
#     "presence_penalty": 0,
#
#     "temperature": 0.7,
#     "top_p": 1
# }
# {
#     "query": "2024年业绩怎么样",
#     "kb_name": "永信至诚",
#     "top_k": 3,
#     "score_threshold": 2,
#     "model": "glm4-9b-1m",
#     "temperature": 0.7,
#     "max_tokens": 0,
# }
