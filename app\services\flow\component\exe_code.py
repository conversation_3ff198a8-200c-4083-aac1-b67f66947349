import os
import re
from copy import deepcopy
import traceback
from app.services.flow import logger, now_ts
from app.services.flow.component.base import ComponentBase
from app.services.flow.namespace import FlowNamespace


class ExeCode(ComponentBase):
    function_call = "代码执行"
    component_name = "exe_code"
    desc = "通过用户的问题, 生成对应的代码, 然后在本地执行, 返回执行结果。使用场景: 比如根据问题和数据生成PPT, Excel等文件; 根据问题生成代码, 进行数据处理等。"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        super().__init__(namespace, **kwargs)

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """调用代码执行工具（新增context参数）"""
        start_ts = now_ts()
        question = step["question"]
        context = self.get_last_context(step_result_list)
        log_msg = f"代码执行: 问题: {question}\n 问题上下文: {context[:200]}..."
        logger.info(log_msg)
        yield self.namespace.add_log(log_msg, step)
        script_name = f"my_script_{step['step']}.py"
        python_code = ""
        python_code_workspace = "/workspace"
        prompt = f"""
        根据问题生成Python脚本。只返回代码，不需要解释。
        如果需要数据，但用户又没有提供工具, 则在脚本中生成一些简单的数据demo。
        注意: python执行的系统的环境是最小化安装的Linux系统。特别注意生成的脚本不要删除当前工作目录及父目录及系统文件。
        返回的代码块，请用 Markdown语法包裹, 例如: ```python\nimport os``` 或 ```bash\nls```。
        如果python脚本执行后会生成数据文件, 请返回文件的绝对路径, 如果生成多个数据文件, 请将最重要的,最终生成的文件放在最前面显示, 
        请注意，这些产生的文件请用Markdown语法包裹返回，类型为: my_file，例如: ```my_file\n {python_code_workspace}/data.pptx```。
        当前python脚本的工作目录是: {python_code_workspace} 。
        我会把脚本的内容保存为: {script_name} 。
        如果脚本需要其他依赖包，请返回pip安装的bash命令脚本, 这个安装命令最多只能是一行, 且安装源设置为: https://pypi.tuna.tsinghua.edu.cn/simple。
        """
        code_generation_messages = [
            *self.get_last_chat_content(step_result_list),
            {"role": "system", "content": prompt},
            {"role": "user", "content": f"请根据上下文历史对话, 生成代码, 处理我的问题: {question}"},
        ]
        result = {
            "success": False,
            "cmd": "",
            "code": "",
            "result": "代码生成失败",
            "answer_file": "",
        }
        while attempt_count > 0:
            try:
                # 先调用DeepSeek生成代码
                success, answer = await self.call_llm(code_generation_messages)
                if not success:
                    logger.info(f"调用模型失败: {answer}")
                    retry_count -= 1
                    continue
                logger.info(f"LLM answer: {answer}")
                python_code = self._refactor_python_code(answer)
                if not python_code:
                    log_msg = "代码生成失败, 尝试重新生成代码"
                    logger.info(log_msg)
                    yield self.namespace.add_log(log_msg, step)
                    async for data in self._optimize_code(
                        python_code, script_name, "未发现代码", code_generation_messages, step
                    ):
                        if data.get("event"):
                            yield data
                        else:
                            result = data
                else:
                    yield self.namespace.add_log(f"已生成代码, 尝试执行: ```python\n{python_code}```", step)
                    install_request_cmd = self._refactor_shell_code(answer)
                    result = await self.namespace.exe_python(python_code, script_name, install_request_cmd)
                    yield self.namespace.add_log(f"已执行代码: {self._format_result_str(result)}", step)
                    if not result["success"]:
                        res: str = result["result"]  # type: ignore
                        async for data in self._optimize_code(
                            python_code, script_name, res, code_generation_messages, step
                        ):
                            if data.get("event"):
                                yield data
                            else:
                                result = data
                    else:
                        result_filename = self._refactor_data_filename(answer)
                        if not result_filename:
                            result_filename = await self._refactor_data_filename_from_code(python_code)
                        result["answer_file"] = result_filename
                        logger.info(f"结果是: {result}")
                break
            except Exception as e:
                attempt_count -= 1
                error_msg = f"代码生成失败: {traceback.format_exc()}"
                logger.error(error_msg)
                yield self.namespace.add_log(error_msg, step)
                async for data in self._optimize_code(
                    python_code, script_name, "代码异常", code_generation_messages, step
                ):
                    if data.get("event"):
                        yield data
                    else:
                        result = data

        filepath = result["answer_file"]
        # TODO: 优雅的处理文件路径
        if isinstance(filepath, str):
            logger.info(f"文件路径: old: {filepath}")
            filepath = filepath.replace(python_code_workspace, self.namespace.current_workspace_abs_path, 1)
            logger.info(f"文件路径: new: {filepath}")
        if isinstance(filepath, str) and os.path.exists(filepath):
            success, url_msg = self.upload_file_to_storage(filepath)
            if not success:
                raise ValueError("上传文件失败")
            self.output_files_urls.append(url_msg)
            result["answer_file"] = url_msg["filename"]
        else:
            result["answer_file"] = ""
        result_str = self._format_result_str(result)
        formatted_input = f"{question}\n 已知的问题的上下文是: {context[:512]}..." if context is None else f"{question}"
        yield self.save_input_and_output(formatted_input, result_str, start_ts, step)
        yield result_str

    async def _optimize_code(self, code, script_name, error, code_generation_messages, step, retry_count=5):
        result = {
            "success": False,
            "cmd": "",
            "code": "",
            "result": "代码生成失败",
            "answer_file": "",
        }
        while retry_count > 0:
            try:
                # 先调用DeepSeek生成代码
                new_question = (
                    f"请帮我优化下面的代码，请返回修正后的完整代码, 还是用markdown语法包裹, 如果脚本生成了数据文件或bash命令也按照之前的定义返回。\n"
                    + f"以下是之前的代码: {code}\n 上面的代码运行时报错：{error[:2000]}"
                )
                yield self.namespace.add_log("尝试优化代码...", step)
                history = deepcopy(code_generation_messages)
                history.append({"role": "user", "content": new_question})
                success, answer = await self.call_llm(history)
                if not success:
                    logger.info(f"调用模型失败: {answer}")
                    retry_count -= 1
                    continue
                logger.info(f"LLM new answer: {answer}")
                code = self._refactor_python_code(answer)
                if not code:
                    logger.info("代码生成失败")
                    retry_count -= 1
                    continue
                install_request_cmd = self._refactor_shell_code(answer)
                yield self.namespace.add_log(f"已优化代码, 尝试执行: ```python\n{code}```", step)
                result = await self.namespace.exe_python(code, script_name, install_request_cmd)
                if not result["success"]:  # type: ignore
                    retry_count -= 1
                    error = result["result"]
                    continue
                result_filename = self._refactor_data_filename(answer)
                if not result_filename:
                    result_filename = await self._refactor_data_filename_from_code(code)
                result["answer_file"] = result_filename
                break
            except Exception as e:
                error_msg = f"代码生成失败: {traceback.format_exc()}"
                logger.error(error_msg)
                yield self.namespace.add_log(error_msg, step)
                retry_count -= 1
                continue
        yield result

    def _refactor_python_code(self, ans_content):
        ans_content = re.sub(r"<think>.*</think>", "", ans_content, flags=re.DOTALL)
        match = re.search(r"```python\s*(.*?)\s*```", ans_content, re.DOTALL)
        if match:
            script_content = match.group(1)  # Query content
            return script_content
        else:
            return None

    def _refactor_shell_code(self, ans_content):
        ans_content = re.sub(r"<think>.*</think>", "", ans_content, flags=re.DOTALL)
        # match = re.search(r"(?:^|```(?:bash|sh|shell)?\s*\n)((?:(?!```)[^\n<])+)(?:$|\n```)", ans_content, re.DOTALL)
        match = re.search(r"```bash\s*(.*?)\s*```", ans_content, re.DOTALL)
        if match:
            script_content = match.group(1)  # Query content
            return script_content
        else:
            return None

    def _refactor_data_filename(self, ans_content):
        ans_content = re.sub(r"<think>.*</think>", "", ans_content, flags=re.DOTALL)
        match = re.search(r"```my_file\s*(.*?)\s*```", ans_content, re.DOTALL)
        if match:
            script_content = match.group(1)  # Query content
            return script_content
        else:
            return None

    async def _refactor_data_filename_from_code(self, code):
        code_generation_messages = [
            {
                "role": "user",
                "content": f"请查看下面这段代码是否生成了文件, 如果最终生成了文件, 请返回文件的路径, 不需要思思考过程。返回的文件路径请用Markdown语法包裹返回，请注意文件的类型为: my_file，"
                + f"例如: 产生的文件路径是: ```my_file\n /workspace/data.pptx```\n 具体的代码如下: {code}",
            },
        ]
        success, answer = await self.call_llm(code_generation_messages)
        logger.info(f"Refactor filename from answer by code: {answer}")
        if not success:
            return None
        return self._refactor_data_filename(answer)

    def _format_result_str(self, result: dict):
        is_success = "成功" if result["success"] else "失败"
        cmd_msg = result["cmd"] if result["cmd"] else "无"
        python_code = result["code"] if result["code"] else "无"
        cmd_result = result["result"] if result["result"] else "无打印信息"
        output_files = os.path.basename(result.get("answer_file", "")) if result.get("answer_file") else "无生成文件"
        return (
            f"代码是否执行成功: {is_success}; 执行的命令: {cmd_msg}。python脚本的代码内容: \n{python_code}\n"
            + f"代码执行后, 控制台打印的信息: {cmd_result}。代码执行后生成的文件: {output_files}。"
        )


if __name__ == "__main__":
    import json

    namespace = FlowNamespace()
    coder = ExeCode(namespace)
    step = {
        "step": "1",
        "question": "生成一个ppt, 介绍最近一周的销售手机的业绩",
        "call_function": "普通对话",
        "component_name": ExeCode.component_name,
        "component_id": f"{ExeCode.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = coder.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
