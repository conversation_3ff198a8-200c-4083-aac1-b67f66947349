#!/usr/bin/env python3
"""
真实系统测试启动脚本
专门测试任务编排准确性和工具调用准确性
"""

import asyncio
import sys
import os
import json
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_execution.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def print_banner():
    """打印测试横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              AI Agent 真实系统优化测试                        ║
║                                                              ║
║  🎯 测试目标:                                                ║
║    1. 任务编排准确性对比                                     ║
║    2. 工具调用准确性对比                                     ║
║    3. 系统性能改进验证                                       ║
║                                                              ║
║  📊 测试方法:                                                ║
║    - 真实API调用测试                                         ║
║    - 多轮测试取平均值                                        ║
║    - 定量分析优化效果                                        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查必要文件
    required_files = [
        'real_system_test.py',
        'app/services/flow/__init__.py',
        'settings_dev.toml'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件检查通过")
    
    # 检查Python模块
    try:
        import aiohttp
        import asyncio
        print("✅ 依赖模块检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖模块: {e}")
        return False
    
    return True

async def test_api_connectivity():
    """测试API连接性"""
    print("\n🌐 测试API连接性...")
    
    import aiohttp
    
    api_endpoints = [
        ("API服务器", "http://localhost:8081/docs"),
        ("模型服务器", "http://**************:8000/v1/models")
    ]
    
    results = {}
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
        for name, url in api_endpoints:
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        print(f"  ✅ {name}: 连接正常")
                        results[name] = True
                    else:
                        print(f"  ⚠️ {name}: HTTP {response.status}")
                        results[name] = False
            except Exception as e:
                print(f"  ❌ {name}: 连接失败 - {e}")
                results[name] = False
    
    return results

def select_test_mode():
    """选择测试模式"""
    print("\n📋 选择测试模式:")
    print("  1. 快速测试 (基础功能验证)")
    print("  2. 标准测试 (完整功能测试)")
    print("  3. 深度对比测试 (优化效果对比)")
    print("  4. 全面测试 (所有测试)")
    
    while True:
        try:
            choice = input("\n请选择测试模式 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return int(choice)
            else:
                print("请输入有效的选项 (1-4)")
        except (EOFError, KeyboardInterrupt):
            print("\n测试已取消")
            sys.exit(0)

async def run_quick_test():
    """运行快速测试"""
    print("\n🚀 运行快速测试...")
    
    from real_system_test import RealSystemTester
    
    tester = RealSystemTester()
    
    # 只运行前3个简单测试用例
    quick_test_cases = tester.test_cases[:3]
    tester.test_cases = quick_test_cases
    
    try:
        report = await tester.run_all_tests()
        return report
    except Exception as e:
        logger.error(f"快速测试失败: {e}")
        return None

async def run_standard_test():
    """运行标准测试"""
    print("\n🚀 运行标准测试...")
    
    from real_system_test import RealSystemTester
    
    tester = RealSystemTester()
    
    try:
        report = await tester.run_all_tests()
        return report
    except Exception as e:
        logger.error(f"标准测试失败: {e}")
        return None

async def run_comparison_test():
    """运行对比测试"""
    print("\n🚀 运行优化效果对比测试...")
    
    from real_system_test import OptimizationComparisonTester
    
    tester = OptimizationComparisonTester()
    
    try:
        report = await tester.run_comparison_test()
        return report
    except Exception as e:
        logger.error(f"对比测试失败: {e}")
        return None

async def run_comprehensive_test():
    """运行全面测试"""
    print("\n🚀 运行全面综合测试...")
    
    from real_system_test import run_comprehensive_comparison
    
    try:
        report = await run_comprehensive_comparison()
        return report
    except Exception as e:
        logger.error(f"全面测试失败: {e}")
        return None

def print_test_summary(report: dict, test_mode: str):
    """打印测试总结"""
    if not report:
        print(f"\n❌ {test_mode}测试失败，无法生成报告")
        return
    
    print(f"\n📊 {test_mode}测试完成")
    print("=" * 50)
    
    if "test_summary" in report:
        # 标准测试报告
        summary = report["test_summary"]
        print(f"总测试数: {summary.get('total_tests', 0)}")
        print(f"成功测试: {summary.get('successful_tests', 0)}")
        print(f"成功率: {summary.get('success_rate', 0):.1%}")
        print(f"平均执行时间: {summary.get('avg_execution_time', 0):.2f}秒")
        print(f"平均准确性: {summary.get('avg_accuracy_score', 0):.2f}")
        
        # 性能分析
        if "performance_analysis" in report:
            perf = report["performance_analysis"]
            print(f"\n🎯 性能分析:")
            print(f"任务编排准确性: {perf.get('task_orchestration_accuracy', {}).get('step_count_accuracy', 0):.1%}")
            print(f"工具调用准确性: {perf.get('tool_calling_accuracy', {}).get('component_selection_accuracy', 0):.1%}")
    
    elif "comparison_summary" in report:
        # 对比测试报告
        summary = report["comparison_summary"]
        metrics = summary.get("optimization_metrics", {})
        print(f"对比测试数: {summary.get('total_comparison_tests', 0)}")
        print(f"整体准确性: {metrics.get('overall_accuracy', 0):.2f}")
        print(f"任务编排分数: {metrics.get('task_orchestration_score', 0):.2f}")
        print(f"工具调用分数: {metrics.get('tool_calling_score', 0):.2f}")
    
    elif "final_analysis" in report:
        # 综合测试报告
        analysis = report["final_analysis"]
        improvements = analysis.get("key_improvements", {})
        
        print(f"📈 关键改进指标:")
        if "success_rate" in improvements:
            sr = improvements["success_rate"]
            print(f"成功率提升: {sr.get('improvement', 0):.1%}")
        
        if "execution_time" in improvements:
            et = improvements["execution_time"]
            print(f"执行时间改善: {et.get('improvement_percent', 0):.1f}%")
        
        if "accuracy" in improvements:
            acc = improvements["accuracy"]
            print(f"准确率提升: {acc.get('improvement', 0):.1%}")

def save_test_results(report: dict, test_mode: str):
    """保存测试结果"""
    if not report:
        return
    
    timestamp = asyncio.get_event_loop().time()
    filename = f"test_results_{test_mode.lower()}_{int(timestamp)}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n💾 测试结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存测试结果失败: {e}")

async def main():
    """主函数"""
    print_banner()
    
    # 1. 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        sys.exit(1)
    
    # 2. API连接测试
    connectivity = await test_api_connectivity()
    
    if not connectivity.get("API服务器", False):
        print("\n⚠️ API服务器连接失败，某些测试可能无法运行")
        print("请确保API服务器运行在 http://localhost:8081")
        
        user_input = input("是否继续测试? (y/n): ").lower().strip()
        if user_input != 'y':
            print("测试已取消")
            sys.exit(0)
    
    # 3. 选择测试模式
    test_mode = select_test_mode()
    
    test_modes = {
        1: ("快速", run_quick_test),
        2: ("标准", run_standard_test),
        3: ("对比", run_comparison_test),
        4: ("全面", run_comprehensive_test)
    }
    
    mode_name, test_func = test_modes[test_mode]
    
    print(f"\n🎯 开始执行{mode_name}测试...")
    print("⏳ 测试可能需要几分钟时间，请耐心等待...")
    
    # 4. 执行测试
    start_time = asyncio.get_event_loop().time()
    
    try:
        report = await test_func()
        
        end_time = asyncio.get_event_loop().time()
        total_time = end_time - start_time
        
        print(f"\n⏱️ 测试总耗时: {total_time:.2f}秒")
        
        # 5. 显示结果
        print_test_summary(report, mode_name)
        
        # 6. 保存结果
        save_test_results(report, mode_name)
        
        # 7. 生成建议
        print(f"\n💡 测试建议:")
        if report and "test_summary" in report:
            success_rate = report["test_summary"].get("success_rate", 0)
            if success_rate >= 0.8:
                print("  ✅ 系统运行良好，优化效果显著")
            elif success_rate >= 0.6:
                print("  ⚠️ 系统基本正常，建议进一步优化")
            else:
                print("  ❌ 系统存在问题，需要重点改进")
        
        print(f"\n🎉 {mode_name}测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")
        sys.exit(1)
