import aiohttp
from app.services.flow import logger, now_ts
from app.services.flow.component.base import ComponentBase
from app.services.flow.namespace import FlowNamespace


class KnowledgeBase(ComponentBase):
    function_call = "知识库查询"
    component_name = "knowledge_base"
    desc = "该工具可以查询知识库中的数据, 知识库中的内容有: TCT细胞药品, TCR-T, TCR-T药品，TCR-T治疗等信息"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        super().__init__(namespace, **kwargs)

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """调用知识库查询工具（新增context参数）"""
        start_ts = now_ts()
        question = step["question"]
        context = self.get_last_context(step_result_list)
        context_display = context[:200] if context is not None else ""
        log_msg = f"问题: {question}\n上下文: {context_display}..."
        logger.info(log_msg)  # 日志显示上下文摘要
        yield self.namespace.add_log(log_msg, step)
        # 将上下文与问题拼接（示例格式：当前问题\n 上游结果）
        full_question = f"{question}\n 已知问题的上下文是: {context[:4000]}" if context else question
        while attempt_count > 0:
            try:
                payload = {"model": "model", "messages": [{"role": "user", "content": full_question}], "stream": False}
                knowledge_base_url = self.kwargs.get("knowledge_base_url", "")  # TODO: 获取知识库的URL和TOKEN
                knowledge_base_token = self.kwargs.get("knowledge_base_token", "")  # TODO:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60 * 2)) as session:
                    async with session.post(
                        knowledge_base_url,
                        headers={"Content-Type": "application/json", "Authorization": f"Bearer {knowledge_base_token}"},
                        json=payload,
                    ) as response:
                        response.raise_for_status()
                        data = await response.json()
                        result = data["choices"][0]["message"]["content"]
                break
            except Exception as e:
                attempt_count -= 1
                error_msg = f"知识库查询失败: {e}"
                logger.error(error_msg)
                result = error_msg
        formatted_input = f"{full_question[:512]}..." if len(full_question) > 512 else full_question
        yield self.save_input_and_output(formatted_input, result, start_ts, step)
        yield result


if __name__ == "__main__":
    import json

    def get_step_chain_runner_config():
        return {
            "knowledge_base_url": "http://192.168.22.191:81/api/v1/chats_openai/65951946482011f097eb0242ac130006/chat/completions",
            "knowledge_base_token": "ragflow-k0MTA0MDIyNDY3MDExZjA4N2I3MDI0Mm",
        }

    namespace = FlowNamespace()
    config = get_step_chain_runner_config()
    coder = KnowledgeBase(namespace, **config)
    step = {
        "step": "1",
        "question": "TCT细胞药品",
        "call_function": KnowledgeBase.function_call,
        "component_name": KnowledgeBase.component_name,
        "component_id": f"{KnowledgeBase.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = coder.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
