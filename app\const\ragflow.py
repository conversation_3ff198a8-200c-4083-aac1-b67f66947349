from enum import Enum


class RagFlowUrl(str, Enum):
    def __str__(self):
        return self.value

    # 获取知识库列表
    DATASETS_LIST_URL = "/api/v1/datasets"
    # 创建知识库
    DATASET_CREATE_URL = "/api/v1/datasets"
    # 在知识库中上传文件并解析
    DATASET_UPLOAD_AND_PARSE_URL = "/api/v1/datasets/documents/upload_and_parse"

    # 获取agent列表
    AGENTS_LIST_URL = "/api/v1/agents"
    # 获取agent列表（包含总数）
    AGENTS_LIST_SUMMARY_URL = "/api/v1/agents/summary"

    # 创建知识库对话助手
    DATASET_CHAT_ASSISTANT_URL = "/api/v1/chats"

    # 创建知识库助手会话
    DATASET_CHAT_SESSION_URL = "/api/v1/chats/{chat_id}/sessions"

    # 创建AGENT会话
    AGENT_CHAT_SESSION_URL = "/api/v1/agents/{agent_id}/sessions"

    # 加密文件位置
    PUBLIC_KEY_PATH = "./public.pem"

    # 注册接口
    REGISTER_URL = "/v1/user/register"

    # 登录接口
    LOGIN_URL = "/v1/user/login"

    # 更新密码接口
    UPDATE_PASSWORD_URL = "/v1/user/setting"

    # 生成问题
    GENERATE_QUESTIONS_URL = "/api/v1/chats_openai/{chat_id}/chat/generate_questions"
