import requests
import json
import time
from typing import AsyncGenerator
import logging
import uuid

from app.config.config import settings
from app.utils.http import AsyncHTTPRequest
from app.log import logger


class SearchXngEngine:
    def __init__(self):
        self.searxng_url = settings.agent_search.search_api
        self.model_api_url = settings.agent_search.model_api
        self.model = settings.agent_search.model

    def filter_think_process(self, result):
        """过滤掉包含 think 的思考过程"""
        if isinstance(result, str):
            lines = result.split('\n')
            filtered_lines = [line for line in lines if "think" not in line.lower()]
            return '\n'.join(filtered_lines)
        return result

    def check_accuracy(self, original_query, response):
        """简单的召回功能，检测结果是否包含查询关键词，后面在更新"""
        print("-----")
        print(original_query.lower())
        print(response.lower())
        print("-----")
        return original_query.lower() in response.lower()

    def search_searxng(self, query, pageno, engines: str = "bing_news", categories: str = "general",
                       time_range: str = "day", safesearch: int = 2):
        """向SearXNG发送搜索请求"""
        params = {
            "q": query,
            "format": "json",
            "time_range": time_range,
            "categories": categories,
            "safesearch": safesearch,
            "language": "zh-CN",
            "engines": engines,
            "pageno": pageno
        }
        try:
            searxng_response = requests.get(self.searxng_url, params=params)
            searxng_response.raise_for_status()
            return searxng_response.json()
        except requests.RequestException as e:
            print(f"请求SearXNG出错: {e}")
            raise "请求SearXNG出错" from e
    async def get_deepseek_answer(self, combined_summary, query):
        """向DeepSeek API发送请求进行进一步处理"""
        current_datetime = time.strftime('%Y-%m-%d %H:%M:%S')
        deepseek_payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": f"当前时间是{current_datetime},这个是用户提问{query}，请根据以下搜索结果进行分析，如果搜索不到，可回答‘没有找到相关信息’。"
                },
                {
                    "role": "user",
                    "content": f"请分析以下搜索结果: {combined_summary}"
                }
            ],
            "stream": True  # 开启流式输出
        }

        deepseek_headers = {
            "Content-Type": "application/json",
        }
        try:
            request = AsyncHTTPRequest(
                "POST", self.model_api_url, headers=deepseek_headers, params=deepseek_payload
            )

            async for data in request():
                yield data
        except Exception as e:
            print(f"请求DeepSeek出错: {e}")

    def remove_spaces_and_newlines(self, text):
        # 去除所有空格
        text = text.replace(" ", "")
        # 去除所有换行符
        text = ''.join(text.splitlines())
        return text

    def extract_keywords(self, results):
        """从搜索结果中提取关键词"""
        keywords = []
        for result in results.get("results", [])[:5]:
            title = result.get("title", "")
            content = result.get("content", "")
            keywords.extend(title.split())
            keywords.extend(content.split())
        return " ".join(set(keywords))

    async def perform_search(self, query, categories="general",engines="bing_news") -> AsyncGenerator[str, None]:
        infos = []
        max_results = 2
        # 第一次搜索
        searxng_data = self.search_searxng(query=query, pageno=1,categories=categories,engines=engines)
        if searxng_data:
            # 提取搜索结果的摘要信息
            summaries = [result["content"] for result in searxng_data.get("results", []) if "content" in result]
            combined_summary = " ".join(summaries)
            # 向DeepSeek API发送请求进行进一步处理
            requests = self.get_deepseek_answer(combined_summary, query)
            async for data in requests:
                message =  {"data": data, "search_result": None}
                yield message
                # 异步生成器结束后执行这里的代码
            yield {"data": await self.last_message(), "search_result": searxng_data}

        else:
            # 先只重试一次
            if max_results < 2:
                max_results += 1
                # 如果第一次搜索没有结果，进行二次搜索，并检索网页信息
                logging.info()
                self.perform_search(query=query, categories="web",engines="360search")

    async def last_message(self):
        """组装最后一条消息，确保给前端返回的结构是一致的"""
        data = {
            "id": uuid.uuid4().hex,
            "object": "ai_search.completion.chunk",
            "created":  int(time.time()),
            "model": "",
            "choices": [
                {
                    "index": 0,
                    "delta": {
                        "content": "",
                        "sensitive_words": "",
                        "reason_content": ""
                    },
                    "logprobs": None,
                    "finish_reason": "stop",
                    "stop_reason": None
                }
            ]
        }
        return json.dumps(data)


import asyncio

# 示例调用
if __name__ == "__main__":
    query = "北京潮白河坍塌"

    search_engine = SearchXngEngine()
    asyncio.run(search_engine.perform_search(query))
