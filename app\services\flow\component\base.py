import os
import time
import uuid
from typing import Dict, List
import aiohttp
import requests
from app.services.flow import (
    _DP_API_URL,
    _DP_MODEL,
    logger,
    now_date_str,
    now_ts,
    filter_think_tags as filter_think_tags_msg,
)
from app.services.flow.namespace import FlowNamespace
from app.db.minio_client import MinioClient, get_manus_file_url


class ComponentBase:
    """所有组件的基类，提供通用方法"""
    function_call = "组件基类"
    component_name = "base"
    desc = "组件基类, 不可直接调用"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        self.namespace = namespace
        self.kwargs = kwargs
        self.output_files_urls = []  # 产生的文件: [{filename:xxx, url:xxx}]
        self.use_ts = 0  # 运行使用的时间
        self.prompt_used_tokens = 0  # prompt使用的token
        self.history_used_tokens = 0  # history使用的token
        self.answer_used_tokens = 0  # answer使用的token

    async def run(self, step: dict, step_result_list: list[dict]):
        # 在子类中实现此方法
        raise NotImplementedError

    def reset(self):
        # 当重新运行(run方法)时, 可以清空一些缓存数据。
        self.output_files_urls.clear()
        # 重置一下变量
        self.use_ts = 0
        self.prompt_used_tokens = 0
        self.history_used_tokens = 0
        self.answer_used_tokens = 0

    @staticmethod
    def filter_think_tags(response: str) -> str:
        """过滤掉LLM响应中的<think>标签"""
        # 使用非贪婪匹配模式，确保正确处理多个标签
        return filter_think_tags_msg(response)

    def save_input_and_output(self, input_msg: str, output_msg: str, start_ts: int, step: dict, retry_count: int = 0):
        """保存组件的输入输出信息"""
        self.use_ts = round((now_ts() - start_ts) / 1000, 2)
        payload = {
            "input": input_msg,
            "output": output_msg,
            "output_files": self.output_files_urls,
            "prompt_used_tokens": self.prompt_used_tokens,
            "history_used_tokens": self.history_used_tokens,
            "answer_used_tokens": self.answer_used_tokens,
            "total_used_tokens": self.prompt_used_tokens + self.history_used_tokens + self.answer_used_tokens,
            "use_ts": self.use_ts,
            "current_date": now_date_str(),
            "component_name": step["component_name"],
            "component_id": step["component_id"],
            "call_function": step["call_function"],
            "step": step["step"],
            "step_question": step["question"],
            "retry_count": retry_count,
        }
        self.namespace.add_input_and_output(payload)
        return {"event": "input_and_output", "payload": payload}

    async def call_llm(
        self,
        messages: List[Dict[str, str]],
        deepseek_api_url=_DP_API_URL,
        model=_DP_MODEL,
    ):
        """调用DeepSeek模型"""
        try:
            history_used_tokens = 0
            for message in messages:
                history_used_tokens += len(message["content"])

            payload = {
                "model": model,
                "messages": messages,
            }

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60 * 2)) as session:
                async with session.post(
                    deepseek_api_url,
                    headers={"Content-Type": "application/json"},
                    json=payload,
                ) as response:
                    response.raise_for_status()
                    data = await response.json()
                    result = data["choices"][0]["message"]["content"]
            # 更新使用的token数量
            self.prompt_used_tokens += 0
            self.history_used_tokens += history_used_tokens
            self.answer_used_tokens += len(result)

            return True, result
        except Exception as e:
            logger.error(f"调用DeepSeek模型失败: {e}")
            return False, ""

    @staticmethod
    def upload_file_to_storage(local_filepath: str, retry=3):
        """上传文件到minio"""
        filename = local_filepath.split("/")[-1]
        store_filepath = f"{uuid.uuid4().hex[:16]}/{filename}"
        while retry > 0:
            try:
                minio_client = MinioClient()
                base_url = get_manus_file_url()
                file_url = os.path.join(base_url, store_filepath)
                content = open(local_filepath, "rb").read()
                minio_client.put(minio_client.get_manus_files_bucket_name(), store_filepath, content)
                return True, {"filename": filename, "url": file_url}
            except Exception as e:
                logger.error(f"Failed to put minio file: {e}")
                time.sleep(0.2)
                retry -= 1

        return False, {}

    def get_last_context(self, step_result_list, max_length: int | None = None):
        if not step_result_list:
            return ""
        prev_step_question = step_result_list[-1]["question"] if step_result_list else ""
        prev_step_result = step_result_list[-1]["result"] if step_result_list else ""
        last_context = f"{prev_step_question} {prev_step_result}" if prev_step_question and prev_step_result else ""
        if max_length:
            last_context = last_context[:max_length]
        return last_context

    def get_last_chat_content(self, step_result_list, count=2):
        if not step_result_list:
            return []
        result = []
        for step_result in step_result_list[-count:]:
            result.append({"role": "user", "content": step_result["question"]})
            result.append({"role": "system", "content": step_result["result"]})
        return result


if __name__ == "__main__":
    _current_file = os.path.abspath(__file__)
    r1 = ComponentBase.upload_file_to_storage(_current_file)
    print(f"upload file to storage: {r1}")
