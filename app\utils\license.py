import traceback
from Crypto.Cipher import ChaCha20
import json
from app.db.redis_sessions import RedisSession
from app.config.config import settings
from datetime import datetime


class YuanFangLicense:
    @staticmethod
    def get_license():
        try:
            r = RedisSession()
            encrypt_txt = r.get("tmp").decode(
                "utf-8"
            )  # python 从 redis 里拿出来的都是字节
            encrypt_b = bytes.fromhex(encrypt_txt)
            key = settings.crypt.key.encode("utf-8")
            nonce = settings.crypt.nonce.encode("utf-8")
            cipher = ChaCha20.new(key=key, nonce=nonce)
            decrypt_txt = cipher.decrypt(encrypt_b).decode("utf-8")
            json_data = json.loads(decrypt_txt)
            data = json_data.pop("key")
            return json_data
        except Exception as e:
            return ""

    @staticmethod
    def check_license():
        try:
            license_data = YuanFangLicense.get_license()
            if license_data == "":
                return False, "授权数据为空"
            r = RedisSession()
            machine_id = r.get("machine").decode(
                "utf-8"
            )  # python 从 redis 里拿出来的都是字节
            if machine_id != license_data["machine_id"]:
                return False, "机器码不匹配"
            expire_time = datetime.strptime(
                license_data["expires"], "%Y-%m-%d %H:%M:%S"
            )
            current_time = datetime.now()
            if current_time > expire_time:
                return False, "授权已过期"
            return True, "ok"

        except Exception as e:
            traceback.print_exc()
            return False, f"异常错误{e}"

if __name__ == "__main__":
    print(YuanFangLicense.check_license())