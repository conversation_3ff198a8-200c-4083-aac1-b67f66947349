"""
关键代码修复建议
修复现有系统中的关键问题
"""

# 1. 修复 step_chain_runner.py 中的重试逻辑错误
def fix_retry_logic():
    """
    原代码问题：
    while retry_count >= max_retry_count:  # 错误！应该是 <
        retry_count += 1
    
    修复后的代码：
    """
    async def _process_task_fixed(self, max_retry_count=3):  # 增加默认重试次数
        meta = self.flow_namespace.get_meta()
        if meta is None:
            raise ValueError(f"无法获取任务元数据: {self.flow_namespace.id}")
        
        task_question = meta["question"]
        self.flow_namespace.reset()
        step_result_list = []
        component_objs = []
        step_chain = self.flow_namespace.get_step_chain()
        
        if step_chain is None:
            raise ValueError(f"无法获取任务步骤链: {self.flow_namespace.id}")
        
        for current_step in step_chain:
            component = self._get_component_by_step(current_step)
            current_step_result = ""
            
            # 执行步骤
            async for data in self._get_current_step_result(component, current_step, step_result_list):
                if isinstance(data, str):
                    current_step_result = data
                else:
                    yield data
            
            # 验证结果
            result_is_valid = await self._validate_step_result(current_step, current_step_result)
            
            if result_is_valid:
                yield self.flow_namespace.add_log(f"当前步骤: {current_step['step']}: 结果有效\n", current_step)
            else:
                # 修复后的重试逻辑
                retry_count = 0
                while retry_count < max_retry_count:  # 修复：改为 <
                    retry_count += 1
                    log_info = f"结果无效, 正在重新执行: step: {current_step['step']}: 重试次数: {retry_count}\n"
                    logger.info(log_info)
                    yield self.flow_namespace.add_log(log_info, current_step)
                    yield self._save_node_retry(component, current_step, retry_count=retry_count)
                    
                    # 智能问题优化
                    optimized_question = await self._optimize_question_intelligently(
                        current_step, current_step_result, step_result_list, retry_count
                    )
                    updated_step = {**current_step, "question": optimized_question}
                    component.reset()
                    current_step_result = ""
                    
                    # 重新执行
                    async for data in self._get_current_step_result(component, updated_step, step_result_list):
                        if isinstance(data, str):
                            current_step_result = data
                        else:
                            yield data
                    
                    result_is_valid = await self._validate_step_result(current_step, current_step_result)
                    if result_is_valid:
                        break
                    
                    # 指数退避延迟
                    await asyncio.sleep(2 ** retry_count)
                
                if not result_is_valid:
                    error_log = f"步骤 {current_step['step']} 达到最大重试次数({max_retry_count})，结果无效。"
                    logger.error(error_log)
                    yield self.flow_namespace.add_log(f"{error_log}\n", current_step)
                    
                    # 尝试降级策略
                    fallback_result = await self._try_fallback_strategy(current_step, step_result_list)
                    if fallback_result:
                        current_step_result = fallback_result
                        result_is_valid = True
                        yield self.flow_namespace.add_log("使用降级策略成功\n", current_step)
            
            step_result_list.append({
                "question": current_step["question"],
                "call_function": current_step["call_function"],
                "result": current_step_result,
                "is_valid": result_is_valid,
            })
        
        # 生成最终答案
        final_answer = await self._generate_final_answer(step_result_list, task_question)
        yield self.flow_namespace.add_log(f"任务完成，最终答案: {final_answer}\n")
        
        # 更新任务状态
        self.flow_namespace.update_meta("status", TaskStatus.FINISHED.value)
        return final_answer

# 2. 改进的问题优化策略
async def _optimize_question_intelligently(self, current_step, failed_result, step_result_list, retry_count):
    """智能优化问题"""
    original_question = current_step["question"]
    
    # 构建优化提示词
    context = self._build_optimization_context(step_result_list)
    failure_analysis = self._analyze_failure_reason(failed_result)
    
    optimization_strategies = [
        "简化问题表述",
        "增加具体细节",
        "改变问题角度",
        "分解为子问题"
    ]
    
    strategy = optimization_strategies[min(retry_count - 1, len(optimization_strategies) - 1)]
    
    prompt = f"""
    原始问题: {original_question}
    失败原因分析: {failure_analysis}
    上下文信息: {context}
    优化策略: {strategy}
    
    请根据失败原因和优化策略，重新表述问题，使其更容易得到有效结果。
    要求：
    1. 保持问题的核心意图不变
    2. 根据失败原因调整问题表述
    3. 考虑上下文信息
    4. 问题要具体明确
    """
    
    # 调用LLM优化问题
    optimized_question = await self._call_llm_for_optimization(prompt)
    return optimized_question or original_question

# 3. 改进的结果验证机制
async def _validate_step_result_enhanced(self, step, result):
    """增强的结果验证"""
    if not result or (isinstance(result, str) and len(result.strip()) < 10):
        return False
    
    # 基于步骤类型的验证
    call_function = step.get("call_function", "")
    
    if "search" in call_function.lower():
        return await self._validate_search_result(result, step)
    elif "analysis" in call_function.lower():
        return await self._validate_analysis_result(result, step)
    elif "generation" in call_function.lower():
        return await self._validate_generation_result(result, step)
    elif "code" in call_function.lower():
        return await self._validate_code_result(result, step)
    
    # 通用验证
    return await self._validate_generic_result(result, step)

async def _validate_search_result(self, result, step):
    """验证搜索结果"""
    if isinstance(result, list):
        return len(result) > 0 and any(
            isinstance(item, (str, dict)) and 
            (len(str(item)) > 20 if isinstance(item, str) else bool(item))
            for item in result
        )
    elif isinstance(result, str):
        # 检查是否包含有用信息
        useful_indicators = ["http", "www", "信息", "数据", "结果", "发现"]
        return any(indicator in result for indicator in useful_indicators)
    
    return False

async def _validate_analysis_result(self, result, step):
    """验证分析结果"""
    if isinstance(result, str):
        # 分析结果应该包含分析性词汇
        analysis_keywords = ["分析", "结论", "发现", "趋势", "因为", "所以", "表明", "显示", "认为", "建议"]
        keyword_count = sum(1 for keyword in analysis_keywords if keyword in result)
        return keyword_count >= 2 and len(result) > 50
    
    return isinstance(result, dict) and any(
        key in result for key in ["analysis", "conclusion", "insights", "summary"]
    )

# 4. 降级策略实现
async def _try_fallback_strategy(self, step, step_result_list):
    """尝试降级策略"""
    call_function = step.get("call_function", "")
    
    # 搜索类工具的降级策略
    if "search" in call_function.lower():
        return await self._fallback_search_strategy(step, step_result_list)
    
    # 分析类工具的降级策略
    elif "analysis" in call_function.lower():
        return await self._fallback_analysis_strategy(step, step_result_list)
    
    # 生成类工具的降级策略
    elif "generation" in call_function.lower():
        return await self._fallback_generation_strategy(step, step_result_list)
    
    return None

async def _fallback_search_strategy(self, step, step_result_list):
    """搜索工具降级策略"""
    # 1. 尝试使用知识库搜索替代网络搜索
    if "web" in step.get("call_function", "").lower():
        try:
            kb_result = await self._search_knowledge_base(step["question"])
            if kb_result:
                return f"基于知识库的搜索结果: {kb_result}"
        except Exception as e:
            logger.warning(f"知识库搜索降级失败: {e}")
    
    # 2. 基于上下文生成简化答案
    context = self._extract_relevant_context(step_result_list, step["question"])
    if context:
        return f"基于上下文的简化回答: {context}"
    
    # 3. 返回通用回答
    return f"抱歉，无法获取关于'{step['question']}'的具体信息，建议手动查询相关资源。"

async def _fallback_analysis_strategy(self, step, step_result_list):
    """分析工具降级策略"""
    # 基于已有结果进行简单分析
    available_data = [item["result"] for item in step_result_list if item.get("is_valid")]
    
    if available_data:
        combined_data = " ".join(str(data) for data in available_data)
        return f"基于已有信息的简单分析: {combined_data[:500]}..."
    
    return "由于数据不足，无法进行详细分析。"

# 5. 上下文管理优化
class ContextManager:
    """上下文管理器"""
    
    def __init__(self):
        self.context_store = {}
        self.context_weights = {}
    
    def add_context(self, key: str, value: Any, weight: float = 1.0, ttl: int = 3600):
        """添加上下文"""
        import time
        self.context_store[key] = {
            "value": value,
            "timestamp": time.time(),
            "ttl": ttl
        }
        self.context_weights[key] = weight
    
    def get_relevant_context(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """获取相关上下文"""
        import time
        current_time = time.time()
        
        # 清理过期上下文
        expired_keys = [
            key for key, data in self.context_store.items()
            if current_time - data["timestamp"] > data["ttl"]
        ]
        for key in expired_keys:
            del self.context_store[key]
            del self.context_weights[key]
        
        # 计算相关性分数
        relevance_scores = {}
        query_words = set(query.lower().split())
        
        for key, data in self.context_store.items():
            value_str = str(data["value"]).lower()
            value_words = set(value_str.split())
            
            # 简单的词汇重叠相关性
            overlap = len(query_words.intersection(value_words))
            total = len(query_words.union(value_words))
            
            if total > 0:
                relevance = overlap / total * self.context_weights.get(key, 1.0)
                relevance_scores[key] = relevance
        
        # 返回最相关的上下文
        sorted_keys = sorted(relevance_scores.keys(), key=lambda k: relevance_scores[k], reverse=True)
        return {
            key: self.context_store[key]["value"] 
            for key in sorted_keys[:top_k]
        }
    
    def update_context_weight(self, key: str, success: bool):
        """根据使用效果更新上下文权重"""
        if key in self.context_weights:
            if success:
                self.context_weights[key] = min(self.context_weights[key] * 1.1, 2.0)
            else:
                self.context_weights[key] = max(self.context_weights[key] * 0.9, 0.1)

# 使用示例
def integration_example():
    """集成示例"""
    # 在 StepChainRunner 中集成上下文管理器
    class EnhancedStepChainRunner:
        def __init__(self, **config):
            self.context_manager = ContextManager()
            # ... 其他初始化代码
        
        async def _get_current_step_result(self, component, step, step_result_list):
            # 获取相关上下文
            relevant_context = self.context_manager.get_relevant_context(step["question"])
            
            # 将上下文传递给组件
            if hasattr(component, 'set_context'):
                component.set_context(relevant_context)
            
            # 执行原有逻辑
            async for result in component.run(step, step_result_list):
                yield result
            
            # 更新上下文
            if hasattr(component, 'get_result'):
                result = component.get_result()
                self.context_manager.add_context(
                    f"step_{step['step']}_result",
                    result,
                    weight=1.0
                )

if __name__ == "__main__":
    print("关键修复建议已生成，请参考上述代码进行实施。")
