from sqlalchemy.orm import Session
from sqlalchemy import update
from app.models.switch_model_log import SwitchModelLog


class SwitchModelLogCurd(object):

    def __init__(self, db: Session):
        self.db = db

    def update_log(self, id: int, status: int, log_msg: str):
        stmt = (
            update(SwitchModelLog)
            .where(SwitchModelLog.id == id)
            .values({"status": status, "log": log_msg})
        )
        self.db.execute(stmt)
        self.db.commit()
