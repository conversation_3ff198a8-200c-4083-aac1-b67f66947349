
AVATARURLPATH = "/api/v1/llm/resource/avatar/"
PAPERURLPATH = "/api/v1/llm/resource/paper/"
REPORTURLPATH = "/api/v1/llm/resource/report/"

RESOURCE_TYPE = {
    'avatar': AVATARURLPATH,
    'paper': PAPERURLPATH,
    'report': REPORTURLPATH
}

RESOURCE_PREFIX = "/api/v1/llm/resource/"

def urlformat(id: int, type: str='avatar'):
    if type not in RESOURCE_TYPE:
        raise KeyError(f"没有该资源类型: {type}")
    return "%s%d" % (RESOURCE_TYPE[type], id)
