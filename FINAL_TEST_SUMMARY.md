# AI Agent 系统优化真实测试方案

## 🎯 测试目标确认

本测试方案专门针对你提出的两个核心要求：

1. **任务编排更加准确** - 通过真实测试验证任务分解和步骤编排的准确性
2. **工具调用更加准确** - 通过真实API调用验证组件选择和参数生成的准确性

## 📁 完整文件清单

### 核心测试文件
- **`real_system_test.py`** ⭐ - 主要测试框架，包含真实API调用测试
- **`run_real_tests.py`** ⭐ - 一键启动脚本，交互式测试界面
- **`REAL_TEST_README.md`** - 详细使用说明

### 优化组件文件（参考实现）
- `task_scheduler_optimization.py` - 智能任务调度器
- `context_aware_decomposition.py` - 上下文感知任务分解
- `intelligent_tool_calling.py` - 智能工具调用系统
- `critical_fixes.py` - 关键问题修复方案

## 🚀 快速开始测试

### 1. 环境检查
```bash
# 确保API服务器运行
curl http://localhost:8081/docs

# 确保模型服务器可访问
curl http://**************:8000/v1/models
```

### 2. 运行测试
```bash
# 一键启动测试
python run_real_tests.py
```

### 3. 选择测试模式
- **模式1 - 快速测试**: 3个基础测试用例，验证核心功能
- **模式2 - 标准测试**: 6个完整测试用例，全面功能验证
- **模式3 - 对比测试**: 4个专项测试，重点验证优化效果
- **模式4 - 全面测试**: 所有测试 + 综合分析报告

## 📊 真实测试用例设计

### 任务编排准确性测试

#### 测试用例1: 简单任务
```
问题: "TCT细胞药品有哪些？"
期望步骤: 1-2步
期望组件: ["知识库查询"]
测试重点: 单一工具调用的准确性
```

#### 测试用例2: 中等复杂任务
```
问题: "搜索最新的人工智能发展趋势，然后分析其对医疗行业的影响"
期望步骤: 2-4步
期望组件: ["搜索引擎查询", "数据分析"]
测试重点: 多步骤任务编排的逻辑性
```

#### 测试用例3: 复杂多步骤任务
```
问题: "查询TCR-T治疗相关信息，搜索最新研究进展，分析治疗效果数据，最后生成一份综合报告"
期望步骤: 3-6步
期望组件: ["知识库查询", "搜索引擎查询", "数据分析", "普通对话"]
测试重点: 复杂任务的分解和编排准确性
```

### 工具调用准确性测试

#### 测试用例4: 工具选择准确性
```
问题: "我需要查询TCT细胞药品的信息，然后搜索相关的最新研究进展"
期望组件: ["知识库查询", "搜索引擎查询"]
测试重点: 是否正确识别需要的工具类型
```

#### 测试用例5: 参数生成准确性
```
问题: "用Python计算斐波那契数列的前10项，并生成可视化图表"
期望组件: ["执行代码"]
测试重点: 代码生成和参数传递的准确性
```

#### 测试用例6: 错误处理能力
```
问题: "查询不存在的数据库表xyz123的信息"
期望组件: ["执行SQL查询"]
测试重点: 错误处理和重试机制的有效性
```

## 🔍 专项优化验证测试

### 重试逻辑修复验证
- **测试目标**: 验证重试逻辑BUG修复效果
- **测试方法**: 故意触发错误，观察重试行为
- **成功标准**: 不出现无限循环，正确处理错误

### 上下文感知验证
- **测试目标**: 验证上下文传递和利用能力
- **测试方法**: 多步骤任务，后续步骤依赖前面结果
- **成功标准**: 后续步骤能正确利用前面的输出

### 工具选择算法验证
- **测试目标**: 验证智能工具选择的准确性
- **测试方法**: 对比期望工具和实际选择的工具
- **成功标准**: 工具选择准确率 > 85%

## 📈 量化评估标准

### 任务编排准确性评分
```python
def calculate_orchestration_score(test_result, test_case):
    score = 0.0
    
    # 步骤数量合理性 (40%)
    actual_steps = len(test_result.step_chain)
    expected_min = test_case.success_criteria['min_steps']
    expected_max = test_case.success_criteria['max_steps']
    
    if expected_min <= actual_steps <= expected_max:
        score += 0.4
    
    # 步骤逻辑性 (30%)
    # 检查步骤间的依赖关系和顺序
    
    # 上下文利用 (30%)
    # 检查是否正确传递和利用上下文
    
    return score
```

### 工具调用准确性评分
```python
def calculate_tool_calling_score(test_result, test_case):
    score = 0.0
    
    # 组件选择准确性 (50%)
    must_use = test_case.success_criteria['must_use_components']
    used_components = test_result.components_used
    
    correct_selections = sum(1 for comp in must_use if comp in used_components)
    score += 0.5 * (correct_selections / len(must_use))
    
    # 参数生成正确性 (30%)
    # 基于执行成功率评估参数质量
    
    # 执行成功率 (20%)
    if test_result.success:
        score += 0.2
    
    return score
```

## 📊 预期测试结果

### 优化前基线指标（估计）
- 任务成功率: 75%
- 工具选择准确率: 60%
- 任务分解准确率: 55%
- 平均执行时间: 45秒
- 错误恢复率: 30%

### 优化后目标指标
- 任务成功率: 90%+ (提升15%)
- 工具选择准确率: 85%+ (提升25%)
- 任务分解准确率: 80%+ (提升25%)
- 平均执行时间: 30秒 (减少33%)
- 错误恢复率: 70%+ (提升40%)

## 🎯 关键优化验证点

### 1. 重试逻辑修复验证
```python
# 修复前的BUG代码
while retry_count >= max_retry_count:  # 永远不会执行
    retry_count += 1

# 修复后的正确代码  
while retry_count < max_retry_count:   # 正确执行
    retry_count += 1
    await asyncio.sleep(2 ** retry_count)  # 指数退避
```

### 2. 智能任务分解验证
- 自动识别任务复杂度
- 基于领域的分解策略
- 上下文权重计算
- 质量控制机制

### 3. 工具选择优化验证
- 语义相似度匹配
- 历史性能考虑
- 智能参数生成
- 错误恢复策略

## 📋 测试执行清单

### 测试前准备
- [ ] API服务器运行正常 (localhost:8081)
- [ ] 模型服务器可访问 (**************:8000)
- [ ] 数据库连接正常
- [ ] 测试环境清理

### 执行测试
- [ ] 运行快速测试验证基础功能
- [ ] 运行标准测试获得完整评估
- [ ] 运行对比测试验证优化效果
- [ ] 运行全面测试生成综合报告

### 结果分析
- [ ] 检查任务编排准确性指标
- [ ] 验证工具调用准确性指标
- [ ] 对比优化前后性能差异
- [ ] 识别进一步改进机会

## 🎉 测试成功标准

### 最低成功标准
- 所有测试用例执行完成
- 任务成功率 > 80%
- 工具选择准确率 > 75%
- 无系统崩溃或无限循环

### 优秀成功标准
- 任务成功率 > 90%
- 工具选择准确率 > 85%
- 任务分解准确率 > 80%
- 平均执行时间 < 35秒

### 卓越成功标准
- 任务成功率 > 95%
- 工具选择准确率 > 90%
- 任务分解准确率 > 85%
- 平均执行时间 < 30秒

## 🚀 开始测试

```bash
# 立即开始测试
python run_real_tests.py
```

选择测试模式，系统将自动：
1. 检查环境配置
2. 测试API连接
3. 执行真实测试用例
4. 生成详细分析报告
5. 提供优化建议

通过这套真实测试，你将获得准确的数据来验证AI Agent系统优化的实际效果！

---

**重要提醒**: 这是基于真实API调用的测试，不是模拟测试。测试结果将直接反映系统的实际性能和优化效果。
