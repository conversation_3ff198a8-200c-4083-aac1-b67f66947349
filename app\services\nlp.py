import re
import time
# pip install pyahocorasick -i https://pypi.tuna.tsinghua.edu.cn/simple/
import ahocorasick

pattern = re.compile(r'[^\w\u4e00-\u9fa5]')


def filter_chinese(raw_text):
    #pattern = re.compile(r'[^。！！、!"，；;,.\w\u4e00-\u9fa5]')
    return re.sub(pattern, '', raw_text)


def build_actree(wordlist):
    actree = ahocorasick.Automaton()
    for index, word in enumerate(wordlist):
        actree.add_word(word, (index, word))
    actree.make_automaton()
    return actree


# DFA算法
class DFAFilter(object):
    def __init__(self, wordlist = []):
        self.keyword_chains = {}  # 关键词链表
        self.delimit = '\x00'  # 限定
        for w in wordlist:
            self.add(w)

    def add(self, keyword):
        keyword = keyword.lower()  # 关键词英文变为小写
        chars = keyword.strip()  # 关键字去除首尾空格和换行
        if not chars:  # 如果关键词为空直接返回
            return
        level = self.keyword_chains
        # 遍历关键字的每个字
        for i in range(len(chars)):
            # 如果这个字已经存在字符链的key中就进入其子字典
            if chars[i] in level:
                level = level[chars[i]]
            else:
                if not isinstance(level, dict):
                    break
                for j in range(i, len(chars)):
                    level[chars[j]] = {}
                    last_level, last_char = level, chars[j]
                    level = level[chars[j]]
                last_level[last_char] = {self.delimit: 0}
                break
        if i == len(chars) - 1:
            level[self.delimit] = 0

    def filter(self, message, repl="*"):
        message = message.lower()
        ret = []
        start = 0
        while start < len(message):
            level = self.keyword_chains
            step_ins = 0
            for char in message[start:]:
                if char in level:
                    step_ins += 1
                    if self.delimit not in level[char]:
                        level = level[char]
                    else:
                        ret.append(repl * step_ins)
                        start += step_ins - 1
                        break
                else:
                    ret.append(message[start])
                    break
            else:
                ret.append(message[start])
            start += 1

        return ''.join(ret)


def dump_skipchars(raw_text):
    pure_text = re.sub(pattern, '', raw_text)

    skip_chars = {}
    for index, c in enumerate(raw_text):
        if c not in pure_text:
            skip_chars[index] = c

    return skip_chars, pure_text


def restore_skipchars(skip_chars, pure_text):
    for index in sorted(skip_chars.keys()):
        c = skip_chars[index]
        pure_text = pure_text[:index] + c + pure_text[index:]

    return pure_text


def ac_filter(raw_text, org_id: int=None, group_id: int=None):
    skip_chars, pure_text = dump_skipchars(raw_text)

    wordlist = SensitiveCurd.get_all_sensitives(org_id, group_id)
    actree = build_actree(wordlist)
    sensitive = []
    for i in actree.iter(pure_text):
        pure_text = pure_text.replace(i[1][1], "*" * len(i[1][1]))
        sensitive.append(i[1][1])

    text = restore_skipchars(skip_chars, pure_text)

    return dict(evil_flag=int(raw_text != text), text=text, sensitive=sensitive)


def content_ac_filter(raw_text, org_id: int=None, group_id: int=None):
    skip_chars, pure_text = dump_skipchars(raw_text)

    wordlist = ContentSecurityCurd.get_all_sensitives(org_id, group_id)
    actree = build_actree(wordlist)
    sensitive = []
    for i in actree.iter(pure_text):
        pure_text = pure_text.replace(i[1][1], "*" * len(i[1][1]))
        sensitive.append(i[1][1])

    text = restore_skipchars(skip_chars, pure_text)

    return dict(evil_flag=int(raw_text != text), text=text, sensitive=sensitive)


def dfa_filter(handler, raw_text):
    skip_chars, pure_text = dump_skipchars(raw_text)

    pure_text_after_filter = handler(pure_text)

    text = restore_skipchars(skip_chars, pure_text_after_filter)

    return dict(evilflag=int(raw_text != text), text = text)


if __name__ == "__main__":
    WORDS = []
    wordlist = [word.get("word") for word in WORDS]
    raw_text = "今天天气不错，发射巡.&航导弹，过,氧,丙酮你真是个大傻逼，大傻子，傻大个，大坏蛋，坏人。法轮功"
    print (raw_text)

    actree = build_actree(wordlist)
    start = time.time()
    print ('ac_filter', ac_filter(actree, raw_text), time.time() - start)

    handler = DFAFilter(wordlist = wordlist)
    start = time.time()
    print ('dfa_filter', dfa_filter(handler.filter, raw_text), time.time() - start)


