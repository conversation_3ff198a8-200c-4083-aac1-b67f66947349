from enum import Enum


class MessageCode(Enum):
    # 公共方法
    @property
    def status_code(self):
        return self.value[0]

    @property
    def msg(self):
        return self.value[1]

    # 公共错误码
    SUCCESS = (200, "success")
    SYSTEMERR = (201, "err")
    PARAMERR = (202, "参数错误")
    FILEFORMATERROR = (203, "文件格式不正确")
    UPLOADFAILED = (204, "文件上传失败")
    TOKENDENIED = (205, "该用户无此接口权限")
    TOKENINVALID = (206, "token无效")
    TOKENEXPIRED = (207, "token已过期")
    UNKNOWERROR = (208, "未知错误")
    DATABASEERR = (209, "数据库操作错误")
    USERPERMISSERR = (210, "用户没有此操作权限")
    REMOTESERVICEERR = (211, "远程服务错误")
    LICENSEDENIED = (212, "License 无效")

    # 以下错误码按模块区分,每个模块使用一个区段错误码
    # 同模块状态码右缩进对齐，模块之间用空行分隔开

    # ----敏感词模块 [1000 - 1199]------------
    SENSINOTEXIST = (1000, "敏感词不存在")
    SENSIOVERLENGTH = (1001, "敏感词过长")
    SENSINOTNONE = (1002, "敏感词不存在")

    # ----用户模块 [1200 - 1399]-------------
    PASSWORDERR = (1200, "用户不存在/密码错误")
    ROLENONE = (1201, "用户角色为空")
    USEREXIST = (1202, "用户登录名已存在")
    MODULENOTDIST = (1203, "该团队没有分配模型")
    USERNOTADMIN = (1204, "该用户不是管理员无法登陆管理系统")
    USERNOTADMINNOAUTH = (1205, "新增的用户不是管理员，无法分配权限")
    NOTEMAILFORMAT = (1206, "邮箱格式错误，请检查")
    NOTTELFORMAT = (1207, "手机格式错误，请检查")
    NOTDATEFORMAT = (1208, "日期格式错误，请检查")
    BIRTHDAYTOOLATER = (1209, "生日不能早于当前日期")

    # ----对话模块[1300-1399]-----------------
    SESSIONNOTFOUND = (1301, "没有找到该对话")
    SEESIONPERMERR = (1302, "用户没有该对话权限")
    SEESIEDITRMERR = (1302, "用户没有该对话信息修改权限")

    # ----模型调用模块[1400-1599] -----------
    AVATARNOTFOUND = (1400, "头像未找到")
    MODELTEAMADDEXIST = (1401, "团队已分配有模型")
