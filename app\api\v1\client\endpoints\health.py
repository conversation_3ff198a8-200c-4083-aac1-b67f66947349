import json

from fastapi import APIRouter, Depends
from app.api.response import APIResponse, response_formatter
from app.config.config import settings
import requests
from app.const.response import MessageCode
from datetime import datetime,timedelta
from prometheus_api_client import PrometheusConnect
from prometheus_api_client.utils import parse_datetime

router = APIRouter()

#访问 prometheus 辅助类
class PrometheusHelper():
    @staticmethod
    def getPrometheus()->PrometheusConnect:
        api = settings.prometheus.api
        prom = PrometheusConnect(url=api, headers=None, disable_ssl=True)
        is_ready = prom.check_prometheus_connection()
        if not is_ready:
            return None
        return prom
    @staticmethod
    def getValue(metric_data):
        if "value" in metric_data:
            return metric_data["value"][0],metric_data["value"][1]
        elif "values" in metric_data:
            return metric_data["values"][0][0],metric_data["values"][0][1]

    @staticmethod
    def getProperty(metric_data,property_name):
        return metric_data["metric"][property_name]
        

#访问Portainer的辅助类
class PortainerHelper():
    def __init__(self,api_base:str="",token:str=""):
        tmp_api = api_base
        if api_base[-1] == "/":
            tmp_api = api_base[:-1]
        #portainer把本机docker的endpoints ID 定为2，直接proxy到 docker api，这里访问本机直接写为2
        self.api_url = tmp_api + "/api/endpoints/2/docker/"
        self.token = token

    def list_contains(self):
        request_url = self.api_url + "containers/json"
        headers = {"X-API-KEY":self.token}
        response = requests.get(request_url,headers=headers)
        if response.status_code == 200:
            response_data = response.json()
            contains = response_data
            data = []
            for contain in contains:
                #print(contain)
                state = contain["State"]
                #print(contain["Names"])
                contain_name = contain["Names"][0][1:]  #名字去掉左边的 /
                data.append({"name":contain_name,"state":state})
            return 200,data
        else:
            return response.status_code,""
        
    def restart_contains(self,contain_name:str):
        pass
    def update_contains(self,contain_name:str):
        pass




class HealthApi(object):
    @staticmethod
    @router.get("/health")
    async def health():
        """
        服务状态列表

        输出字段:

            - service: 服务名称
            - status:  状态值，值为200表示正常

        """
        #连接prometheus获得服务状态
        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        metrics_data = prom.get_current_metric_value("probe_success")
        data = []
        for metric in metrics_data:
            service = PrometheusHelper.getProperty(metric,"service")
            timestamp,status = PrometheusHelper.getValue(metric)
            data.append({"service":service,"status":status})


        return APIResponse({"data":data})
    
    @staticmethod
    @router.get("/health/gpus")
    async def health_gpus():
        """
        gpu基本信息列表

        输出字段:

            - pcie_count: pcie插槽总数
            - gpu_count: gpu数量 
            - driver_ver: 显卡驱动版本
            - cuda_ver : cuda版本
            - gpus: gpu状态数组
            -       mem_used: 显存使用数
            -       mem_total: 显存总数
            -       temperature: 显卡温度

        """

        data = {"pcie_count":4,
                "gpu_count":2,
                "driver_ver":"550.120",
                "cuda_ver":"12.4",
                "gpus":[{
                    "mem_used":24655167488,
                    "mem_total":25757220864,
                    "temperature":57,
                },
                {
                    "mem_used":22768779264,
                    "mem_total":25757220864,
                    "temperature":69,

                }
                ]}
        
        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        metric_data = prom.get_current_metric_value("nvidia_smi_gpu_info")
        data["gpu_count"] = len(metric_data)
        data["gpus"] = []
        for metric in metric_data:
            gpu_uuid = metric["metric"]["uuid"]
            metric_temperature = prom.get_current_metric_value("nvidia_smi_temperature_gpu{uuid='%s'}"%gpu_uuid)
            timestamp,temperature = PrometheusHelper.getValue(metric_temperature[0])
            metric_mem_total = prom.get_current_metric_value("nvidia_smi_memory_total_bytes{uuid='%s'}"%gpu_uuid)
            timestamp,mem_total = PrometheusHelper.getValue(metric_mem_total[0])
            metric_mem_used = prom.get_current_metric_value("nvidia_smi_memory_used_bytes{uuid='%s'}"%gpu_uuid)
            timestamp,mem_used = PrometheusHelper.getValue(metric_mem_used[0])
            data["gpus"].append({
                "mem_used":mem_used,
                "mem_total":mem_total,
                "temperature":temperature
            })

        return APIResponse({"data":data})
    
    
    @staticmethod
    @router.get("/health/host")
    async def health_host():
        """
        主机基本信息列表

        输出字段:

            - host_name: 主机名
            - running_time: 运行时间 
            - cpu_count: cpu核数
            - total_mem: 总内存
            - m5_load: 5m负载
            - cpu_ratio: cpu使用率
            - disk_read: 磁盘读取
            - disk_write: 磁盘写入
            - down_band: 下载带宽
            - up_band: 上传带宽

        """

        data = {
            "host_name": "CortexMesh",
            "running_time": "1 week" ,
            "cpu_count": "48",
            "m5_load": "2.50",
            "cpu_ratio": "78",
            "disk_read": "24.48",
            "disk_write": "197.7",
            "down_band": "512.1",
            "up_band": "433.12"
        }

        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        metric_data = prom.get_current_metric_value("node_time_seconds - node_boot_time_seconds")
        timestamp,running_time = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("count(node_cpu_seconds_total{mode='idle'})")
        timestamp,cpu_count = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("node_load5")
        timestamp,m5_load = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("1 - avg(irate(node_cpu_seconds_total{mode='idle'}[5m]))")
        timestamp,cpu_ratio = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("sum(irate(node_disk_read_bytes_total[5m]))")
        timestamp,disk_read = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("sum(irate(node_disk_written_bytes_total[5m]))")
        timestamp,disk_write = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("sum(irate(node_network_receive_bytes_total[5m]))*8")
        timestamp,down_band = PrometheusHelper.getValue(metric_data[0])
        metric_data = prom.get_current_metric_value("sum(irate(node_network_transmit_bytes_total[5m]))*8")
        timestamp,up_band = PrometheusHelper.getValue(metric_data[0])

        data = {
            "host_name": "CortexMesh",
            "running_time": running_time ,
            "cpu_count": cpu_count,
            "m5_load": m5_load,
            "cpu_ratio": cpu_ratio,
            "disk_read": disk_read,
            "disk_write": disk_write,
            "down_band": down_band,
            "up_band": up_band
        }


        return APIResponse({"data":data})
        
    
    @staticmethod
    @router.get("/health/mem")
    async def health_mem(
        start_time: int = -1,
        end_time: int = -1,
        chunk_size: int = 15
        ):
        """
        内存使用率时序数据

        输入字段：

            - start_time: 开始时间timestamp秒，缺省30分钟前
            - end_time: 结束时间timestamp秒，缺省当前时间
            - chunk_size: 间隔单位 秒

        输出字段:

            - total_mem: 内存总数
            - used_mem: 总使用数 
            - used_ratio: 总平均使用率
            - used_timeline: 总使用数时序数组
            -   timestamp: 时间戳
            -   value: 使用量
            - used_ratio_timeline: 平均使用率时序数组
            -   timestamp: 时间戳
            -   value: 平均使用率

        """

        data = {
            "total_mem": "128GB",
            "used_mem": "60.2GB" ,
            "used_ratio": "48",
            "used_timeline": [
                {"timestamp":12312312, "value":"21.1GB"},
                {"timestamp":12312315, "value":"21.1GB"}
            ],
            "used_ratio_timeline": [
                {"timestamp":12312312, "value":90},
                {"timestamp":12312315, "value":89}
            ]
        }
        if start_time <=0:
            start_time = int(parse_datetime("30m").timestamp())
        if end_time <=0:
            end_time = int(parse_datetime("now").timestamp())

        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        metric_data = prom.get_current_metric_value("node_memory_MemTotal_bytes")
        timestamp,total_mem = PrometheusHelper.getValue(metric_data[0])  
        metric_data = prom.get_current_metric_value("node_memory_MemTotal_bytes - node_memory_MemFree_bytes")
        timestamp,used_mem = PrometheusHelper.getValue(metric_data[0])
        data["total_mem"] = total_mem
        data["used_mem"] = used_mem
        data["used_timeline"] = []
        metric_data = prom.custom_query_range(
            "node_memory_MemTotal_bytes - node_memory_MemFree_bytes",
            start_time=datetime.fromtimestamp(start_time),
            end_time=datetime.fromtimestamp(end_time),
            step="%ds"%chunk_size
            )
        for metric in metric_data[0]["values"]:
            data["used_timeline"].append({"timestamp":metric[0],"value":metric[1]})
        data["used_ratio_timeline"] = []
        metric_data = prom.custom_query_range(
            "(node_memory_MemTotal_bytes - node_memory_MemFree_bytes)/node_memory_MemTotal_bytes",
            start_time=datetime.fromtimestamp(start_time),
            end_time=datetime.fromtimestamp(end_time),
            step="%ds"%chunk_size
            )
        for metric in metric_data[0]["values"]:
            data["used_ratio_timeline"].append({"timestamp":metric[0],"value":metric[1]})
        


        return APIResponse({"data":data})
    
       
    
    @staticmethod
    @router.get("/health/disk")
    async def health_disk(
        start_time: int = -1,
        end_time: int = -1,
        chunk_size: int = 15
        ):
        """
        硬盘读写时序数据

        输入字段：

            - start_time: 开始时间timestamp秒，缺省30分钟前
            - end_time: 结束时间timestamp秒，缺省当前时间
            - chunk_size: 间隔单位 秒

        输出字段:

            - read_timeline: 硬盘读取时序数组
            -   timestamp: 时间戳
            -   value: 读取速度
            - write_timeline: 硬盘写入时序数组
            -   timestamp: 时间戳
            -   value: 写入速度

        """

        data = {
            "read_timeline": [
                {"timestamp":12312312, "value":100},
                {"timestamp":12312315, "value":200}
            ],
            "write_timeline": [
                {"timestamp":12312312, "value":90},
                {"timestamp":12312315, "value":89}
            ]
        }

        if start_time <=0:
            start_time = int(parse_datetime("30m").timestamp())
        if end_time <=0:
            end_time = int(parse_datetime("now").timestamp())


        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        usage_data = prom.custom_query_range(
                "sum(irate(node_disk_read_bytes_total[5m]))",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["read_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["read_timeline"].append({"timestamp":usage[0],"value":usage[1]})
        usage_data = prom.custom_query_range(
                "sum(irate(node_disk_written_bytes_total[5m]))",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["write_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["write_timeline"].append({"timestamp":usage[0],"value":usage[1]})


        return APIResponse({"data":data})
    
 
    
    @staticmethod
    @router.get("/health/cpu")
    async def health_cpu(
        start_time: int = -1,
        end_time: int = -1,
        chunk_size: int = 15
        ):
        """
        CPU 使用率时序数据

        输入字段：

            - start_time: 开始时间timestamp秒，缺省30分钟前
            - end_time: 结束时间timestamp秒，缺省当前时间
            - chunk_size: 间隔单位 秒

        输出字段:

            - user_timeline: 用户使用率时序数组
            -   timestamp: 时间戳
            -   value: 使用率
            - sys_timeline: 系统使用率时序数组
            -   timestamp: 时间戳
            -   value: 使用率
            - total_timeline:  总使用率时序数组
            -   timestamp: 时间戳
            -   value: 使用率

        """
        #print(start_time,",",end_time)

        #irate(node_cpu_seconds_total{mode=user}[5m]
        data = {
            "user_timeline": [
                {"timestamp":12312312, "value":30},
                {"timestamp":12312315, "value":40}
            ],
            "sys_timeline": [
                {"timestamp":12312312, "value":10},
                {"timestamp":12312315, "value":29}
            ],
            "total_timeline": [
                {"timestamp":12312312, "value":40},
                {"timestamp":12312315, "value":69}
            ]
        }

        if start_time <=0:
            start_time = int(parse_datetime("30m").timestamp())
        if end_time <=0:
            end_time = int(parse_datetime("now").timestamp())

        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        usage_data = prom.custom_query_range(
                "1- (sum(irate(node_cpu_seconds_total{mode='idle'}[5m])) / count(node_cpu_seconds_total{mode='idle'}))",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        #print(usage_data)
        data["total_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["total_timeline"].append({"timestamp":usage[0],"value":usage[1]})

        usage_data = prom.custom_query_range(
                "sum(irate(node_cpu_seconds_total{mode='system'}[5m])) / count(node_cpu_seconds_total{mode='system'})",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["sys_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["sys_timeline"].append({"timestamp":usage[0],"value":usage[1]})

        usage_data = prom.custom_query_range(
                "sum(irate(node_cpu_seconds_total{mode='user'}[5m])) / count(node_cpu_seconds_total{mode='user'})",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["user_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["user_timeline"].append({"timestamp":usage[0],"value":usage[1]})
    

        return APIResponse({"data":data})
    

    
    @staticmethod
    @router.get("/health/sysload")
    async def health_cpu(
        start_time: int = -1,
        end_time: int = -1,
        chunk_size: int = 15
        ):
        """
        系统负载时序数据

        输入字段：

            - start_time: 开始时间timestamp秒，缺省30分钟前
            - end_time: 结束时间timestamp秒，缺省当前时间
            - chunk_size: 间隔单位 秒

        输出字段:

            - m15_timeline: 15分钟负载时序数组
            -   timestamp: 时间戳
            -   value: 负载
            - m5_timeline: 5分钟负载时序数组
            -   timestamp: 时间戳
            -   value: 负载
            - m1_timeline:  1分钟负载时序数组
            -   timestamp: 时间戳
            -   value: 负载

        """
        

        data = {
            "m15_timeline": [
                {"timestamp":12312312, "value":2.7},
                {"timestamp":12312315, "value":3.5}
            ],
            "m5_timeline": [
                {"timestamp":12312312, "value":1.5},
                {"timestamp":12312315, "value":3.6}
            ],
            "m1_timeline": [
                {"timestamp":12312312, "value":3.5},
                {"timestamp":12312315, "value":2.4}
            ]
        }

        if start_time <=0:
            start_time = int(parse_datetime("30m").timestamp())
        if end_time <=0:
            end_time = int(parse_datetime("now").timestamp())
        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        usage_data = prom.custom_query_range(
                "node_load1",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["m1_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["m1_timeline"].append({"timestamp":usage[0],"value":usage[1]})
        
        usage_data = prom.custom_query_range(
                "node_load5",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["m5_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["m5_timeline"].append({"timestamp":usage[0],"value":usage[1]})
        
        usage_data = prom.custom_query_range(
                "node_load15",
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                step="%ds"%chunk_size
                )
        data["m15_timeline"]=[]
        for usage in usage_data[0]["values"]:
            data["m15_timeline"].append({"timestamp":usage[0],"value":usage[1]})


        return APIResponse({"data":data})
    


    @staticmethod
    @router.get("/health/gpu_usage")
    async def health_gpu_usage(
        start_time: int = -1,
        end_time: int = -1,
        chunk_size: int = 15
    ):
        """
        gpu使用率时序

        输入字段：

            - start_time: 开始时间timestamp秒，缺省30分钟前
            - end_time: 结束时间timestamp秒，缺省当前时间
            - chunk_size: 间隔单位 秒

        输出字段:

            - gpu_uuid: gpu编号
            - usages: gpu使用率时序数组
            -       timestamp: 时间
            -       value: 使用率百分比
 

        """

        if start_time <=0:
            start_time = int(parse_datetime("30m").timestamp())
        if end_time <=0:
            end_time = int(parse_datetime("now").timestamp())
        prom = PrometheusHelper.getPrometheus()
        if prom is None:
            return APIResponse(content={"msg":"无法连接prometheus"},status=MessageCode.UNKNOWERROR)
        
        metric_data = prom.get_current_metric_value("nvidia_smi_gpu_info")
        data = []
        for metric in metric_data:
            gpu_uuid = PrometheusHelper.getProperty(metric,"uuid")
            usage_data = prom.get_metric_range_data(
                "nvidia_smi_utilization_gpu_ratio{uuid='%s'}"%gpu_uuid,
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                chunk_size=timedelta(seconds=chunk_size)
                )
            #print(usage_data)
            gpu_usage = {"gpu_uuid":gpu_uuid}
            usages = []
            for usage in usage_data:
                #print(usage)
                timestamp,usage_value = PrometheusHelper.getValue(usage)
                usages.append({"timestamp":timestamp,"value":usage_value})
            gpu_usage["usages"] = usages
            data.append(gpu_usage)
                

        return APIResponse({"data":data})
    
        
    @staticmethod
    @router.get("/list_contains")
    async def list_contains():
        api_base = settings.portainer.api_base
        token = settings.portainer.token
        portainer = PortainerHelper(api_base,token)
        ret_code,data = portainer.list_contains()
        if ret_code == 200:
            return APIResponse({"data":data})
        else:
            return APIResponse(content={},status=MessageCode.UNKNOWERROR)


