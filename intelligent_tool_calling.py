"""
智能工具调用系统优化方案
提供更准确的工具选择、参数生成和执行机制
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class ToolType(Enum):
    """工具类型"""
    SEARCH = "search"           # 搜索类工具
    ANALYSIS = "analysis"       # 分析类工具
    GENERATION = "generation"   # 生成类工具
    COMPUTATION = "computation" # 计算类工具
    COMMUNICATION = "communication" # 通信类工具
    STORAGE = "storage"         # 存储类工具

class ParameterType(Enum):
    """参数类型"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    FILE = "file"

@dataclass
class ParameterSchema:
    """参数模式定义"""
    name: str
    type: ParameterType
    required: bool = True
    description: str = ""
    default: Any = None
    constraints: Dict[str, Any] = field(default_factory=dict)  # 约束条件
    examples: List[Any] = field(default_factory=list)  # 示例值

@dataclass
class ToolDefinition:
    """工具定义"""
    name: str
    type: ToolType
    description: str
    parameters: List[ParameterSchema] = field(default_factory=list)
    
    # 工具能力描述
    input_types: List[str] = field(default_factory=list)
    output_types: List[str] = field(default_factory=list)
    capabilities: List[str] = field(default_factory=list)
    
    # 性能指标
    avg_execution_time: float = 0.0
    success_rate: float = 1.0
    cost_score: float = 1.0  # 成本评分，越低越好
    
    # 依赖和兼容性
    dependencies: List[str] = field(default_factory=list)
    compatible_tools: List[str] = field(default_factory=list)

class ToolSelector:
    """智能工具选择器"""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.tool_embeddings: Dict[str, List[float]] = {}  # 工具语义向量
        self.usage_history: Dict[str, List[Dict]] = {}  # 使用历史
    
    def register_tool(self, tool: ToolDefinition) -> None:
        """注册工具"""
        self.tools[tool.name] = tool
        # 生成工具的语义向量（这里简化处理）
        self.tool_embeddings[tool.name] = self._generate_tool_embedding(tool)
    
    def _generate_tool_embedding(self, tool: ToolDefinition) -> List[float]:
        """生成工具的语义向量（简化版本）"""
        # 实际实现中应该使用预训练的embedding模型
        text = f"{tool.name} {tool.description} {' '.join(tool.capabilities)}"
        # 这里返回模拟的向量
        return [hash(text) % 100 / 100.0 for _ in range(128)]
    
    def select_tools(self, query: str, context: Dict[str, Any] = None, top_k: int = 3) -> List[Tuple[ToolDefinition, float]]:
        """选择最适合的工具"""
        candidates = []
        
        for tool_name, tool in self.tools.items():
            score = self._calculate_tool_score(query, tool, context)
            candidates.append((tool, score))
        
        # 按分数排序
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:top_k]
    
    def _calculate_tool_score(self, query: str, tool: ToolDefinition, context: Dict[str, Any] = None) -> float:
        """计算工具匹配分数"""
        score = 0.0
        
        # 1. 语义相似度（简化版本）
        semantic_score = self._calculate_semantic_similarity(query, tool)
        score += semantic_score * 0.4
        
        # 2. 关键词匹配
        keyword_score = self._calculate_keyword_match(query, tool)
        score += keyword_score * 0.3
        
        # 3. 历史成功率
        history_score = self._get_historical_performance(tool.name, query)
        score += history_score * 0.2
        
        # 4. 上下文适配度
        if context:
            context_score = self._calculate_context_fit(tool, context)
            score += context_score * 0.1
        
        return score
    
    def _calculate_semantic_similarity(self, query: str, tool: ToolDefinition) -> float:
        """计算语义相似度（简化版本）"""
        # 实际实现中应该使用向量相似度计算
        query_words = set(query.lower().split())
        tool_words = set((tool.name + " " + tool.description).lower().split())
        
        intersection = query_words.intersection(tool_words)
        union = query_words.union(tool_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_keyword_match(self, query: str, tool: ToolDefinition) -> float:
        """计算关键词匹配度"""
        query_lower = query.lower()
        matches = 0
        
        # 检查工具名称
        if tool.name.lower() in query_lower:
            matches += 2
        
        # 检查能力描述
        for capability in tool.capabilities:
            if capability.lower() in query_lower:
                matches += 1
        
        # 检查工具类型
        if tool.type.value in query_lower:
            matches += 1
        
        return min(matches / 5.0, 1.0)  # 归一化到0-1
    
    def _get_historical_performance(self, tool_name: str, query: str) -> float:
        """获取历史性能分数"""
        if tool_name not in self.usage_history:
            return 0.5  # 默认分数
        
        history = self.usage_history[tool_name]
        if not history:
            return 0.5
        
        # 计算成功率
        successful = sum(1 for record in history if record.get('success', False))
        return successful / len(history)
    
    def _calculate_context_fit(self, tool: ToolDefinition, context: Dict[str, Any]) -> float:
        """计算上下文适配度"""
        score = 0.0
        
        # 检查输入类型匹配
        available_types = context.get('available_data_types', [])
        for input_type in tool.input_types:
            if input_type in available_types:
                score += 0.5
        
        # 检查依赖满足
        available_tools = context.get('available_tools', [])
        dependencies_met = all(dep in available_tools for dep in tool.dependencies)
        if dependencies_met:
            score += 0.5
        
        return min(score, 1.0)

class ParameterGenerator:
    """智能参数生成器"""
    
    def __init__(self):
        self.parameter_patterns = self._init_parameter_patterns()
        self.validation_rules = self._init_validation_rules()
    
    def _init_parameter_patterns(self) -> Dict[str, List[str]]:
        """初始化参数模式"""
        return {
            'query': [r'搜索|查找|寻找', r'关于.*的', r'.*信息'],
            'location': [r'在.*地方', r'.*市|.*省|.*国', r'地址.*'],
            'time': [r'\d{4}年', r'\d+月', r'今天|明天|昨天', r'最近.*'],
            'count': [r'\d+个', r'前\d+', r'最多\d+'],
            'format': [r'格式.*', r'.*文件', r'输出.*']
        }
    
    def _init_validation_rules(self) -> Dict[ParameterType, callable]:
        """初始化验证规则"""
        return {
            ParameterType.STRING: lambda x: isinstance(x, str),
            ParameterType.INTEGER: lambda x: isinstance(x, int),
            ParameterType.FLOAT: lambda x: isinstance(x, (int, float)),
            ParameterType.BOOLEAN: lambda x: isinstance(x, bool),
            ParameterType.ARRAY: lambda x: isinstance(x, list),
            ParameterType.OBJECT: lambda x: isinstance(x, dict)
        }
    
    def generate_parameters(self, query: str, tool: ToolDefinition, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成工具调用参数"""
        parameters = {}
        
        for param_schema in tool.parameters:
            value = self._extract_parameter_value(query, param_schema, context)
            
            if value is not None:
                # 验证参数
                if self._validate_parameter(value, param_schema):
                    parameters[param_schema.name] = value
                else:
                    logger.warning(f"参数 {param_schema.name} 验证失败: {value}")
                    if param_schema.required:
                        # 尝试使用默认值或示例值
                        fallback_value = self._get_fallback_value(param_schema)
                        if fallback_value is not None:
                            parameters[param_schema.name] = fallback_value
            elif param_schema.required:
                # 必需参数缺失，尝试生成
                generated_value = self._generate_missing_parameter(query, param_schema, context)
                if generated_value is not None:
                    parameters[param_schema.name] = generated_value
                else:
                    logger.error(f"无法生成必需参数: {param_schema.name}")
        
        return parameters
    
    def _extract_parameter_value(self, query: str, param_schema: ParameterSchema, context: Dict[str, Any] = None) -> Any:
        """从查询中提取参数值"""
        param_name = param_schema.name.lower()
        
        # 1. 直接模式匹配
        if param_name in self.parameter_patterns:
            for pattern in self.parameter_patterns[param_name]:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    return self._convert_match_to_type(match.group(), param_schema.type)
        
        # 2. 基于参数描述的智能提取
        value = self._smart_extract_by_description(query, param_schema)
        if value is not None:
            return value
        
        # 3. 从上下文中获取
        if context:
            context_value = self._extract_from_context(param_schema, context)
            if context_value is not None:
                return context_value
        
        return None
    
    def _convert_match_to_type(self, match_text: str, param_type: ParameterType) -> Any:
        """将匹配文本转换为指定类型"""
        try:
            if param_type == ParameterType.STRING:
                return match_text.strip()
            elif param_type == ParameterType.INTEGER:
                numbers = re.findall(r'\d+', match_text)
                return int(numbers[0]) if numbers else None
            elif param_type == ParameterType.FLOAT:
                numbers = re.findall(r'\d+\.?\d*', match_text)
                return float(numbers[0]) if numbers else None
            elif param_type == ParameterType.BOOLEAN:
                return match_text.lower() in ['是', '对', 'true', '1', '开启']
        except (ValueError, IndexError):
            return None
        
        return match_text
    
    def _smart_extract_by_description(self, query: str, param_schema: ParameterSchema) -> Any:
        """基于参数描述智能提取"""
        description = param_schema.description.lower()
        query_lower = query.lower()
        
        # 简单的关键词匹配策略
        if '查询' in description or 'query' in description:
            # 提取查询相关的内容
            return query.strip()
        elif '数量' in description or 'count' in description:
            # 提取数字
            numbers = re.findall(r'\d+', query)
            return int(numbers[0]) if numbers else None
        elif '格式' in description or 'format' in description:
            # 提取格式信息
            formats = ['json', 'xml', 'csv', 'txt', 'pdf']
            for fmt in formats:
                if fmt in query_lower:
                    return fmt
        
        return None
    
    def _extract_from_context(self, param_schema: ParameterSchema, context: Dict[str, Any]) -> Any:
        """从上下文中提取参数值"""
        # 检查上下文中是否有同名的键
        if param_schema.name in context:
            return context[param_schema.name]
        
        # 检查相关的上下文键
        related_keys = [
            f"previous_{param_schema.name}",
            f"default_{param_schema.name}",
            param_schema.name.replace('_', '')
        ]
        
        for key in related_keys:
            if key in context:
                return context[key]
        
        return None
    
    def _validate_parameter(self, value: Any, param_schema: ParameterSchema) -> bool:
        """验证参数值"""
        # 类型验证
        type_validator = self.validation_rules.get(param_schema.type)
        if type_validator and not type_validator(value):
            return False
        
        # 约束验证
        constraints = param_schema.constraints
        
        if param_schema.type == ParameterType.STRING:
            if 'min_length' in constraints and len(value) < constraints['min_length']:
                return False
            if 'max_length' in constraints and len(value) > constraints['max_length']:
                return False
            if 'pattern' in constraints and not re.match(constraints['pattern'], value):
                return False
        
        elif param_schema.type in [ParameterType.INTEGER, ParameterType.FLOAT]:
            if 'min_value' in constraints and value < constraints['min_value']:
                return False
            if 'max_value' in constraints and value > constraints['max_value']:
                return False
        
        elif param_schema.type == ParameterType.ARRAY:
            if 'min_items' in constraints and len(value) < constraints['min_items']:
                return False
            if 'max_items' in constraints and len(value) > constraints['max_items']:
                return False
        
        return True
    
    def _get_fallback_value(self, param_schema: ParameterSchema) -> Any:
        """获取备用值"""
        if param_schema.default is not None:
            return param_schema.default
        
        if param_schema.examples:
            return param_schema.examples[0]
        
        # 类型默认值
        type_defaults = {
            ParameterType.STRING: "",
            ParameterType.INTEGER: 0,
            ParameterType.FLOAT: 0.0,
            ParameterType.BOOLEAN: False,
            ParameterType.ARRAY: [],
            ParameterType.OBJECT: {}
        }
        
        return type_defaults.get(param_schema.type)
    
    def _generate_missing_parameter(self, query: str, param_schema: ParameterSchema, context: Dict[str, Any] = None) -> Any:
        """生成缺失的参数"""
        # 这里可以集成更复杂的参数生成逻辑
        # 比如调用LLM来生成参数
        logger.info(f"尝试生成缺失参数: {param_schema.name}")
        
        # 简单的生成策略
        if param_schema.type == ParameterType.STRING and 'query' in param_schema.name.lower():
            return query
        
        return self._get_fallback_value(param_schema)

# 使用示例
def create_tool_calling_example():
    """创建工具调用示例"""
    # 创建工具选择器
    selector = ToolSelector()
    
    # 注册示例工具
    search_tool = ToolDefinition(
        name="web_search",
        type=ToolType.SEARCH,
        description="在互联网上搜索信息",
        parameters=[
            ParameterSchema(
                name="query",
                type=ParameterType.STRING,
                required=True,
                description="搜索查询词",
                constraints={"min_length": 1, "max_length": 200}
            ),
            ParameterSchema(
                name="max_results",
                type=ParameterType.INTEGER,
                required=False,
                description="最大结果数量",
                default=10,
                constraints={"min_value": 1, "max_value": 100}
            )
        ],
        capabilities=["网页搜索", "信息检索", "实时数据"],
        avg_execution_time=3.0,
        success_rate=0.95
    )
    
    selector.register_tool(search_tool)
    
    # 创建参数生成器
    param_generator = ParameterGenerator()
    
    return selector, param_generator

class ResultValidator:
    """结果验证器"""

    def __init__(self):
        self.validation_strategies = {
            ToolType.SEARCH: self._validate_search_result,
            ToolType.ANALYSIS: self._validate_analysis_result,
            ToolType.GENERATION: self._validate_generation_result,
            ToolType.COMPUTATION: self._validate_computation_result
        }

    def validate_result(self, result: Any, tool: ToolDefinition, context: Dict[str, Any] = None) -> Tuple[bool, str, float]:
        """验证工具执行结果

        Returns:
            Tuple[bool, str, float]: (是否有效, 错误信息, 质量分数)
        """
        if result is None:
            return False, "结果为空", 0.0

        # 基础验证
        basic_valid, basic_msg, basic_score = self._basic_validation(result)
        if not basic_valid:
            return False, basic_msg, basic_score

        # 工具特定验证
        validator = self.validation_strategies.get(tool.type)
        if validator:
            specific_valid, specific_msg, specific_score = validator(result, tool, context)
            if not specific_valid:
                return False, specific_msg, specific_score

            # 综合分数
            final_score = (basic_score + specific_score) / 2
            return True, "验证通过", final_score

        return True, "基础验证通过", basic_score

    def _basic_validation(self, result: Any) -> Tuple[bool, str, float]:
        """基础验证"""
        if result is None:
            return False, "结果为空", 0.0

        if isinstance(result, str) and len(result.strip()) == 0:
            return False, "结果为空字符串", 0.0

        if isinstance(result, (list, dict)) and len(result) == 0:
            return False, "结果为空集合", 0.0

        # 检查是否包含错误信息
        if isinstance(result, dict) and result.get('error'):
            return False, f"结果包含错误: {result['error']}", 0.0

        return True, "基础验证通过", 0.7

    def _validate_search_result(self, result: Any, tool: ToolDefinition, context: Dict[str, Any] = None) -> Tuple[bool, str, float]:
        """验证搜索结果"""
        if not isinstance(result, (list, dict)):
            return False, "搜索结果格式错误", 0.0

        if isinstance(result, list):
            if len(result) == 0:
                return False, "搜索无结果", 0.0

            # 检查结果质量
            quality_score = min(len(result) / 10.0, 1.0)  # 结果数量质量

            # 检查结果内容
            for item in result[:3]:  # 检查前3个结果
                if isinstance(item, dict):
                    if not item.get('title') and not item.get('content'):
                        quality_score *= 0.8
                elif isinstance(item, str) and len(item.strip()) < 10:
                    quality_score *= 0.9

            return True, "搜索结果验证通过", quality_score

        return True, "搜索结果验证通过", 0.8

    def _validate_analysis_result(self, result: Any, tool: ToolDefinition, context: Dict[str, Any] = None) -> Tuple[bool, str, float]:
        """验证分析结果"""
        # 分析结果应该包含洞察或结论
        if isinstance(result, dict):
            required_keys = ['insights', 'conclusions', 'summary', 'analysis']
            has_key = any(key in result for key in required_keys)
            if not has_key:
                return False, "分析结果缺少关键信息", 0.3

            return True, "分析结果验证通过", 0.9

        if isinstance(result, str):
            if len(result) < 50:
                return False, "分析结果过于简短", 0.4

            # 检查是否包含分析性词汇
            analysis_keywords = ['分析', '结论', '发现', '趋势', '因为', '所以', '表明', '显示']
            keyword_count = sum(1 for keyword in analysis_keywords if keyword in result)
            quality_score = min(keyword_count / 3.0, 1.0)

            return True, "分析结果验证通过", quality_score

        return True, "分析结果验证通过", 0.7

    def _validate_generation_result(self, result: Any, tool: ToolDefinition, context: Dict[str, Any] = None) -> Tuple[bool, str, float]:
        """验证生成结果"""
        if isinstance(result, str):
            if len(result) < 20:
                return False, "生成内容过短", 0.3

            # 检查内容质量
            quality_score = min(len(result) / 500.0, 1.0)  # 基于长度的质量评估

            # 检查是否有重复内容
            sentences = result.split('。')
            unique_sentences = set(sentences)
            if len(unique_sentences) < len(sentences) * 0.8:
                quality_score *= 0.7  # 重复内容较多

            return True, "生成结果验证通过", quality_score

        return True, "生成结果验证通过", 0.8

    def _validate_computation_result(self, result: Any, tool: ToolDefinition, context: Dict[str, Any] = None) -> Tuple[bool, str, float]:
        """验证计算结果"""
        if isinstance(result, (int, float)):
            # 检查数值合理性
            if abs(result) > 1e10:
                return False, "计算结果数值过大", 0.2

            return True, "计算结果验证通过", 0.95

        if isinstance(result, dict) and 'result' in result:
            return self._validate_computation_result(result['result'], tool, context)

        return True, "计算结果验证通过", 0.8

class ErrorRecoveryManager:
    """错误恢复管理器"""

    def __init__(self):
        self.recovery_strategies = {
            'parameter_error': self._recover_parameter_error,
            'execution_error': self._recover_execution_error,
            'validation_error': self._recover_validation_error,
            'timeout_error': self._recover_timeout_error
        }

    def recover_from_error(self, error_type: str, error_info: Dict[str, Any], tool: ToolDefinition,
                          original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从错误中恢复

        Returns:
            Optional[Dict[str, Any]]: 恢复策略，包含新的参数或替代工具
        """
        recovery_func = self.recovery_strategies.get(error_type)
        if recovery_func:
            return recovery_func(error_info, tool, original_params)

        return None

    def _recover_parameter_error(self, error_info: Dict[str, Any], tool: ToolDefinition,
                               original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """参数错误恢复"""
        missing_param = error_info.get('missing_parameter')
        invalid_param = error_info.get('invalid_parameter')

        recovery_params = original_params.copy()

        if missing_param:
            # 尝试添加默认值
            for param_schema in tool.parameters:
                if param_schema.name == missing_param and param_schema.default is not None:
                    recovery_params[missing_param] = param_schema.default
                    return {'action': 'retry', 'parameters': recovery_params}

        if invalid_param:
            # 尝试修正无效参数
            param_name = invalid_param.get('name')
            param_value = invalid_param.get('value')

            for param_schema in tool.parameters:
                if param_schema.name == param_name:
                    corrected_value = self._correct_parameter_value(param_value, param_schema)
                    if corrected_value is not None:
                        recovery_params[param_name] = corrected_value
                        return {'action': 'retry', 'parameters': recovery_params}

        return None

    def _recover_execution_error(self, error_info: Dict[str, Any], tool: ToolDefinition,
                               original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行错误恢复"""
        error_message = error_info.get('message', '')

        # 网络错误 - 建议重试
        if 'network' in error_message.lower() or 'timeout' in error_message.lower():
            return {'action': 'retry', 'delay': 5, 'max_retries': 2}

        # 权限错误 - 建议使用替代工具
        if 'permission' in error_message.lower() or 'unauthorized' in error_message.lower():
            return {'action': 'use_alternative_tool'}

        # 资源不足 - 建议简化参数
        if 'resource' in error_message.lower() or 'limit' in error_message.lower():
            simplified_params = self._simplify_parameters(original_params)
            return {'action': 'retry', 'parameters': simplified_params}

        return None

    def _recover_validation_error(self, error_info: Dict[str, Any], tool: ToolDefinition,
                                original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """验证错误恢复"""
        quality_score = error_info.get('quality_score', 0.0)

        if quality_score < 0.3:
            # 质量太低，建议使用替代工具
            return {'action': 'use_alternative_tool'}
        elif quality_score < 0.6:
            # 质量一般，建议调整参数重试
            adjusted_params = self._adjust_parameters_for_quality(original_params, tool)
            return {'action': 'retry', 'parameters': adjusted_params}

        return None

    def _recover_timeout_error(self, error_info: Dict[str, Any], tool: ToolDefinition,
                             original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """超时错误恢复"""
        # 简化参数以减少执行时间
        simplified_params = self._simplify_parameters(original_params)
        return {'action': 'retry', 'parameters': simplified_params, 'timeout': error_info.get('timeout', 30) * 1.5}

    def _correct_parameter_value(self, value: Any, param_schema: ParameterSchema) -> Any:
        """修正参数值"""
        if param_schema.type == ParameterType.STRING:
            return str(value) if value is not None else ""
        elif param_schema.type == ParameterType.INTEGER:
            try:
                return int(float(str(value)))
            except (ValueError, TypeError):
                return param_schema.default or 0
        elif param_schema.type == ParameterType.FLOAT:
            try:
                return float(str(value))
            except (ValueError, TypeError):
                return param_schema.default or 0.0
        elif param_schema.type == ParameterType.BOOLEAN:
            if isinstance(value, str):
                return value.lower() in ['true', '1', 'yes', '是']
            return bool(value)

        return value

    def _simplify_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """简化参数"""
        simplified = params.copy()

        # 减少数量相关的参数
        for key, value in simplified.items():
            if 'count' in key.lower() or 'limit' in key.lower() or 'max' in key.lower():
                if isinstance(value, int) and value > 5:
                    simplified[key] = min(value // 2, 5)

        return simplified

    def _adjust_parameters_for_quality(self, params: Dict[str, Any], tool: ToolDefinition) -> Dict[str, Any]:
        """为提高质量调整参数"""
        adjusted = params.copy()

        # 增加结果数量以提高质量
        for key, value in adjusted.items():
            if 'count' in key.lower() or 'limit' in key.lower():
                if isinstance(value, int):
                    adjusted[key] = min(value * 2, 20)

        return adjusted

if __name__ == "__main__":
    selector, param_gen = create_tool_calling_example()
    validator = ResultValidator()
    recovery_manager = ErrorRecoveryManager()

    query = "搜索最新的AI发展趋势，需要10个结果"
    selected_tools = selector.select_tools(query, top_k=1)

    if selected_tools:
        tool, score = selected_tools[0]
        print(f"选择的工具: {tool.name} (分数: {score:.2f})")

        parameters = param_gen.generate_parameters(query, tool)
        print(f"生成的参数: {parameters}")

        # 模拟结果验证
        mock_result = ["AI发展趋势1", "AI发展趋势2", "AI发展趋势3"]
        is_valid, msg, quality = validator.validate_result(mock_result, tool)
        print(f"结果验证: {is_valid}, 消息: {msg}, 质量: {quality:.2f}")
