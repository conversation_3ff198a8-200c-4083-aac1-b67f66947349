import os
import json
from urllib.parse import quote
import traceback
from fastapi import APIRouter, Depends, File, UploadFile, Form
from fastapi.responses import StreamingResponse
from app.log import logger
from app.db import get_mcp_db
from sqlalchemy.orm import Session
from app.services.mcp.mcp import McpService
from app.api.response import APIResponse
from app.api.v1.client.schemas.ai_mcp import (
    AddMcp,
    DeleteMcp,
    GetMcpList,
    GetMcpListByConfig,
    GetMcpTools,
    SetMcpEnabled,
    TestMcpTool,
    TestMcpToolByConfig,
)
from app.const.response import MessageCode

router = APIRouter()

# 1. mcp服务列表
# 2. 工具列表
# 3. 开启关闭mcp
# 4. 调试运行工具


@staticmethod
@router.post("/mcp_server/add")
def add_mcp_server(req: AddMcp, db: Session = Depends(get_mcp_db)):
    """添加MCP服务"""
    try:
        mcp_service = McpService()
        config = json.loads(req.config)
        mcp_service.add_mcp_server(db=db, config=config, name=req.name, desc=req.desc)
        return APIResponse("添加MCP服务成功")
    except Exception as e:
        logger.error(f"Failed to add MCP server: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/mcp_server/delete")
def delete_mcp_server(req: DeleteMcp, db: Session = Depends(get_mcp_db)):
    """删除MCP服务"""
    try:
        mcp_service = McpService()
        mcp_service.delete_mcp_server(db=db, mcp_id=req.id)
        return APIResponse("删除服务成功")
    except Exception as e:
        logger.error(f"Failed to add MCP server: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/mcp_server/enabled")
def set_mcp_server_enabled(req: SetMcpEnabled, db: Session = Depends(get_mcp_db)):
    """设置MCP服务关闭或开启"""
    try:
        McpService().set_mcp_server_enabled(db, req.id, req.enabled)
        return APIResponse(f"MCP服务已设置为: {req.enabled}")
    except Exception as e:
        logger.error(f"Failed to add MCP server: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.get("/mcp_server/list")
def get_mcp_server_list(req: GetMcpList = Depends(), db: Session = Depends(get_mcp_db)):
    """获取MCP服务列表"""
    try:
        result = McpService().get_mcp_server_list(db=db, page=req.page, page_size=req.page_size)
        return APIResponse(result, MessageCode.SUCCESS)
    except Exception as e:
        logger.error(f"Failed to add MCP server: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.get("/mcp_server/tools")
def get_mcp_server_tools(req: GetMcpTools = Depends(), db: Session = Depends(get_mcp_db)):
    """通过ID 获取某个MCP服务的工具集"""
    try:
        result = McpService().get_mcp_server_tools(db=db, mcp_id=req.mcp_id)
        return APIResponse(result)
    except Exception as e:
        logger.error(f"Failed to fetch MCP server list: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/mcp_server/tools_summary")
def get_mcp_server_tools_summary(req: GetMcpListByConfig, db: Session = Depends(get_mcp_db)):
    """
    通过config 获取MCP服务列表
    param
        - config: dict = Field(description="mcp服务地址")
        config示例
         {
            "mcpServers": {
                "mcp-e2bdev": {
                    "url": "https://mcp.higress.ai/mcp-e2bdev/cmb5yrsw100d08r01xajzzom6/sse"
                }
            }
        }
    :return:
    """
    try:
        mcp_service = McpService()
        config = json.loads(req.config)
        if not config:
            # 如果没有提供服务器配置，则使用默认配置
            raise ValueError("No MCP server configuration provided")
        url, error = mcp_service.get_dynamic_url(config)
        if not url:
            return APIResponse(error, MessageCode.SYSTEMERR)
        data = mcp_service.fetch_tools_via_rpc(url)
        return APIResponse(data)
    except Exception as e:
        logger.error(f"Failed to fetch MCP server list: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/mcp_server/tool/test")
def test_mcp_server_tool(req: TestMcpTool, db: Session = Depends(get_mcp_db)):
    """通过ID 测试某个MCP服务的工具"""
    try:
        result = McpService().test_mcp_server_tool(db, req.id, req.params)
        return APIResponse(result)
    except Exception as e:
        logger.error(f"Failed to fetch MCP server list: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)


@staticmethod
@router.post("/mcp_server/tool/test_by_config")
def test_mcp_server_tool_by_config(req: TestMcpToolByConfig, db: Session = Depends(get_mcp_db)):
    """
    通过config 和 工具名称及参数 测试工具的可用性
    :return:
    """
    try:
        mcp_service = McpService()
        config = json.loads(req.config)
        if not config:
            # 如果没有提供服务器配置，则使用默认配置
            raise ValueError("No MCP server configuration provided")
        url, error = mcp_service.get_dynamic_url(config)
        if not url:
            return APIResponse(error, MessageCode.SYSTEMERR)
        data = mcp_service.fetch_tools_via_rpc(url)
        for row in data:
            if row["name"] == req.tool_name:
                result = McpService().test_mcp_server_tool_by_name(db, url, req.tool_name, req.params)
                return APIResponse(result)
        return APIResponse(f"工具不存在: {req.tool_name}", MessageCode.SYSTEMERR)
    except Exception as e:
        logger.error(f"Failed to fetch MCP server list: {e}")
        return APIResponse(str(e), MessageCode.SYSTEMERR)
