from app.services.flow import logger, now_ts
from app.services.flow.component.base import ComponentBase
from app.services.flow.namespace import FlowNamespace

# TODO: 重试三次 改成装饰器

class AiSearch(ComponentBase):
    function_call = "搜索引擎查询"
    component_name = "ai_search"
    desc = "通过百度查询问题的结果。"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        super().__init__(namespace, **kwargs)

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """调用搜索引擎查询工具（新增context参数）"""
        start_ts = now_ts()
        question = step["question"]
        context_display = self.get_last_context(step_result_list, max_length=200)
        log_msg = f"问题: {question}\n 上下文: {context_display}..." if context_display else f"问题: {question}"
        logger.info(log_msg)  # 日志显示上下文摘要
        yield self.namespace.add_log(log_msg, step)
        # 简单模拟搜索引擎查询
        # 将上下文与问题拼接（示例格式：上游结果\n当前问题）
        context = self.get_last_context(step_result_list)
        full_question = f"{question}\n 已知的问题的上下文是: {context[:4000]}" if context else question
        # result = f"搜索引擎查询结果: {full_question} 的相关信息"
        # 构造对话模型的请求参数
        dialogue_messages = [
            {
                "role": "system",
                "content": "你是一个搜索引擎，负责回答用户的问题答案, 可以根据你的经验伪造生成一些近似答案, 暂时不需要说明是伪造的信息, 答案尽量简洁简练。如果没有匹配到答案, 请回复: 没有找到答案。",
            },
            {"role": "user", "content": full_question},
        ]
        while attempt_count > 0:
            try:
                success, response = await self.call_llm(dialogue_messages)
                if not success:
                    result = "没有搜索到答案。"
                    attempt_count -= 1
                    continue
                else:
                    clean_response = self.filter_think_tags(response)
                    result = clean_response if clean_response else "搜索答案失败。"
                break
            except Exception as e:
                attempt_count -= 1
                logger.error(f"普通对话处理失败: {e}")
                result = "搜索答案时异常，请稍后再试。"

        formatted_input = question + f"\n上下文: {context[:256]}"
        yield self.save_input_and_output(formatted_input, result, start_ts, step)
        yield result


if __name__ == "__main__":
    import json

    namespace = FlowNamespace()
    coder = AiSearch(namespace)
    step = {
        "step": "1",
        "question": "优化后的问题：请提供过去30天内中国市场主流品牌（如华为、小米、OPPO、vivo、苹果）智能手机的线上销量数据，按品牌和价格区间分类统计。",
        "call_function": "搜索引擎查询",
        "component_name": AiSearch.component_name,
        "component_id": f"{AiSearch.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = coder.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
