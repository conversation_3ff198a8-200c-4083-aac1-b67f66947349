from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from app.log import logger


class ExceptionHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            error_message = str(e)
            error_response = {
                "msg": error_message,
                "code": 101,
                "content": ""
            }
            logger.error(f"请求地址:{request.url},错误信息:{error_message}")
            return JSONResponse(content=error_response, status_code=500)


