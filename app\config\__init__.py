from pathlib import Path
from app.config.config import settings


ICON_PATH = Path(settings.path.icons)
FILE_PATH = Path(settings.path.file_path)
ICON_PATH.mkdir(exist_ok=True)

# db config
DB_PATH = 'mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8mb4'.format(
    settings.mysql.user, settings.mysql.passwd, settings.mysql.host,
    settings.mysql.port, settings.mysql.dbname
)

ASYNC_DB_PATH = 'mysql+asyncmy://{}:{}@{}:{}/{}?charset=utf8mb4'.format(
    settings.mysql.user, settings.mysql.passwd, settings.mysql.host,
    settings.mysql.port, settings.mysql.dbname
)

# 先临时设置一个ip  等完事在改成配置文件
DB_PATH_MCP = 'mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8mb4'.format(
    "root","root123456", "**************",
    3306, "ai_manus"
)


# 用于签发和验证 JWT token 的密钥
SECRET_KEY = settings.auth.seckey
ALGORITHM = settings.auth.algorithm

# 头像URL路径
AVATAR_PATH = "/avatar"

