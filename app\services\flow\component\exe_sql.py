from app.services.flow import _DP_API_KEY, _DP_API_URL, _DP_MODEL, logger, now_ts
from app.services.flow.namespace import FlowNamespace
from app.services.flow.component.base import ComponentBase
from app.services.exesql import ExeSQL as ExeSQLTool, ExeSQLParam as ExeSQLToolParam, LLMService


class ExeSql(ComponentBase):
    function_call = "数据库查询"
    component_name = "exe_sql"
    desc = "通过查询本地SQL数据库查询结果, 本地数据库中有 ragflow 项目的数据, ragflow是一个大模型工作流平台, 如果非常确定数据源才可以走数据库查询。"

    def __init__(self, namespace: FlowNamespace, **kwargs):
        super().__init__(namespace, **kwargs)

    async def run(self, step: dict, step_result_list: list[dict] | None = None, attempt_count=3):
        """调用数据库查询工具（新增context参数）"""
        start_ts = now_ts()
        question = step["question"]

        context_display = self.get_last_context(step_result_list, max_length=200)
        log_msg = f"问题: {question}\n上下文: {context_display}..."
        logger.info(log_msg)  # 日志显示上下文摘要
        yield self.namespace.add_log(log_msg, step)
        context = self.get_last_context(step_result_list)
        full_question = f"{question}\n 已知问题的上下文是: {context}" if context else question
        while attempt_count >= 0:
            try:
                # 需要安装 pip3 install  tabulate==0.9.0 psycopg2-binary==2.9.9 tabulate==0.9.0
                my_param = ExeSQLToolParam(
                    db_type="mysql",
                    database="agent_test",
                    username="root",
                    host="***************",
                    port=3306,
                    password="infini_rag_flow",
                    loop=1,
                    top_n=30,
                )
                # TODO: LLM地址
                my_llm = LLMService(model=_DP_MODEL, api_key=_DP_API_KEY, base_url=_DP_API_URL)
                exe_sql = ExeSQLTool(my_param, my_llm)
                # 将上下文与问题拼接（示例格式：上游结果\n当前问题）
                resp = await exe_sql.run(question=full_question)  # TODO:  异步处理
                yield self.namespace.add_log(f"数据库查询结果: {resp}", step)
                result = str(resp)
                break
            except Exception as e:
                attempt_count -= 1
                error_msg = f"数据库查询失败: {e}"
                logger.error(error_msg)
                yield self.namespace.add_log(error_msg, step)
                result = error_msg

        formatted_input = f"{full_question[:512]}..." if len(full_question) > 512 else full_question
        yield self.save_input_and_output(formatted_input, result, start_ts, step)
        yield result


if __name__ == "__main__":
    import json

    namespace = FlowNamespace()
    coder = ExeSql(namespace)
    step = {
        "step": "1",
        "question": "生成一个ppt, 介绍最近一周的销售手机的业绩",
        "call_function": "普通对话",
        "component_name": ExeSql.component_name,
        "component_id": f"{ExeSql.component_name}:ae6907",
    }

    def run_step(step: dict):
        ans = coder.run(step)
        for row in ans:
            print("输出信息: \n", json.dumps(row, ensure_ascii=False, indent=2))

    run_step(step)
