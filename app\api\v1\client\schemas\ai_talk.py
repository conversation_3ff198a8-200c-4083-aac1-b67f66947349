from pydantic import BaseModel, Field, field_validator


class Params(BaseModel):
    temperature: float = Field(default=0.8, description='模型温度')
    top_p: float = Field(default=0.9, description='模型的top_p')


class CreateSessionParam(BaseModel):
    name: str | None = Field(default=None, description="对话名称,可为空,默认返回会话ID")
    prompt: str | None = Field(default="你是一个有用的助手", description='给模型的系统提示语')
    params: Params | dict = Field(default={}, description="对模型提问的参数")

    @field_validator('params')
    def check_create_session_params(cls, params: Params | dict):
        if isinstance(params, dict):
            return params
        return params.model_dump()


class PostSingleTestParam(BaseModel):
    session_id: str | None = Field(default='', description='会话ID')
    parent_question_id: int = Field(default=0, description="上次回答的问题id, 默认为0")
    targets: list[int] = Field(default=[], description='要测试的模型id列表')
    question: str = Field(default='', description='要问模型的问题')
    # 兼容旧版
    prompt: str | None = Field(default="你是一个有用的助手", description='给模型的系统提示语')
    params: Params | dict = Field(default={}, description="对模型提问的参数")

    @field_validator('params')
    def check_post_single_test_params(cls, params: Params | dict):
        if isinstance(params, dict):
            return params
        return params.model_dump()


class PostStopConversation(BaseModel):
    question_id: int = Field(description="要停止回答的问题id")
    targets: list[int] | None = Field(default=None,
                                      description="要停止回答的模型ID列表,当不传递此参数时,默认全部模型都停止")


class SetConversationQuestionJudgeParam(BaseModel):
    question: int = Field(description="单次对话的问题ID")
    aimodel: int = Field(description="模型ID")
    judge: dict = Field(description="答案风险判断结果")
    exa_selector: int = Field(description="事例选择器（0不选1选）")
