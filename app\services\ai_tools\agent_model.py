import json

from app.utils.http import AsyncHTTPRequest
from app.const.agent_tools_const import AgentToolsConstants
import asyncio


class AgentModel:
    def __init__(self):
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.url = AgentToolsConstants.CHAT_CHAT_URL

    # 获取模型列表
    async def getl_model_list(self):
        request = AsyncHTTPRequest('GET', self.url + "/v1/models", headers=self.headers)
        async for data in request():
            return data['data']

    # 获取模型列表
    async def list_knowledge_bases(self):
        request = AsyncHTTPRequest('GET', self.url + "/knowledge_base/list_knowledge_bases", headers=self.headers)
        async for data in request():
            return data['data']


if __name__ == '__main__':
    agent_model = AgentModel()

    print(asyncio.run(agent_model.getl_model_list()))
