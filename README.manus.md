# 元方项目 - 聊天对话时的事件推送(三种类型:log, node_msg, node_msg_stat ):

# 类型一: log 运行日志
```json
{
    "event": "log",
    "payload": {
        "msg": "具体的log信息",
        "level": "log级别",
        "created_at": "创建时间",
        "call_function": "工具名称（可能为null）",
        "component_name": "节点名称（可能为null）",
        "component_id": "节点ID（可能为null）",
        "step": "步骤（可能为null）",
    },
}
```

# 类型二: node_msg(逻辑推理-节点的输入输出信息)
```json
{
    "event": "input_and_output",
    "payload": {
        "input": "节点输入信息",
        "output": "节点输出信息",
        "output_files": "节点生成的文件，格式如下 : [{filename: 文件名, url: url地址}]",
        "prompt_used_tokens": "提示词消耗的token数量",
        "history_used_tokens": "对话历史消耗的token数量",
        "answer_used_tokens": "答案消耗的token数量",
        "total_used_tokens": "当前节点消耗的总的token数量"，
        "use_ts": "节点运行耗时(秒级)",
        "current_date": "当前时间",
        "call_function": "工具名称",
        "component_name": "节点名称",
        "component_id": "节点ID",
        "step": "当前步骤",
        "step_question": "当前步骤的问题",
        "retry_count": "重试次数",
    },
}
```

# 类型三: node_msg_stat(逻辑推理-节点的汇总信息)
```json
node_stat_msg = {
    "event": "事件类型:node_msg_stat(节点的总统计信息)",
    "payload": {
        "use_ts": "节点运行总耗时",
        "prompt_used_tokens": "提示词使用的token数量",
        "history_used_tokens": "历史会话使用的token数量",
        "answer_used_tokens": "回答使用的token数量",
        "total_used_tokens": "总使用的token数量",
    },
}
```
