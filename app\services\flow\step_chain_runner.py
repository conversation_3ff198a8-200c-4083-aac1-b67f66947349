import re
from typing import Dict, Any
from app.db.mysql import get_mcp_db
from app.models.ai_mcp import AiMcp
from app.services.flow import call_chat_mdl, filter_think_tags, logger, TaskStatus, now_date_str
from app.services.flow.component.base import ComponentBase
from app.services.flow.component.mcp_tool_wrapper import McpToolWrapper
from app.services.flow.namespace import FlowNamespace
from app.services.flow.component.ai_search import AiSearch
from app.services.flow.component.common_chat import CommonChat
from app.services.flow.component.data_analyze import DataAnalyze
from app.services.flow.component.exe_code import ExeCode
from app.services.flow.component.exe_sql import ExeSql
from app.services.flow.component.knowledge_base import KnowledgeBase
from app.services.mcp.mcp import McpService
from app.services.flow.step_chain_builder import get_ai_mcp_mapping, get_step_chain_prompt_by_deep_chat

from app.services.flow import build_openai_response


def get_mcp_server_url_from_db(mcp_id):
    db = next(get_mcp_db())
    existing_mcp = db.query(AiMcp).filter_by(id=mcp_id).first()
    return existing_mcp.url if existing_mcp else None


class StepChainRunner:
    def __init__(
        self,
        deepseek_api_url: str,
        knowledge_base_url: str,
        knowledge_base_token: str,
        db_config: Dict[str, str],
        flow_namespace: FlowNamespace,
    ):
        """初始化工具执行器"""
        self.deepseek_api_url = deepseek_api_url
        self.knowledge_base_url = knowledge_base_url
        self.knowledge_base_token = knowledge_base_token
        self.db_config = db_config
        self.flow_namespace = flow_namespace
        self.all_context = ""
        self.mcp_service = McpService()

    async def process_task(self):
        meta = self.flow_namespace.get_meta()
        if meta is None:
            raise ValueError(f"无法获取任务元数据: {self.flow_namespace.id}")
        fns_id = self.flow_namespace.id
        if meta["status"] == TaskStatus.RUNNING.value:
            raise ValueError("任务正在运行中，请等待完成后再进行操作。")
        # 状态置为运行中
        self.flow_namespace.update_meta("status", TaskStatus.RUNNING.value)
        try:
            async for msg in self._process_task():
                if isinstance(msg, dict):
                    yield build_openai_response(fns_id, node_event=msg)
                elif isinstance(msg, str):
                    yield build_openai_response(fns_id, content=msg)
                else:
                    logger.warning(f"Unknown message type: {msg}")
                    continue
        except Exception as e:
            logger.warning(f"Failed to process task: {e}")
            # 状态置为失败
            self.flow_namespace.update_meta("status", TaskStatus.FAILED.value)
            raise e

    async def _process_task(self, max_retry_count=1):
        meta = self.flow_namespace.get_meta()
        if meta is None:
            raise ValueError(f"无法获取任务元数据: {self.flow_namespace.id}")
        task_question = meta["question"]
        # 删除旧的日志和输入输出
        self.flow_namespace.reset()
        # 存放每个步骤的问题，结果，结果是否有效。[{question:xxx, result:xxx, is_valid:xxx}]
        step_result_list = []
        # 存放节点对象
        component_objs = []
        step_chain = self.flow_namespace.get_step_chain()
        if step_chain is None:
            raise ValueError(f"无法获取任务步骤链: {self.flow_namespace.id}")
        for current_step in step_chain:
            component = self._get_component_by_step(current_step)
            current_step_result = ""
            async for data in self._get_current_step_result(component, current_step, step_result_list):
                if isinstance(data, str):
                    current_step_result = data
                else:
                    yield data
            result_is_valid = await self._validate_step_result(current_step, current_step_result)
            if result_is_valid:
                yield self.flow_namespace.add_log(f"当前步骤: {current_step['step']}: 结果有效\n", current_step)
            else:
                retry_count = 0  # 根据设置的最大重试次数进行重试
                while retry_count >= max_retry_count:
                    retry_count += 1
                    log_info = f"结果无效, 正在重新执行: step: {current_step['step']}: 重试次数: {retry_count}\n"
                    logger.info(log_info)
                    yield self.flow_namespace.add_log(log_info, current_step)
                    yield self._save_node_retry(component, current_step, retry_count=retry_count)
                    # 优化问题: 优化问题时也需传入上下文。TODO: 把判定失败的原因也传入优化问题
                    optimized_question = await self._optimize_question(
                        current_step, current_step_result, step_result_list
                    )
                    updated_step = {**current_step, "question": optimized_question}
                    component.reset()  # 清理变量值
                    current_step_result = ""
                    async for data in self._get_current_step_result(component, updated_step, step_result_list):
                        if isinstance(data, str):
                            current_step_result = data
                        else:
                            yield data
                    result_is_valid = await self._validate_step_result(current_step, current_step_result)
                    if result_is_valid:  # 结果无效进行再次重试
                        break
                    else:
                        continue
                else:
                    error_log = f"步骤 {current_step['step']} 达到最大重试次数({max_retry_count})，结果无效。"
                    logger.error(error_log)
                    yield self.flow_namespace.add_log(f"{error_log}\n", current_step)
            step_result_list.append(
                {
                    "question": current_step["question"],
                    "call_function": current_step["call_function"],
                    "result": current_step_result,
                    "is_valid": result_is_valid,
                }
            )
            component_objs.append(component)
        # 整理最终答案
        step_chain_answer = meta.get("step_chain_answer", "")
        final_result = await self._format_result_final(task_question, step_chain_answer, step_result_list)
        # 更新node节点的统计结果
        yield self._save_node_msg_stat(component_objs)
        # 更新结果记录
        self.flow_namespace.add_chat_history({"role": "assistant", "content": final_result})
        self.flow_namespace.update_meta("status", TaskStatus.FINISHED.value)
        yield final_result

    async def _get_current_step_result(self, component: ComponentBase, step, step_result_list):
        step_num = step["step"]
        function_name = step["call_function"]
        current_question = step["question"]
        running_msg = f"执行步骤 {step_num}: {function_name} - {current_question}"
        yield self.flow_namespace.add_log(running_msg, step)
        logger.info(f"调用工具: {function_name}, 问题: {current_question}")
        async for res in component.run(step, step_result_list):
            yield res

    async def _format_result_final(self, question, step_chain_answer, step_result_list: list) -> str:
        # 整理最终答案
        result_str = ""
        for step_result in step_result_list:
            step_result_result = step_result["result"]
            step_result_call_function = step_result["call_function"]
            step_result_msg = (
                f"{{'role': 'tool', 'content': '{step_result_result}', 'call_function': '{step_result_call_function}'}}"
            )
            result_str += f"\n以下是刚刚执行的工具调用响应，请根据响应内容更新你的回答：\n {step_result_msg}\n"
        logger.info(f"拼接后的最终答案: {result_str}")
        max_token = 30000  # 8192
        if len(result_str) >= max_token:
            logger.warning(f"答案过长, 截取前{max_token}个字符: org length: {len(result_str)}")
            result_str = result_str[-max_token:]
        messages = [
            {
                "role": "user",
                "content": question,
            },
            {
                "role": "system",
                "content": step_chain_answer,
            },
            {
                "role": "system",
                "content": "如果问答需要包含生成的文件, 那么请返回文件名，而不是文件路径。并且不要提到该条提示词。",
            },
            {
                "role": "user",
                "content": get_step_chain_prompt_by_deep_chat(result_str),
            },
        ]

        response = await call_chat_mdl(messages, self.deepseek_api_url)
        return filter_think_tags(response)

    async def _validate_step_result(self, step: Dict[str, Any], result: str) -> bool:
        """校验步骤结果是否符合预期"""
        logger.info(f"校验步骤结果: step={step['step']}, 问题: {step['question']}")
        messages = [
            {
                "role": "system",
                "content": "判断工具执行结果是否回答了问题。如果回答了问题，返回 true；否则返回 false。",
            },
            {"role": "user", "content": f"问题: {step['question']}\n结果: {result}"},
        ]
        response = await call_chat_mdl(messages, self.deepseek_api_url)
        logger.info(f"**检查步骤结果**: {step['question']}: \n{response}\n")
        # 去掉思考过程
        response = filter_think_tags(response)
        match = re.search(r"(true|false)", response.lower())
        is_valid = False
        if match:
            # 如果匹配到true则为结果有效
            if match.group(1) == "true":
                is_valid = True
        logger.info(f"校验结果: {'有效' if is_valid else '无效'}")
        return is_valid

    async def _optimize_question(self, step: Dict[str, Any], result: str, step_result_list: list) -> str:
        """优化问题（新增context参数）"""
        logger.info(f"优化问题: step={step['step']}, question={step['question']}")
        prev_step_question = step_result_list[-1]["question"] if step_result_list else ""
        prev_step_result = step_result_list[-1]["result"] if step_result_list else ""
        prev_step_info = f"前一个问题: {prev_step_question}\n前一个的回复: {prev_step_result}"
        messages = [
            {
                "role": "system",
                "content": "根据之前的问题和工具返回的结果，生成一个优化后的问题，以便更准确地获取所需信息。只返回优化后的问题。",
            },
            {
                "role": "user",
                "content": f"原始问题: {step['question']}\n工具返回的结果: {result}\n该问题的上下文是: {prev_step_info}",
            },
        ]
        optimized_question = await call_chat_mdl(messages, self.deepseek_api_url)
        optimized_question = filter_think_tags(optimized_question)

        logger.info(f"优化后的问题: {optimized_question}")
        return optimized_question

    def _get_component_by_step(self, step: dict) -> ComponentBase:
        """获取对应的组件对象"""
        # TODO: 可以改成工厂函数调用(根据step中的component_name直接实例化类)
        function_name = step["call_function"]
        if function_name == KnowledgeBase.function_call:
            component = KnowledgeBase(
                self.flow_namespace,
                knowledge_base_url=self.knowledge_base_url,
                knowledge_base_token=self.knowledge_base_token,
            )
        elif function_name == ExeSql.function_call:
            component = ExeSql(self.flow_namespace)
        elif function_name == AiSearch.function_call:
            component = AiSearch(self.flow_namespace)
        elif function_name == DataAnalyze.function_call:
            component = DataAnalyze(self.flow_namespace)
        elif function_name == ExeCode.function_call:
            component = ExeCode(self.flow_namespace)
        elif function_name == CommonChat.function_call:
            component = CommonChat(self.flow_namespace)
        else:
            function_calls_mcp_mapping = get_ai_mcp_mapping()
            mcp_tool = function_calls_mcp_mapping.get(function_name, None)
            if mcp_tool is None:
                logger.info(f"Not found mcp tool: {function_name}")
                component = CommonChat(self.flow_namespace)
            else:
                # TODO: 优化
                url = get_mcp_server_url_from_db(mcp_tool.mcp_id)
                if url is None:
                    logger.info(f"Not found mcp tool: {function_name}")
                    component = CommonChat(self.flow_namespace)
                else:
                    arguments = step.get("arguments", {})
                    tool_info = {"parameters": arguments, "tool": {"name": step["call_function"]}}
                    component = McpToolWrapper(self.flow_namespace, self.mcp_service, tool_info, url)
        return component

    def _save_node_retry(self, component: ComponentBase, step: dict, retry_count: int = 0):
        payload = {
            "input": "",
            "output": "",
            "output_files": [],
            "prompt_used_tokens": component.prompt_used_tokens,
            "history_used_tokens": component.history_used_tokens,
            "answer_used_tokens": component.answer_used_tokens,
            "total_used_tokens": component.prompt_used_tokens
            + component.history_used_tokens
            + component.answer_used_tokens,
            "current_date": now_date_str(),
            "component_name": step["component_name"],
            "component_id": step["component_id"],
            "call_function": step["call_function"],
            "step": step["step"],
            "step_question": step["question"],
            "retry_count": retry_count,
        }
        self.flow_namespace.add_input_and_output(payload)
        return {"event": "input_and_output", "payload": payload}

    def _save_node_msg_stat(self, components: list[ComponentBase]):
        use_ts = 0
        prompt_used_tokens = 0
        history_used_tokens = 0
        answer_used_tokens = 0
        total_used_tokens = 0
        for cpn in components:
            use_ts += cpn.use_ts
            prompt_used_tokens += cpn.prompt_used_tokens
            history_used_tokens += cpn.history_used_tokens
            answer_used_tokens += cpn.answer_used_tokens
            total_used_tokens += cpn.prompt_used_tokens + cpn.history_used_tokens + cpn.answer_used_tokens
        payload = {
            "use_ts": use_ts,
            "prompt_used_tokens": prompt_used_tokens,
            "history_used_tokens": history_used_tokens,
            "answer_used_tokens": answer_used_tokens,
            "total_used_tokens": total_used_tokens,
            "current_date": now_date_str(),
        }
        self.flow_namespace.add_node_stat_msg(payload)
        return {"event": "node_msg_stat", "payload": payload}

    def _build_last_context(self, question: str, result: str) -> str:
        return f"{question} {result}" if question and result else ""


def get_step_chain_runner_config():
    return {
        "deepseek_api_url": "http://***************:20000/v1/chat/completions",
        "knowledge_base_url": "http://**************:81/api/v1/chats_openai/65951946482011f097eb0242ac130006/chat/completions",
        "knowledge_base_token": "ragflow-k0MTA0MDIyNDY3MDExZjA4N2I3MDI0Mm",
        "db_config": {
            "host": "***************",
            "port": 3306,
            "user": "root",
            "passwd": "infini_rag_flow",
            "dbname": "agent_test",
        },
    }


# 示例：处理用户请求
if __name__ == "__main__":
    from app.services.flow.step_chain_builder import StepChainBuilder

    # step_chain_builder = StepChainBuilder()
    # step_chain = step_chain_builder.create_step_chain(user_question)
    # 配置信息
    config = get_step_chain_runner_config()
    # 创建工具执行器实例
    user_question = "生成一个月度汇报的ppt"
    step_chain = [
        {
            "step": "1",
            "question": "1+1等于几",
            "call_function": "数据分析",
            "component_name": "data_analyze",
            "component_id": "data_analyze:xxxx1",
            "arguments": {},
        },
        {
            "step": "2",
            "question": "如何将数据可视化并优化PPT的排版与动画效果？",
            "call_function": "数据分析",
            "component_name": "data_analyze",
            "component_id": "data_analyze:xxxx2",
            "arguments": {},
        },
    ]
    executor = StepChainRunner(**config, flow_namespace=FlowNamespace(step_chain=step_chain, question=user_question))

    def handle_msg():
        res = yield from executor.process_task()
        logger.info(f"Handle msg: res: {res}")

    print(f"用户问题: {user_question}")
    for m in handle_msg():
        logger.info(f"返回的流数据: data: {m}")
