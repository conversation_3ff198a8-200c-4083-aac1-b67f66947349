#读机器码等信息的接口

import json
import traceback

from fastapi import APIRouter, Depends
from app.api.response import APIResponse, response_formatter
from app.config.config import settings
import requests
from app.const.response import MessageCode
from datetime import datetime,timedelta
from prometheus_api_client import PrometheusConnect
from prometheus_api_client.utils import parse_datetime
import redis
from Crypto.Cipher import ChaCha20
from app.utils.license import YuanFangLicense

router = APIRouter()


class ServerAPI:
    @staticmethod
    @router.get("/lic")
    async def server_lic():

        """
        读取主机 license 信息

        输出字段:

            - userid: 用户编号，识别用户
            - machine_id: license授权机器码，具有这个机器码的机器可用
            - expires: 过期时间
            - features: 逗号分隔的授权功能列表，暂未使用

        """

        data = {
            "userid": "123A-32",
            "machine_id": "8d2a2b766c4129cf9904b126cd21f963" ,
            "expires": "2025-12-31 00:00:00",
            "features": "web,rag,chat"
        }
        
        license_data = YuanFangLicense.get_license()
        if license_data == "":
            return APIResponse(content={},status=MessageCode.UNKNOWERROR)
        
        return APIResponse({"data":license_data})
